package com.car.autolink.base;

import com.elvishew.xlog.XLog;

/**
 * <AUTHOR>
 * @date 2019/12/6
 */
public abstract class BaseAudio {
    private static final String TAG = "BaseAudio";
    protected Callback mCallback;

    /**
     * shutdown audio module.
     */
    public void shutdown() {
        XLog.tag(TAG).d("Shutting down...");
        if (mCallback != null) {
            mCallback = null;
        }
        onShutdown();
        XLog.tag(TAG).d("Shutdown completed.");
    }

    /**
     * start audio module.
     *
     * @param callback {@link Callback}
     */
    public void start(Callback callback, String address) {
        XLog.tag(TAG).d("Starting...");
        mCallback = callback;
        onStart(address);
        XLog.tag(TAG).d("Start sequence finished.");
    }

    /**
     * stop audio module.
     */
    public void stop() {
        XLog.tag(TAG).d("Stopping...");
        onStop();
        XLog.tag(TAG).d("Stop finished.");
    }

    protected abstract void onStart(String address);

    protected abstract void onStop();

    protected abstract void onShutdown();

    public abstract String getAddress();

    public abstract void setAudioConfig(String address, boolean alreadyPaired);

    public abstract void connectHid();

    public abstract void disconnectHid();

    public interface Callback {

        /**
         * request bluetooth permission
         */
        void requestBtPermission();

        /**
         * pairing request
         *
         * @param address bluetooth address
         */
        void onParingRequest(String address);

        String requestPlatformInfo();
    }
}
