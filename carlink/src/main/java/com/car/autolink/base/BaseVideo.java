package com.car.autolink.base;

import android.content.Intent;
import android.view.Display;

import com.car.autolink.config.PhoneVideoConfig;
import com.car.autolink.config.VideoConfig;
import com.car.autolink.config.VideoPackage;
import com.elvishew.xlog.XLog;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public abstract class BaseVideo {
    private static final String TAG = "BaseVideo";

    public interface Callback {
        /**
         * render frame
         *
         * @param pkg VideoPackage
         */
        void onRenderFrame(VideoPackage pkg);

        void onPresentationDisplayCreated(Display display);
    }

    protected Callback mCallback;

    /**
     * shutdown render module.
     */
    public void shutdown() {
        XLog.tag(TAG).d("Shutting down...");
        if (mCallback != null) {
            mCallback = null;
        }
        onShutdown();
        XLog.tag(TAG).d("Shutdown completed.");
    }

    /**
     * start render module.
     *
     * @param callback {@link Callback}
     */
    public void start(Callback callback) {
        XLog.tag(TAG).d("Starting...");
        mCallback = callback;
        onStart();
        XLog.tag(TAG).d("Start sequence finished.");
    }

    /**
     * stop render module.
     */
    public void stop() {
        XLog.tag(TAG).d("Stopping...");
        onStop();
        XLog.tag(TAG).d("Stop finished.");
    }

    /**
     * start video module
     */
    protected abstract void onStart();

    /**
     * stop video module
     */
    protected abstract void onStop();

    /**
     * shutdown video module
     */
    protected abstract void onShutdown();

    /**
     * setVideoConfig
     *
     * @param config config
     */
    public abstract void setVideoConfig(VideoConfig config);

    /**
     * setPermissionResult
     *
     * @param resultCode  resultCode
     * @param intent      intent
     * @return true or false
     */
    public abstract boolean setPermissionResult(int resultCode, Intent intent, boolean isSecondary);

    /**
     * requestPermission
     *
     * @return Intent
     */
    public abstract Intent requestPermission();

    public abstract void setProjectionMode(int mode);
}
