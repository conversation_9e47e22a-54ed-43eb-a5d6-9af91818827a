package com.car.autolink;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Build;
import android.provider.Settings;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.OrientationEventListener;
import android.view.WindowManager;

import com.car.autolink.base.BaseAudio;
import com.car.autolink.base.BaseVideo;
import com.car.autolink.config.AlConfig;
import com.car.autolink.config.VideoPackage;
import com.car.autolink.events.ALBluetoothPairingEvent;
import com.car.autolink.events.BluetoothPermissionEvent;
import com.car.autolink.events.DisconnectedEvent;
import com.car.autolink.events.OpenBtEvent;
import com.car.autolink.events.PresentationDisplayCreatedEvent;
import com.car.autolink.events.ProjectionPermissionEvent;
import com.car.autolink.events.ProjectionRequestEvent;
import com.car.autolink.events.ProjectionStatusEvent;
import com.car.autolink.events.ScreenResolutionEvent;
import com.car.autolink.events.StartEncodeEvent;
import com.car.autolink.events.StopEncodeEvent;
import com.car.autolink.events.VideoConfigEvent;
import com.car.autolink.events.VideoFocusEvent;
import com.car.autolink.module.protocal.BaseProtocol;
import com.car.autolink.module.protocal.eightthree.EightThreeProtocol;
import com.car.autolink.module.transport.DataConnection;
import com.car.autolink.module.transport.ITransport;
import com.car.autolink.module.transport.wifi.TcpTransport;
import com.car.autolink.utils.Platform;
import com.elvishew.xlog.XLog;
import com.link.car.BuildConfig;
import com.link.car.R;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2017/10/03
 * @desc Autolink Control for everything.
 */
public class AutolinkControl {
    private static final String TAG = "AutolinkControl";
    private static final String POINTER_SPEED = "pointer_speed";
    private final Context mContext;
    private final long instanceId;
    private static volatile long sInstanceCounter = 0;
    private BaseProtocol mProtocol = null;
    public BaseAudio mAudio;
    public BaseVideo mVideo;
    private int mIsLandscape = 0;
    private int mScreenWidth = 0;
    private int mScreenHeight = 0;
    private String mPhoneName = null;
    private ScheduledExecutorService mTimerService;
    private ScheduledFuture<?> mScheduledFuture;
    private ITransport mTransport;
    private OrientationEventListener mOrientationEventListener;
    private int mLastRotation;
    private File mFile;
    private FileOutputStream mOutputStream;

    /**
     * AutolinkControl constructor.
     *
     * @param context context from service
     */
    private AutolinkControl(Context context) {
        instanceId = ++sInstanceCounter;
        XLog.tag(TAG).d("创建 AutolinkControl 实例 #" + instanceId);
        EventBus.getDefault().register(this);
        mContext = context;
        mTimerService = new ScheduledThreadPoolExecutor(2);
        mOrientationEventListener = new OrientationEventListener(mContext.getApplicationContext()) {
            @Override
            public void onOrientationChanged(int orientation) {
                int rotation = getRotation();
                if (rotation != mLastRotation) {
                    sendOrientation(mContext.getResources().getConfiguration().orientation,
                            rotation);
                    mLastRotation = rotation;
                }
            }
        };
        if (mOrientationEventListener.canDetectOrientation()) {
            mOrientationEventListener.enable();
        }
        updateAutolinkConfig();
        if (BuildConfig.DEBUG) {
            mFile = new File(mContext.getExternalFilesDir(null), "test.H264");
            try {
                mOutputStream = new FileOutputStream(mFile);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
        }
    }

    private void dumpVideoData(byte[] data) {
        if (BuildConfig.DEBUG) {
            try {
                mOutputStream.write(data);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void onConfigurationChanged(Configuration newConfig) {
        DisplayMetrics outMetrics = new DisplayMetrics();
        WindowManager windowManager =
                (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        windowManager.getDefaultDisplay().getRealMetrics(outMetrics);
        int screenWidth;
        int screenHeight;
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            screenWidth = outMetrics.heightPixels;
            screenHeight = outMetrics.widthPixels;
        } else {
            screenWidth = outMetrics.widthPixels;
            screenHeight = outMetrics.heightPixels;
        }
        sendOrientation(mContext.getResources().getConfiguration().orientation, getRotation());
        updateScreenSolution(screenWidth, screenHeight);
    }


    /**
     * updateScreenSolution.
     *
     * @param screenWidth  screenWidth
     * @param screenHeight screenHeight
     */
    private synchronized void updateScreenSolution(int screenWidth, int screenHeight) {
        mScreenWidth = screenWidth;
        mScreenHeight = screenHeight;
        sendResolutionNotification(screenWidth, screenHeight, true);
    }


    /**
     * setEncoderState.
     */
    public void setEncoderState(int focus, int reason) {
        if (mProtocol != null) {
            mProtocol.setEncoderState(focus, reason);
        }
    }

    private void sendOrientation(int isLandscape, int rotation) {
        if (mProtocol != null
                && (isLandscape != mIsLandscape || mLastRotation != rotation)
        ) {
            mProtocol.sendOrientation(isLandscape, rotation);

        }
        mIsLandscape = isLandscape;
        mLastRotation = rotation;
    }


    /**
     * onActivityResult.
     *
     * @param resultCode resultCode
     * @param intent     intent
     */
    public void onActivityResult(int resultCode, Intent intent, boolean isSecondary) {
        if (setPermissionResult(resultCode, intent, isSecondary)) {
            if (isSecondary) {
                return;
            }
            if (mProtocol != null) {
                mProtocol.sendPermission(true);
            }
        } else {
            if (mProtocol != null) {
                mProtocol.sendByeByeRequest(1);
            }
        }
    }

    /**
     * sendByeByeRequest to car.
     */
    public void sendByeByeRequest() {
        if (mProtocol != null) {
            mProtocol.sendByeByeRequest(1);
        }
    }

    private void updateAutolinkConfig() {
        int speed = -1;
        WindowManager windowManager =
                (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getRealMetrics(outMetrics);
        int orientation = mContext.getResources().getConfiguration().orientation;
        mIsLandscape = orientation;
        mLastRotation = getRotation();
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            mScreenWidth = outMetrics.heightPixels;
            mScreenHeight = outMetrics.widthPixels;
        } else {
            mScreenWidth = outMetrics.widthPixels;
            mScreenHeight = outMetrics.heightPixels;
        }
        mPhoneName = Build.MODEL;
        try {
            speed = Settings.System.getInt(mContext.getContentResolver(), POINTER_SPEED);
        } catch (Settings.SettingNotFoundException e) {
            e.printStackTrace();
        }

        if (speed != 0) {
            if (Settings.System.canWrite(mContext.getApplicationContext())) {
                try {
                    Settings.System.putInt(mContext.getContentResolver(), POINTER_SPEED, 0);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void sendResolutionNotification(int width, int height, boolean isRequired) {
        if (mProtocol != null) {
            mProtocol.sendResolutionNotification(width, height, isRequired);
        }
    }

    /**
     * set carId from server.
     *
     * @param id carId
     */
    public void setCarId(String id) {
        if (mProtocol != null) {
            mProtocol.setCarId(id);
        }
    }

    /**
     * connect to car.
     *
     * @param transport usb or wifi
     */
    public void startConnect(ITransport transport) {
        mTransport = transport;
        startGal();
    }

    /**
     * destroy all.
     */
    public void release() {
        XLog.tag(TAG).d("释放 AutolinkControl 实例 #" + instanceId);
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
            XLog.tag(TAG).d("实例 #" + instanceId + " EventBus 取消注册完成");
        }

        if (mTransport != null) {
            mTransport.stopTransport();
            mTransport = null;
        }

        if (mAudio != null) {
            mAudio.shutdown();
            mAudio = null;
        }

        if (mVideo != null) {
            mVideo.shutdown();
            mVideo = null;
        }

        if (mProtocol != null) {
            mProtocol.shutdown();
            mProtocol = null;
        }

        if (mTimerService != null) {
            mTimerService.shutdownNow();
            mTimerService = null;
        }

        if (mOrientationEventListener != null) {
            mOrientationEventListener.disable();
            mOrientationEventListener = null;
        }

        XLog.tag(TAG).d("实例 #" + instanceId + " 释放完成");
    }

    private boolean setPermissionResult(int resultCode, Intent intent, boolean isSecondary) {
        return mVideo.setPermissionResult(resultCode, intent, isSecondary);
    }

    public void requestPresentation() {
        mVideo.setProjectionMode(1);
    }

    public void requestProjection() {
        mVideo.setProjectionMode(0);
    }

    private static final class CheckConnectTask extends TimerTask {
        @Override
        public void run() {
            try {
                XLog.tag(TAG).e("protocol connection timeout");
                EventBus.getDefault().post(new DisconnectedEvent());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private AlConfig createAlConfig() {
        AlConfig config = new AlConfig();
        config.setScreenWidth(mScreenWidth);
        config.setScreenHeight(mScreenHeight);
        config.setPhoneName(mPhoneName);
        config.setVersion(Platform.getAppVersion(mContext));
        config.setAppName(mContext.getString(R.string.config_name));
        config.setIsLandscape(mIsLandscape);
        config.setRotation(mLastRotation);
        if (mTransport.getType() == DataConnection.WIFI_P2P) {
            config.setRemoteHost(((TcpTransport) mTransport).getRemoteSocketAddress());
        }
        XLog.tag(TAG).i("Phone name:" + config.getPhoneName());
        return config;
    }

    private void startGal() {
        mProtocol = new EightThreeProtocol();
        mProtocol.start(new BaseProtocol.Callback() {

            @Override
            public int requestRead(byte[] buf, int offset, int len) throws IOException {
                return mTransport.read(buf, offset, len);
            }

            @Override
            public void requestWrite(byte[] buf, int offset, int len) throws IOException {
                mTransport.write(buf, offset, len);
            }

            @Override
            public void stopTransport() {
                mTransport.stopTransport();
            }

        }, createAlConfig());
        mScheduledFuture = mTimerService.schedule(new CheckConnectTask(), 30, TimeUnit.SECONDS);
    }

    @Subscribe
    public void onStartEncodeEvent(StartEncodeEvent event) {
        XLog.tag(TAG).d("onStartEncodeEvent");
        mVideo.start(new BaseVideo.Callback() {
            @Override
            public void onRenderFrame(VideoPackage pkg) {
                dumpVideoData(pkg.getData());
                mProtocol.sendFrame(pkg);
            }

            @Override
            public void onPresentationDisplayCreated(Display display) {
                EventBus.getDefault().post(new PresentationDisplayCreatedEvent(display));
            }
        });
    }

    @Subscribe
    public void onStopEncodeEvent(StopEncodeEvent event) {
        XLog.tag(TAG).d("onStopEncodeEvent");
        mVideo.stop();
    }

    @Subscribe
    public void onVideoConfigEvent(VideoConfigEvent event) {
        XLog.tag(TAG).d("onVideoConfigEvent");
        mVideo.setVideoConfig(event.getVideoConfig());
    }

    @Subscribe()
    public void onVideoFocusEvent(VideoFocusEvent event) {
        XLog.tag(TAG).d("onVideoFocusEvent");
        if (mScheduledFuture != null) {
            mScheduledFuture.cancel(true);
        }
    }

    @Subscribe
    public void onScreenResolutionEvent(ScreenResolutionEvent event) {
        XLog.tag(TAG).d("onScreenResolutionEvent");
        sendResolutionNotification(mScreenWidth, mScreenHeight, false);
    }


    @Subscribe
    public void onALBluetoothPairingEvent(ALBluetoothPairingEvent event) {
        XLog.tag(TAG).d("onALBluetoothPairingEvent");
        mAudio.setAudioConfig(event.getAddress(), event.getAlreadyPaired());
    }

    @Subscribe
    public void onOpenBtEvent(OpenBtEvent event) {
        XLog.tag(TAG).d("onOpenBtEvent");
        mAudio.start(new BaseAudio.Callback() {

            @Override
            public void requestBtPermission() {
                XLog.tag(TAG).d("requestBtPermission: ");
                EventBus.getDefault().post(new BluetoothPermissionEvent());
            }

            @Override
            public void onParingRequest(String address) {
                if (mProtocol != null) {
                    mProtocol.sendParingRequest(address);
                }
            }

            @Override
            public String requestPlatformInfo() {
                if (mProtocol != null) {
                    return mProtocol.getHuMake();
                }
                return null;
            }
        }, event.getAddress());
    }

    @Subscribe
    public void onProjectionRequestEvent(ProjectionRequestEvent event) {
        XLog.tag(TAG).d("onProjectionRequestEvent 实例 #" + instanceId + " 收到事件");

        // 检查实例是否已经被释放
        if (mVideo == null) {
            XLog.tag(TAG).w("实例 #" + instanceId + " 已释放，忽略 ProjectionRequestEvent");
            return;
        }

        XLog.tag(TAG).d("实例 #" + instanceId + " 处理 ProjectionRequestEvent");
        Intent intent = mVideo.requestPermission();
        EventBus.getDefault().post(new ProjectionPermissionEvent(intent, false));
    }


    @Subscribe
    public void onProjectionStatusEvent(ProjectionStatusEvent event) {
        XLog.tag(TAG).d("onProjectionStatusEvent");
        if (event.currentState == ProjectionStatusEvent.State.RUNNING) {
            if (mAudio != null) {
                if (event.currentMode == 0) {
                    mAudio.connectHid();
                } else {
                    mAudio.disconnectHid();
                }
            }
        }
    }

    private int getRotation() {
        return ((WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay()
                .getRotation();
    }

    public static class Builder {
        private final AutolinkControl mControl;

        public Builder(Context context) {
            mControl = new AutolinkControl(context);
        }

        public Builder setAudio(BaseAudio audio) {
            mControl.mAudio = audio;
            return this;
        }

        public Builder setVideo(BaseVideo video) {
            mControl.mVideo = video;
            return this;
        }


        /**
         * build AutolinkControl.
         *
         * @return AutolinkControl instance
         */
        public AutolinkControl build() {
            if (mControl.mVideo == null) {
                throw new RuntimeException("(encoder and render) or (video),update are necessary!");
            }
            return mControl;
        }
    }
}
