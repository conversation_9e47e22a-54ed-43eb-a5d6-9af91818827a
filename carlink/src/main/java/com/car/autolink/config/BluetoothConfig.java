package com.car.autolink.config;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2019/12/6
 */
public class BluetoothConfig implements Parcelable {
    public String mAddress;
    public boolean isSupportPin;
    public boolean isSupportNumeric;

    BluetoothConfig() {
        mAddress = "SKIP_THIS_BLUETOOTH";
        isSupportNumeric = false;
        isSupportPin = false;
    }

    public static final Parcelable.Creator<BluetoothConfig> CREATOR =
            new Creator<BluetoothConfig>() {

        @Override
        public BluetoothConfig createFromParcel(Parcel arg0) {
            BluetoothConfig config = new BluetoothConfig();
            config.mAddress = arg0.readString();
            config.isSupportPin = arg0.readByte() != 0;
            config.isSupportNumeric = arg0.readByte() != 0;
            return config;
        }

        @Override
        public BluetoothConfig[] newArray(int arg0) {
            return new BluetoothConfig[arg0];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel arg0, int arg1) {
        arg0.writeString(mAddress);
        arg0.writeByte((byte) (isSupportPin ? 1 : 0));
        arg0.writeByte((byte) (isSupportNumeric ? 1 : 0));
    }
}
