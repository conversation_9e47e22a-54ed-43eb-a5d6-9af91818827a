package com.car.autolink.config;

import android.os.Parcel;
import android.os.Parcelable;


/**
 * <AUTHOR>
 * @date 2019/12/6
 */
public class AlConfig implements Parcelable {
    public AlConfig() {
        this.bluetoothConfig = new BluetoothConfig();
    }

    private String phoneName;
    private String version;
    private String appName;
    private String remoteHost;
    private int screenWidth;
    private int screenHeight;
    private int isLandscape;
    private int rotation;


    public BluetoothConfig getBluetoothConfig() {
        return bluetoothConfig;
    }

    public void setBluetoothConfig(BluetoothConfig bluetoothConfig) {
        this.bluetoothConfig = bluetoothConfig;
    }

    private BluetoothConfig bluetoothConfig;

    public String getPhoneName() {
        return phoneName;
    }

    public void setPhoneName(String phoneName) {
        this.phoneName = phoneName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getRemoteHost() {
        return remoteHost;
    }

    public void setRemoteHost(String remoteHost) {
        this.remoteHost = remoteHost;
    }

    public int getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(int screenWidth) {
        this.screenWidth = screenWidth;
    }

    public int getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(int screenHeight) {
        this.screenHeight = screenHeight;
    }

    public int getIsLandscape() {
        return isLandscape;
    }

    public void setIsLandscape(int isLandscape) {
        this.isLandscape = isLandscape;
    }

    public int getRotation() {
        return rotation;
    }

    public void setRotation(int rotation) {
        this.rotation = rotation;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    private AlConfig(Parcel in) {
        this.phoneName = in.readString();
        this.version = in.readString();
        this.appName = in.readString();
        this.remoteHost = in.readString();
        this.screenWidth = in.readInt();
        this.screenHeight = in.readInt();
        this.isLandscape = in.readInt();
        this.rotation = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.phoneName);
        dest.writeString(this.version);
        dest.writeString(this.appName);
        dest.writeString(this.remoteHost);
        dest.writeInt(this.screenWidth);
        dest.writeInt(this.screenHeight);
        dest.writeInt(this.isLandscape);
        dest.writeInt(this.rotation);
    }

    public static final Creator<AlConfig> CREATOR = new Creator<AlConfig>() {
        @Override
        public AlConfig createFromParcel(Parcel source) {
            return new AlConfig(source);
        }

        @Override
        public AlConfig[] newArray(int size) {
            return new AlConfig[size];
        }
    };
}
