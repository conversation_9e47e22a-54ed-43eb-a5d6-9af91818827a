package com.car.autolink.events;

import android.content.Intent;

public class ProjectionPermissionEvent {

    private final Intent intent;
    private final boolean isSecondary;

    public ProjectionPermissionEvent(Intent intent, boolean isSecondary) {
        this.intent = intent;
        this.isSecondary = isSecondary;
    }

    public Intent getIntent() {
        return intent;
    }

    public boolean isSecondary() {
        return isSecondary;
    }
}
