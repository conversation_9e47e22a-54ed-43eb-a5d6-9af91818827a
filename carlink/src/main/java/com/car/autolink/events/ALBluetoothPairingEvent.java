package com.car.autolink.events;

public class ALBluetoothPairingEvent {
    private final String address;
    private final boolean alreadyPaired;

    public ALBluetoothPairingEvent(String address, boolean alreadyPaired) {
        this.address = address;
        this.alreadyPaired = alreadyPaired;
    }

    public String getAddress() {
        return address;
    }

    public boolean getAlreadyPaired() {
        return alreadyPaired;
    }
}
