package com.car.autolink.events;


public class ProjectionStatusEvent {
    public enum State {
        IDLE,           // 空闲/已停止
        STARTING,       // 正在启动
        RUNNING,        // 正在运行/推流中
        STOPPING,       // 正在停止
        WAITING_FOR_PERMISSION, // 等待录屏权限
        ERROR           // 发生错误
    }

    public final State currentState;
    public final int currentMode;

    public ProjectionStatusEvent(State state, int mode) {
        this.currentState = state;
        this.currentMode = mode;
    }

    @Override
    public String toString() {
        return "ProjectionStatusEvent{" + "currentState=" + currentState + ", currentMode=" + currentMode + '}';
    }
}
