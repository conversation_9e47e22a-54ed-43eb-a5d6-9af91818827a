package com.car.autolink.events;


public class WlTouchEvent {
    private final int x;
    private final int y;
    private final int action;
    private final long timestamp;

    public WlTouchEvent(int x, int y, int action, long timestamp) {
        this.x = x;
        this.y = y;
        this.action = action;
        this.timestamp = timestamp;
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public int getAction() {
        return action;
    }

    public long getTimestamp() {
        return timestamp;
    }
}
