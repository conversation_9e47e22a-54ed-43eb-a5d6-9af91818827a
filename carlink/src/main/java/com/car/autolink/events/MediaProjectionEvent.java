package com.car.autolink.events;

import android.content.Intent;

import com.car.autolink.utils.Platform;

public class MediaProjectionEvent {
    private final Intent intent;
    private final int resultCode;
    private final boolean isSecondary;

    public MediaProjectionEvent(Intent intent, int resultCode, boolean isSecondary) {
        this.intent = intent;
        this.resultCode = resultCode;
        this.isSecondary = isSecondary;
    }

    public Intent getIntent() {
        return intent;
    }

    public int getResultCode() {
        return resultCode;
    }

    public boolean isSecondary() {
        return isSecondary;
    }

}
