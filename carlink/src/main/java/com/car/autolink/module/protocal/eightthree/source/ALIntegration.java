package com.car.autolink.module.protocal.eightthree.source;

import android.annotation.SuppressLint;
import android.os.Build;
import android.util.Log;

import com.car.autolink.config.AlConfig;
import com.car.autolink.config.VideoPackage;
import com.car.autolink.events.ALBluetoothPairingEvent;
import com.car.autolink.events.AutoRotationEvent;
import com.car.autolink.events.ByeByeRequestEvent;
import com.car.autolink.events.ByeByeResponseEvent;
import com.car.autolink.events.DisconnectedEvent;
import com.car.autolink.events.LandscapeEvent;
import com.car.autolink.events.OpenBtEvent;
import com.car.autolink.events.ProjectionRequestEvent;
import com.car.autolink.events.ScreenResolutionEvent;
import com.car.autolink.events.UnrecoverableErrorEvent;
import com.car.autolink.events.VideoFocusEvent;
import com.car.autolink.module.protocal.BaseProtocol;
import com.car.autolink.module.protocal.eightthree.EightThreeProtocol;
import com.car.autolink.module.protocal.eightthree.eventcenter.ALEventCenter;
import com.car.autolink.module.protocal.eightthree.eventcenter.IALEventCenter;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALBluetoothPairingRequest;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReadParamEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportBluetoothChannel;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportBluetoothPairingRequest;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportForceLandscapeRequest;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportRunningStatusChange;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportUpLoadCarInfo;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportUpdateCarId;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportVideoFocusNotification;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALScreenOrientationEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALScreenResolutionEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALVideoFocusRequestEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALVideoPermissionEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalCommonEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;
import com.car.autolink.module.protocal.eightthree.project.GalReceiver;
import com.car.autolink.module.protocal.eightthree.project.GalReceiver.AppMessageListener;
import com.car.autolink.module.protocal.eightthree.project.GalReceiver.PhoneInfo;
import com.car.autolink.utils.Platform;
import com.elvishew.xlog.XLog;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALIntegration {

    private static final String TAG = ALIntegration.class.getSimpleName();
    private GalReceiver mGalReceiver;
    private ALBluetoothEndpoint mBluetooth;
    private ALVideoSource mVideoSource;
    private ALVendorExtension mVendorExtension;
    private ALInputSink mAlInputSink;
    private int mIsLand;
    private int mRotation;
    private ITransportChannel mTransportChannel;
    private IALEventCenter mEventCenter;
    private final Object mLock = new Object();
    private boolean isExited = true;
    private String mCarId = "None";
    @SuppressLint("unused")
    private final long[] mCHipAddresses = null;
    @SuppressLint("unused")
    private final int mAddress = 0x80000000;

    /**
     * init ALIntegration.
     *
     * @param config       {@link AlConfig}
     * @param player       {@link IVideoPlayerBase}
     * @param transport    {@link ITransportChannel}
     * @param autoStart    autoStart
     */
    public void init(AlConfig config,
                     IVideoPlayerBase player,
                     ITransportChannel transport,
                     boolean autoStart) {

        mIsLand = config.getIsLandscape();
        mRotation = Platform.getDisplayRotation(config.getRotation());
        mTransportChannel = transport;
        mEventCenter = new ALEventCenter();

        mEventCenter.registerListener(
                new IALEventCenter.Listener() {
                    @Override
                    public void process(InternalEvent event) {
                        switch (event.getConsignee()) {
                            case ALServiceBase.AL_SERVICE_ROOT:
                                internalEventProcess(event);
                                break;
                            case ALServiceBase.AL_SERVICE_ALL:
                            case ALServiceBase.AL_SERVICE_VIDEO_SOURCE:
                                mVideoSource.internalEvent(event);
                                break;
                            case ALServiceBase.AL_SERVICE_BLUETOOTH:
                                mBluetooth.internalEvent(event);
                                break;
                            default:
                                break;
                        }
                    }

                    @Override
                    public void process(ALEvent event) {
                        switch (event.getEventType()) {
                            case ALEvent.VIDEO_FOCUS_REQUEST:
                            case ALEvent.VIDEO_PERMISSION:
                                synchronized (mLock) {
                                    if (!isExited) {
                                        mVideoSource.externalEvent(event);
                                    }
                                }
                                break;
                            case ALEvent.SCREEN_ORIENTATION:
                            case ALEvent.SCREEN_RESOLUTION:
                            case ALEvent.READ_PARAM:
                            case ALEvent.WRITE_PARAM:
                                synchronized (mLock) {
                                    if (!isExited) {
                                        externalEventProcess(event);
                                    }
                                }
                                break;
                            case ALEvent.BLUETOOTH_PAIRING_REQUEST:
                                synchronized (mLock) {
                                    if (!isExited) {
                                        mBluetooth.externalEvent(event);
                                    }
                                }
                                break;
                            default:
                                break;
                        }
                    }

                    @Override
                    public void process(ALReportEvent event) {
                        reportEventProcess(event);
                    }
                });

        mBluetooth = new ALBluetoothEndpoint(config.getBluetoothConfig(), mEventCenter);
        mVideoSource =
                new ALVideoSource(player, mEventCenter, autoStart,
                        config.getRemoteHost());
        mVendorExtension = new ALVendorExtension(mEventCenter);
        mAlInputSink = new ALInputSink();

        PhoneInfo phoneInfo =
                new PhoneInfo(Build.MANUFACTURER + config.getAppName(), Build.MODEL,
                        config.getVersion(), config.getPhoneName(), config.getScreenWidth(),
                        config.getScreenHeight());
        mGalReceiver = new GalReceiver(phoneInfo, mEventCenter, mAppMessageListener);
    }

    /**
     * start gal and eventCenter.
     */
    public void start() {
        mEventCenter.start();
        ALReportEvent reportEvent =
                new ALReportRunningStatusChange(ALReportRunningStatusChange.START);
        mEventCenter.sendEventNonBlock(reportEvent);
        mGalReceiver.startTransport(mTransportChannel);
    }

    public void stop() {
        mGalReceiver.stop();
    }


    public String getHuMake() {
        return mGalReceiver.getHuMake();
    }

    /**
     * destroy everything.
     */
    public void destroy() {
        Log.d(TAG, "destroy");
        mEventCenter.shutdown();
        mEventCenter.release();
        mGalReceiver.destroy();
        mGalReceiver = null;
        mEventCenter = null;
        mBluetooth = null;
        mVideoSource = null;
        mVendorExtension = null;
        mAlInputSink = null;
        mAppMessageListener = null;
        mTransportChannel = null;
    }

    public void sendPermission(boolean ctrl) {
        ALEvent event = new ALVideoPermissionEvent(ctrl);
        mEventCenter.sendEvent(event);
    }

    /**
     * send orientation to car .
     *
     * @param isLand orientation
     */
    public void sendOrientation(int isLand, int rotation) {
        mIsLand = isLand;
        mRotation = Platform.getDisplayRotation(rotation);
        ALEvent event = new ALScreenOrientationEvent(mIsLand, mRotation);
        mEventCenter.sendEvent(event);
    }

    public void sendResolutionNotification(int width, int height, boolean isRequired) {
        ALEvent event = new ALScreenResolutionEvent(width, height, isRequired);
        mEventCenter.sendEventNonBlock(event);
    }

    public void sendEncoderState(int focus, int reason) {
        ALEvent event = new ALVideoFocusRequestEvent(focus, reason);
        mEventCenter.sendEvent(event);
    }

    public void sendParingRequest(String address) {
        ALEvent event = new ALBluetoothPairingRequest(address);
        mEventCenter.sendEventNonBlock(event);
    }

    /**
     * send video frame to car.
     *
     * @param pkg frame data.
     */
    public void sendFrame(VideoPackage pkg) {
        synchronized (mLock) {
            if (!isExited) {
                mVideoSource.sendData(pkg);
            }
        }
    }

    /**
     * send bye to car.
     */
    public void sendByeByeRequest() {
        ALReportRunningStatusChange runningStatusChange =
                new ALReportRunningStatusChange(ALReportRunningStatusChange.BYE_BYE);
        mEventCenter.sendEventNonBlock(runningStatusChange);
    }

    /**
     * send bluetooth status to car.
     *
     * @param status      bluetooth status ext connected, paired
     * @param unsolicited unsolicited
     */
    public void sendBluetoothStatus(int status, boolean unsolicited) {
        synchronized (mLock) {
            if (!isExited && mBluetooth != null) {
                mBluetooth.sendBluetoothStatus(status, unsolicited);
            }
        }
    }

    public void setCarId(String carId) {
        ALReportEvent updateId = new ALReportUpdateCarId(ALReportUpdateCarId.SET_CAR_ID, carId);
        mEventCenter.sendEventNonBlock(updateId);
    }

    private void startAllChanel() {
        if (mGalReceiver != null) {
            mGalReceiver.registerCarService(ALServiceBase.AL_SERVICE_BLUETOOTH, mBluetooth);
            mGalReceiver.registerCarService(ALServiceBase.AL_SERVICE_VIDEO_SOURCE, mVideoSource);
            mGalReceiver.registerCarService(ALServiceBase.AL_SERVICE_VENDOR, mVendorExtension);
            mGalReceiver.registerCarService(ALServiceBase.AL_SERVICE_INPUT_SINK, mAlInputSink);
            mGalReceiver.start();
        }
    }

    private void internalEventProcess(InternalEvent event) {
        if (event.getType() == InternalEvent.ERROR) {
            InternalCommonEvent error = (InternalCommonEvent) event;
            switch (error.getError()) {
                case InternalCommonEvent.PROTOCOL_UNRECOVERABLE_ERROR: {
                    XLog.tag(TAG).d("PROTOCOL_UNRECOVERABLE_ERROR");
                    if (mGalReceiver != null) {
                        mGalReceiver.stop();
                    }
                    mAppMessageListener.onDisconnected();
                    break;
                }
                case InternalCommonEvent.PROBE_SUCCESS: {
                    synchronized (mLock) {
                        startAllChanel();
                        isExited = false;
                    }
                    ALReportEvent reportEvent =
                            new ALReportRunningStatusChange(ALReportRunningStatusChange.PROBE_SUCCESS);
                    mEventCenter.sendEventNonBlock(reportEvent);
                    break;
                }
                case InternalCommonEvent.PROBE_FAIL: {
                    ALReportEvent reportEvent =
                            new ALReportRunningStatusChange(ALReportRunningStatusChange.PROBE_FAIL);
                    mEventCenter.sendEventNonBlock(reportEvent);
                    if (mGalReceiver != null) {
                        mGalReceiver.stop();
                    }
                    break;
                }
                case InternalCommonEvent.PROTOCOL_EXIT: {
                    synchronized (mLock) {
                        mVideoSource.exitVideoSession();
                        mGalReceiver.destroyCarServices();
                        mGalReceiver.sendExitResponse();
                        isExited = true;
                    }
                    break;
                }
                default:
                    break;
            }
        }
    }

    private void externalEventProcess(ALEvent event) {
        switch (event.getEventType()) {
            case ALEvent.SCREEN_ORIENTATION:
                ALScreenOrientationEvent orient = (ALScreenOrientationEvent) event;
                mGalReceiver.nativeSendScreenOrientationNotifi(orient.getOrientation(),
                        orient.getRotation());
                break;
            case ALEvent.SCREEN_RESOLUTION:
                ALScreenResolutionEvent resolutionEvent = (ALScreenResolutionEvent) event;
                mGalReceiver.nativeSendScreenResolutionNotification(resolutionEvent.getWidth(),
                        resolutionEvent.getHeight(),
                        resolutionEvent.isRequired());
                break;
            case ALEvent.READ_PARAM:
                ALReadParamEvent readParamEvent = (ALReadParamEvent) event;
                if (!readParamEvent.checkFinished()) {
                    mGalReceiver.sendReadRequest(readParamEvent);
                } else {
                    mGalReceiver.checkResult();
                }
                break;
            case ALEvent.WRITE_PARAM:
                //mAddress = mAddress+0x08;
                //mGalReceiver.sendWriteRequest("0xffff",mAddress,8);
                break;
            default:
                break;
        }
    }

    private void reportStatus(int status) {
        switch (status) {
            case ALReportRunningStatusChange.START:
                XLog.tag(TAG).d("connection start");
                break;
            case ALReportRunningStatusChange.PROBE_START:
                XLog.tag(TAG).d("probe start");
                break;
            case ALReportRunningStatusChange.PROBE_SUCCESS:
                XLog.tag(TAG).d("probe success");
                break;
            case ALReportRunningStatusChange.BYE_BYE:
                mGalReceiver.sendByeByeRequest(1);
                EventBus.getDefault().post(new ByeByeResponseEvent());
                break;
            default:
                break;
        }
    }


    private void updateCarInfo(ALReportUpLoadCarInfo upLoadCarInfo) {
        mCarId = upLoadCarInfo.getId();
    }

    private void reportEventProcess(ALReportEvent event) {
        switch (event.type()) {
            case ALReportEvent.RUNNING_STATE_CHANGE:
                ALReportRunningStatusChange reportRunningStatusChange =
                        (ALReportRunningStatusChange) event;
                reportStatus(reportRunningStatusChange.getStatus());
                break;
            case ALReportEvent.VIDEO_FOCUS_NOTIFICATION:
                ALReportVideoFocusNotification videoFocusNotification =
                        (ALReportVideoFocusNotification) event;
                EventBus.getDefault().post(new VideoFocusEvent(videoFocusNotification.getFocus()));
                break;
            case ALReportEvent.INQUIRE_ORIENTATION:
                ALEvent screenOrientationEvent = new ALScreenOrientationEvent(mIsLand, mRotation);
                mEventCenter.sendEvent(screenOrientationEvent);
                break;
            case ALReportEvent.INQUIRE_RESOLUTION:
                EventBus.getDefault().post(new ScreenResolutionEvent());
                break;
            case ALReportEvent.FORCE_LANDSCAPE:
                ALReportForceLandscapeRequest forceEvent = (ALReportForceLandscapeRequest) event;
                EventBus.getDefault().post(new LandscapeEvent(forceEvent.getForce()));
                break;
            case ALReportEvent.BLUETOOTH_READY_TO_PAIR:
                ALReportBluetoothPairingRequest btPair = (ALReportBluetoothPairingRequest) event;
                EventBus.getDefault().post(new ALBluetoothPairingEvent(btPair.addr(),btPair.getAlreadyPaired()));
                break;
            case ALReportEvent.BT_CHANNEL_OPEN:
                ALReportBluetoothChannel btChannel = (ALReportBluetoothChannel) event;
                EventBus.getDefault().post(new OpenBtEvent(btChannel.getAddress()));
                break;
            case ALReportEvent.PROJECTION_REQUEST:
                Log.d(TAG, "reportEventProcess PROJECTION_REQUEST");
                EventBus.getDefault().post(new ProjectionRequestEvent());
                break;
            case ALReportEvent.UPLOAD_CARINFO:
                updateCarInfo((ALReportUpLoadCarInfo) event);
                break;
            case ALReportEvent.UPLOAD_CARDATA:
            default:
                break;
        }
    }

    private AppMessageListener mAppMessageListener = new AppMessageListener() {

        @Override
        public void onAutoRotationRequest(boolean isAutoed) {
            EventBus.getDefault().post(new AutoRotationEvent(isAutoed));
        }

        @Override
        public void onDisconnected() {
            XLog.tag(TAG).d("onDisconnected");
            EventBus.getDefault().post(new DisconnectedEvent());
        }

        @Override
        public void onUnrecoverableError(int err) {
            EventBus.getDefault().post(new UnrecoverableErrorEvent(err));
        }

        @Override
        public void onVersionCallback(short major, short minor) {
        }

        @Override
        public void onCarInfoCallback(String id, String make, String model, String year,
                                      String huIc,
                                      String huMake, String huModel, String huSwBuild,
                                      String huSwVersion, String huSeries,
                                      String huMuVersion, int checkSum) {
            if (id == null) {
                ALReportEvent updateId = new ALReportUpdateCarId(ALReportUpdateCarId.GET_CAR_ID);
                mEventCenter.sendEventNonBlock(updateId);
            } else {
                ALReportEvent uploadCarInfo =
                        new ALReportUpLoadCarInfo(id, make, model, year, huIc, huMake, huModel,
                                huSwBuild,
                                huSwVersion, huSeries, huMuVersion, checkSum);
                mEventCenter.sendEventNonBlock(uploadCarInfo);
            }
        }

        @Override
        public void onByeByeRequest(int reason) {
            EventBus.getDefault().post(new ByeByeRequestEvent(reason));
        }

        @Override
        public void onByeByeResponse() {
            EventBus.getDefault().post(new ByeByeResponseEvent());
        }
    };
}
