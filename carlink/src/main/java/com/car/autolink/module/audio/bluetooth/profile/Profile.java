package com.car.autolink.module.audio.bluetooth.profile;

/**
 * <AUTHOR>
 * @date 2018/9/19
 */
public enum Profile {

    /**
     * A2DP
     */
    A2dp(BluetoothA2dpProfile.class),

    /**
     * Headset
     */
    Headset(BluetoothHeadsetProfile.class);

    private final Class<? extends IBluetoothProfile> mProfileClass;

    Profile(Class<? extends IBluetoothProfile> profileClass) {
        this.mProfileClass = profileClass;
    }

    /**
     * create IBluetoothProfile.
     *
     * @param obj context
     * @return {@link IBluetoothProfile} or null
     */
    public IBluetoothProfile create(Object... obj) {
        try {
            return mProfileClass.getConstructor(obj.getClass()).newInstance(obj);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
