package com.car.autolink.module.audio.bluetooth.profile;

import android.bluetooth.BluetoothDevice;

/**
 * <AUTHOR>
 * @date 2018/9/19
 */
public interface IBluetoothProfile {
    boolean isAvailable();

    boolean connect(BluetoothDevice device);

    void disconnect(BluetoothDevice device);

    int getProfileCode();

    int getProfileConnectionState(BluetoothDevice device);

    void close();
}
