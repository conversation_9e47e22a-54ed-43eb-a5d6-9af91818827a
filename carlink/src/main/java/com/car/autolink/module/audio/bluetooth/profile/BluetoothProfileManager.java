package com.car.autolink.module.audio.bluetooth.profile;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.Context;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/9/19
 */
public class BluetoothProfileManager {
    private Context mContext;
    private Map<Profile, IBluetoothProfile> mProfileMap = new HashMap<>();

    public BluetoothProfileManager(Context context) {
        this.mContext = context;
        initProfiles(context);
    }

    private void initProfiles(Context context) {
        mProfileMap.put(Profile.A2dp, new BluetoothA2dpProfile(context));
    }

    /**
     * setProfiles.
     *
     * @param profiles {@linkplain Profile}
     */
    public void setProfiles(Profile... profiles) {
        mProfileMap.clear();
        for (Profile profile : profiles) {
            mProfileMap.put(profile, Objects.requireNonNull(profile.create(mContext)));
        }
    }

    public boolean addProfile(Profile profile) {
        return mProfileMap.containsKey(profile)
                || mProfileMap.put(profile, Objects.requireNonNull(profile.create(mContext))) != null;
    }

    public boolean removeProfile(Profile profile) {
        return mProfileMap.remove(profile) != null;
    }

    /**
     * getProfileConnectionState.
     *
     * @param profile {@link Profile}
     * @param device  {@link BluetoothDevice}
     * @return state for device
     */
    public int getProfileConnectionState(Profile profile, BluetoothDevice device) {
        if (mProfileMap.containsKey(profile)) {
            return mProfileMap.get(profile).getProfileConnectionState(device);
        }
        return BaseBluetoothProfile.PAIRED;
    }

    /**
     * getAllProfileConnectionState.
     *
     * @return state for device's all profile
     */
    @SuppressLint("MissingPermission")
    public int getAllProfileConnectionState() {
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        int status = BaseBluetoothProfile.PAIRED;
        if (adapter.getProfileConnectionState(BluetoothProfile.A2DP)
                == BluetoothAdapter.STATE_CONNECTED) {
            status |= BaseBluetoothProfile.A2DP_CONNECTED;
        }

        if (adapter.getProfileConnectionState(BluetoothProfile.HEADSET)
                == BluetoothAdapter.STATE_CONNECTED) {
            status |= BaseBluetoothProfile.HEADSET_CONNECTED;
        }

        if (adapter.getProfileConnectionState(BluetoothProfile.HEALTH)
                == BluetoothAdapter.STATE_CONNECTED) {
            status |= BaseBluetoothProfile.HEALTH_CONNECTED;
        }

        int hid = 4;
        if (adapter.getProfileConnectionState(hid) == BluetoothAdapter.STATE_CONNECTED) {
            status |= BaseBluetoothProfile.HID_CONNECTED;
        }

        return status;
    }

    /**
     * connect one bluetoothDevice.
     *
     * @param device bluetoothDevice
     */
    public void connect(BluetoothDevice device) {
        for (IBluetoothProfile profile : mProfileMap.values()) {
            profile.connect(device);
        }
    }

    /**
     * connect one bluetoothDevice.
     *
     * @param device  bluetoothDevice
     * @param profile {@link Profile}
     * @return success or error
     */
    public boolean connect(BluetoothDevice device, Profile profile) {
        if (!mProfileMap.containsKey(profile)) {
            mProfileMap.put(profile, Objects.requireNonNull(profile.create(mContext)));
        }
        return mProfileMap.get(profile) != null && Objects.requireNonNull(mProfileMap.get(profile))
                .connect(device);
    }

    /**
     * disconnect one bluetoothDevice.
     *
     * @param device bluetoothDevice
     */
    public void disconnect(BluetoothDevice device) {
        for (IBluetoothProfile profile : mProfileMap.values()) {
            profile.disconnect(device);
        }
    }

    public void disconnect(BluetoothDevice device, Profile profile) {
        if (mProfileMap.containsKey(profile) && mProfileMap.get(profile) != null) {
            Objects.requireNonNull(mProfileMap.get(profile)).disconnect(device);
        }
    }

    /**
     * close all profile from bluetoothDevice.
     */
    public void close() {
        for (IBluetoothProfile profile : mProfileMap.values()) {
            profile.close();
        }
        mProfileMap.clear();
        mProfileMap = null;
        mContext = null;
    }
}
