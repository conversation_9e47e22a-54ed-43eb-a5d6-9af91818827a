package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALReportUpdateCarId extends ALReportEvent {

    public static final int GET_CAR_ID = 1;
    public static final int SET_CAR_ID = 2;
    private int method;
    private String id;


    public ALReportUpdateCarId(int method) {
        this.method = method;
        this.id = null;
    }

    public ALReportUpdateCarId(int method, String id) {
        this.method = method;
        this.id = id;
    }

    @Override
    public int type() {
        return UPDATE_CARID;
    }

    public int getMethod() {
        return method;
    }

    public void setMethod(int method) {
        this.method = method;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
