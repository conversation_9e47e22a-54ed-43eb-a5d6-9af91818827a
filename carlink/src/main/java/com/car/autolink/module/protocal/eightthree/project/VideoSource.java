package com.car.autolink.module.protocal.eightthree.project;

import com.car.autolink.module.protocal.eightthree.source.NativeObject;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class VideoSource implements NativeObject {
    public static class VideoFocusMode {
        public static int VIDEO_FOCUS_PROJECTED = 1;
        public static int VIDEO_FOCUS_NATIVE = 2;
        public static int VIDEO_FOCUS_NATIVE_TRANSIENT = 3;
    }

    private long mNativeVideoSource;
    private final boolean mAutoStartProjection;
    private final String mRemoteHost;
    public VideoSourceCallbacks mVideoListener;

    public VideoSource(VideoSourceCallbacks listener, boolean autoStartProjection,
                       String remoteHost) {
        mVideoListener = listener;
        mAutoStartProjection = autoStartProjection;
        mRemoteHost = remoteHost;
    }

    public boolean create(int id, long nativeGalReceiver) {
        return nativeInit(id, nativeGalReceiver, mAutoStartProjection, mRemoteHost) == 0;
    }

    @Override
    public void destroy() {
        nativeShutdown();
    }

    @Override
    public long getNativeInstance() {
        return mNativeVideoSource;
    }

    private native int nativeInit(int id, long nativeGalReceiver, boolean autoStart,
                                  String remoteHost)
            throws IllegalStateException;

    private native void nativeShutdown();

    public native void sendSetup(int type);

    public native void sendStart(int configIndex, int sessionId, int width, int height);

    public native void sendStop();

    public native void sendData(long timeStamp, byte[] data, int len, int flags);

    public native void sendVideoFocusRequestNotifi(int channelId, int mode, int reason);

    public native void sendDisplayAreaChangeResponse();

    public native void sendVideoOrientation(boolean isLandscape);

    public native boolean isStartResponseMessageExist();
}
