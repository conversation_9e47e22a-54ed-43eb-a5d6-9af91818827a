package com.car.autolink.module.protocal.eightthree.eventcenter;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public interface IALInternalEventProcess {
    /**
     * registerListener
     *
     * @param listener {@link Listener}
     */
    void registerListener(Listener listener);

    /**
     * unregisterListener
     *
     * @param listener {@link Listener}
     */
    void unregisterListener(Listener listener);

    /**
     * start
     *
     * @return success or not
     */
    boolean start();

    /**
     * sendInternalEvent
     *
     * @param event InternalEvent
     */
    void sendInternalEvent(InternalEvent event);

    /**
     * sendInternalEventNonBlock
     *
     * @param event InternalEvent
     */
    void sendInternalEventNonBlock(InternalEvent event);

    /**
     * shutdown
     */
    void shutdown();

    interface Listener {
        /**
         * onInternalEventProcess
         *
         * @param event InternalEvent
         */
        void onInternalEventProcess(InternalEvent event);
    }
}
