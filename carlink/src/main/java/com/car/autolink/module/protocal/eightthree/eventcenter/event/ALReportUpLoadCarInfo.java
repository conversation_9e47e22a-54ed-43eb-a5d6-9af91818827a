package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALReportUpLoadCarInfo extends ALReportEvent {

    private final String id;
    private final String make;
    private final String model;
    private final String year;
    private final String huIc;
    private final String huMake;
    private final String huModel;
    private final String huSwBuild;
    private final String huSwVersion;
    private final String huSeries;
    private final String huMuVersion;
    private final int checkSum;

    /**
     * Constructors ALReportUpLoadCarInfo.
     *
     * @param id          carId
     * @param make        car make
     * @param model       car model
     * @param year        car year
     * @param huIc        car huIc
     * @param huMake      car huMake
     * @param huModel     car huModel
     * @param huSwBuild   car huSwBuild
     * @param huSwVersion car huSwVersion
     * @param huSeries    car huSeries
     * @param huMuVersion car huMuVersion
     * @param checkSum    checkSum
     */
    public ALReportUpLoadCarInfo(String id, String make, String model, String year, String huIc,
                                 String huMake, String huModel, String huSwBuild,
                                 String huSwVersion, String huSeries,
                                 String huMuVersion, int checkSum) {

        this.id = id;
        this.make = make;
        this.model = model;
        this.year = year;
        this.huIc = huIc;
        this.huMake = huMake;
        this.huModel = huModel;
        this.huSwBuild = huSwBuild;
        this.huSwVersion = huSwVersion;
        this.huSeries = huSeries;
        this.huMuVersion = huMuVersion;
        this.checkSum = checkSum;
    }

    @Override
    public int type() {
        return UPLOAD_CARINFO;
    }

    public String getId() {
        return id;
    }

    public String getMake() {
        return make;
    }

    public String getModel() {
        return model;
    }

    public String getYear() {
        return year;
    }

    public String getHuIc() {
        return huIc;
    }

    public String getHuMake() {
        return huMake;
    }

    public String getHuModel() {
        return huModel;
    }

    public String getHuSwBuild() {
        return huSwBuild;
    }

    public String getHuSwVersion() {
        return huSwVersion;
    }

    public String getHuSeries() {
        return huSeries;
    }

    public String getHuMuVersion() {
        return huMuVersion;
    }

    public int getCheckSum() {
        return checkSum;
    }
}
