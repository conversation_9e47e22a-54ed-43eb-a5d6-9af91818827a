package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2018/04/10
 * @desc
 */
public class ALReportUploadCarData extends ALReportEvent {
    private byte[] data;
    private int length;

    public ALReportUploadCarData(byte[] data, int length) {
        this.data = data;
        this.length = length;
    }

    public byte[] getData() {
        return data;
    }


    public int getLength() {
        return length;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public void setLength(int length) {
        this.length = length;
    }

    @Override
    public int type() {
        return UPLOAD_CARDATA;
    }
}
