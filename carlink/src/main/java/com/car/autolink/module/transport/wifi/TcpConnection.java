package com.car.autolink.module.transport.wifi;

import android.util.Log;

import com.car.autolink.module.transport.DataConnection;
import com.elvishew.xlog.XLog;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.ServerSocket;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public class TcpConnection extends DataConnection {
    private static final String TAG = "TcpConnection";
    private static final int LISTEN_PORT = 30512;
    private ServerSocket mServerSocket;

    /**
     * getLocalPort.
     *
     * @return ok or fail
     */
    public int getLocalPort() {
        if (mServerSocket != null) {
            return mServerSocket.getLocalPort();
        }
        XLog.tag(TAG).e("getLocalPort shouldn't be called before server has started");
        return -1;
    }

    @Override
    protected void onStart() throws IOException {
        if (mServerSocket == null) {
            mServerSocket = new ServerSocket();
            mServerSocket.setReuseAddress(true);
            mServerSocket.bind(new InetSocketAddress(LISTEN_PORT));
        }
        Thread thread = new Thread("TCP server") {
            @Override
            public void run() {
                XLog.tag(TAG).i("Listening on: " + mServerSocket);
                try {
                    TcpTransport transport = new TcpTransport(mServerSocket.accept());
                    if (mCallback != null) {
                        mCallback.onConnected(transport);
                    }
                } catch (IOException e) {
                    Log.e(TAG, "Can't listen to socket: " + e);
                    if (mCallback != null) {
                        mCallback.onDisconnected();
                    }
                }
            }
        };
        thread.start();
    }

    @Override
    protected void onShutdown() {
        if (mServerSocket != null) {
            try {
                mServerSocket.close();
            } catch (IOException e) {
                XLog.tag(TAG).e("Can't close server socket: " + e);
            }
            mServerSocket = null;
        }
    }
}
