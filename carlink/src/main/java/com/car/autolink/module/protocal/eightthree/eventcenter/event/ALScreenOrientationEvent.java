package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALScreenOrientationEvent extends ALEvent {
    private final int mOrientation;
    private final int mRotation;

    public ALScreenOrientationEvent(int orientation, int rotation) {
        mOrientation = orientation;
        mRotation = rotation;
    }

    @Override
    public int getEventType() {
        return SCREEN_ORIENTATION;
    }

    public int getOrientation() {
        return mOrientation;
    }

    public int getRotation() {
        return mRotation;
    }
}
