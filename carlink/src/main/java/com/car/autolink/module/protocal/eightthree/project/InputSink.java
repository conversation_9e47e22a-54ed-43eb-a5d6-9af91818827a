package com.car.autolink.module.protocal.eightthree.project;

import com.car.autolink.module.protocal.eightthree.source.NativeObject;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class InputSink implements NativeObject {

    private long mNativeInputSink;
    public InputSinkCallbacks mInputListener;

    public InputSink(InputSinkCallbacks listener) {
        mInputListener = listener;
    }

    public boolean create(int id, long nativeGalReceiver) {
        return nativeInit(id, nativeGalReceiver) == 0;
    }

    @Override
    public long getNativeInstance() {
        return mNativeInputSink;
    }

    @Override
    public void destroy() {
        nativeShutdown();
    }

    private native int nativeInit(int id, long nativeGalReceiver) throws IllegalStateException;

    private native void nativeShutdown();

    public native void sendStart();
}
