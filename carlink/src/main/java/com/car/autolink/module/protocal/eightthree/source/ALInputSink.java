package com.car.autolink.module.protocal.eightthree.source;

import com.car.autolink.module.protocal.eightthree.project.InputSink;
import com.car.autolink.module.protocal.eightthree.project.InputSinkCallbacks;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALInputSink implements ALServiceBase {
    private final InputSink mInputSink;

    ALInputSink() {
        InputSinkCallbacks inputSinkCallBacks = new InputSinkCallbacks() {

            @Override
            public void onTouchPadEvent(long timestamp, int numPointers, int[] pointerIds, int[] x,
                                        int[] y, int action, int actionIndex) {

            }

            @Override
            public void onTouchEvent(long timestamp, int numPointers, int[] pointerIds, int[] x,
                                     int[] y,
                                     int action, int actionIndex) {

            }

            @Override
            public void onRelativeEvent(long timestamp, int keycode, int delta) {

            }

            @Override
            public void onKeyEvent(long timestamp, int keycode, boolean isDown, boolean longPress,
                                   int metaState) {

            }

            @Override
            public int onChannelOpened() {
                return 0;
            }

            @Override
            public void onAbsoluteEvent(long timestamp, int keycode, int value) {

            }

            @Override
            public boolean discoverInputService(boolean hasTouchscreen, boolean hasTouchpad,
                                                boolean hasKey) {
                return false;
            }
        };
        mInputSink = new InputSink(inputSinkCallBacks);
    }

    @Override
    public long getNativeInstance() {
        return mInputSink.getNativeInstance();
    }

    @Override
    public void destroy() {
        mInputSink.destroy();
    }

    @Override
    public boolean create(int serviceId, long nativeGalReceiver) {
        return mInputSink.create(serviceId, nativeGalReceiver);
    }

    @Override
    public boolean start() {
        return false;
    }

    @Override
    public void internalEvent(InternalEvent event) {
    }

    @Override
    public void externalEvent(ALEvent event) {
    }

    @Override
    public int getServiceType() {
        return AL_SERVICE_INPUT_SINK;
    }
}
