package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public abstract class ALEvent {
    public static final int VIDEO_FOCUS_REQUEST = 1;
    public static final int VIDEO_PERMISSION = 2;
    public static final int SCREEN_ORIENTATION = 3;
    public static final int BLUETOOTH_PAIRING_REQUEST = 4;
    public static final int UPGRADE_VERSIONDOWN = 5;
    public static final int UPGRADE_FILEDOWN = 6;
    public static final int UPGRADE_GETDATADOWN = 7;
    public static final int UPGRADE_CHECKSUMDOWN = 8;
    public static final int READ_PARAM = 9;
    public static final int WRITE_PARAM = 10;
    public static final int SCREEN_RESOLUTION = 11;

    /**
     * get event type
     *
     * @return type
     */
    public abstract int getEventType();
}
