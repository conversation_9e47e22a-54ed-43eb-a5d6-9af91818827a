package com.car.autolink.module.audio.bluetooth;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.car.autolink.module.audio.bluetooth.profile.BluetoothProfileManager;
import com.car.autolink.module.audio.bluetooth.profile.Profile;
import com.elvishew.xlog.XLog;

/**
 * <AUTHOR>
 * @date 2018/9/19
 */
public class BluetoothConnectManager {
    private static final String TAG = "BluetoothConnectManager";
    private static final String INPUT_DEVICE_ACTION =
            "android.bluetooth.input.profile.action.CONNECTION_STATE_CHANGED";
    private final Context mContext;
    private final BluetoothProfileManager mProfileManager;

    private BluetoothCallback mCallback;

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context,
                              Intent intent) {
            BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
            String action = intent.getAction();
            if (action != null) {
                XLog.tag(TAG).d(action);
                switch (action) {
                    case BluetoothAdapter.ACTION_STATE_CHANGED:
                        if (mCallback != null) {
                            mCallback.onBluetoothStateChange(
                                    intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, -1));
                        }
                        break;
                    case BluetoothDevice.ACTION_BOND_STATE_CHANGED:
                        if (mCallback != null) {
                            mCallback.onBondedStateChanged(device,
                                    intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, -1));
                        }
                        break;
                    case BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED:
                        if (mCallback != null) {
                            mCallback.onA2dpConnectedStateChanged(device,
                                    intent.getIntExtra(BluetoothProfile.EXTRA_STATE, -1));
                        }
                        break;
                    case INPUT_DEVICE_ACTION:
                        if (mCallback != null) {
                            mCallback.onHidHostConnectedStateChanged(device,
                                    intent.getIntExtra(BluetoothProfile.EXTRA_STATE, -1));
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    };

    /**
     * constructor.
     *
     * @param context {@link Context}
     */
    public BluetoothConnectManager(Context context) {
        this.mContext = context;
        this.mProfileManager = new BluetoothProfileManager(context);

        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        filter.addAction(BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED);
        filter.addAction(INPUT_DEVICE_ACTION);
        context.registerReceiver(mReceiver, filter);
    }

    /**
     * bind bluetoothDevice.
     *
     * @param device BluetoothDevice
     */
    @SuppressLint("MissingPermission")
    public boolean bindDevice(BluetoothDevice device) {
        return device.createBond();
    }

    public void connectProfile(BluetoothDevice device,
                               Profile profile) {
        mProfileManager.connect(device, profile);
    }

    public int getProfileConnectionState(BluetoothDevice device,
                                         Profile profile) {
        return mProfileManager.getProfileConnectionState(profile, device);
    }

    public int getAllProfileConnectionState() {
        return mProfileManager.getAllProfileConnectionState();
    }

    public void disconnectProfile(BluetoothDevice device,
                                  Profile profile) {
        mProfileManager.disconnect(device, profile);
    }

    public void disconnectAllProfile(BluetoothDevice device) {
        mProfileManager.disconnect(device);
    }

    public void destroy() {
        try {
            mContext.unregisterReceiver(mReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setBluetoothCallback(BluetoothCallback callback) {
        this.mCallback = callback;
    }

    public interface BluetoothCallback {

        void onBluetoothStateChange(int state);

        void onBondedStateChanged(BluetoothDevice device, int state);

        void onA2dpConnectedStateChanged(BluetoothDevice device, int state);

        void onHidHostConnectedStateChanged(BluetoothDevice device, int state);
    }
}
