package com.car.autolink.module.transport.wifi;

import com.car.autolink.module.transport.DataConnection;
import com.car.autolink.module.transport.ITransport;
import com.elvishew.xlog.XLog;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public class StreamTransport implements ITransport {
    private static final String TAG = "StreamTransport";
    private final InputStream mInputStream;
    private final OutputStream mOutputStream;

    StreamTransport(InputStream input, OutputStream output) {
        mInputStream = input;
        mOutputStream = output;
    }

    @Override
    public void stopTransport() {
        try {
            mInputStream.close();
            mOutputStream.close();
        } catch (IOException e) {
            XLog.tag(TAG).w("Failed to close streams: " + e);
        }
    }

    @Override
    public int read(byte[] b, int off, int len) throws IOException {
        return mInputStream.read(b, off, len);
    }

    @Override
    public void write(byte[] b, int off, int len) throws IOException {
        mOutputStream.write(b, off, len);
    }

    @Override
    public int getType() {
        return DataConnection.WIFI_P2P;
    }
}
