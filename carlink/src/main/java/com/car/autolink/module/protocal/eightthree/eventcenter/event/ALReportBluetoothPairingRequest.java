package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALReportBluetoothPairingRequest extends ALReportEvent {
    private final String mAddress;
    private final boolean mAlreadyPaired;

    /**
     * Constructors ALReportBluetoothPairingRequest.
     *
     * @param address       bluetooth address
     * @param alreadyPaired alreadyPaired
     */
    public ALReportBluetoothPairingRequest(String address, boolean alreadyPaired) {
        mAddress = address;
        mAlreadyPaired = alreadyPaired;
    }

    @Override
    public int type() {
        return BLUETOOTH_READY_TO_PAIR;
    }

    public String addr() {
        return mAddress;
    }

    public boolean getAlreadyPaired() {
        return mAlreadyPaired;
    }
}
