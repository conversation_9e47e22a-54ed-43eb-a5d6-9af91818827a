package com.car.autolink.module.protocal.eightthree.eventcenter;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public interface IALExternalEventProcess {

    /**
     * registerListener
     *
     * @param listener {@link Listener}
     */
    void registerListener(Listener listener);

    /**
     * unregisterListener
     *
     * @param listener {@link Listener}
     */
    void unregisterListener(Listener listener);

    /**
     * start
     *
     * @return success or not
     */
    boolean start();

    /**
     * sendExternalEvent
     *
     * @param event ALEvent
     */
    void sendExternalEvent(ALEvent event);

    /**
     * sendExternalEventNonBlock
     *
     * @param event ALEvent
     */
    void sendExternalEventNonBlock(ALEvent event);

    /**
     * shutdown
     */
    void shutdown();

    interface Listener {
        /**
         * onExternalEventProcess
         *
         * @param event ALEvent
         */
        void onExternalEventProcess(ALEvent event);
    }
}
