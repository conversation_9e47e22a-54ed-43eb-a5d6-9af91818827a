package com.car.autolink.module.protocal.eightthree.eventcenter;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALEventCenter implements IALEventCenter {

    private IALReportEventProcess mReportEventQueue;
    private IALInternalEventProcess mInternalEventQueue;
    private IALExternalEventProcess mExternalEventQueue;
    private IALEventCenter.Listener mListener;

    /**
     * Constructors ALEventCenter with initial three event queue.
     */
    public ALEventCenter() {
        mReportEventQueue = new ReportEventQueue();
        mInternalEventQueue = new InternalEventQueue();
        mExternalEventQueue = new ExternalEventQueue();

        mInternalEventQueue.registerListener(mAlInternalEventListener);
        mExternalEventQueue.registerListener(mAlExternalEventListener);
        mReportEventQueue.registerListener(mAlReportEventListener);
    }

    @Override
    public void release() {

        mInternalEventQueue.unregisterListener(mAlInternalEventListener);
        mExternalEventQueue.unregisterListener(mAlExternalEventListener);
        mReportEventQueue.unregisterListener(mAlReportEventListener);

        mReportEventQueue = null;
        mInternalEventQueue = null;
        mExternalEventQueue = null;
    }

    @Override
    public int start() {
        mReportEventQueue.start();
        mInternalEventQueue.start();
        mExternalEventQueue.start();
        return 0;
    }

    @Override
    public int shutdown() {
        mReportEventQueue.shutdown();
        mInternalEventQueue.shutdown();
        mExternalEventQueue.shutdown();
        return 0;
    }

    @Override
    public void sendEvent(InternalEvent event) {
        mInternalEventQueue.sendInternalEvent(event);
    }

    @Override
    public void sendEvent(ALReportEvent event) {
        mReportEventQueue.sendReportEvent(event);
    }

    @Override
    public void sendEvent(ALEvent event) {
        mExternalEventQueue.sendExternalEvent(event);
    }

    @Override
    public void sendEventNonBlock(InternalEvent event) {
        mInternalEventQueue.sendInternalEventNonBlock(event);
    }

    @Override
    public void sendEventNonBlock(ALReportEvent event) {
        mReportEventQueue.sendReportEventNonBlock(event);
    }

    @Override
    public void sendEventNonBlock(ALEvent event) {
        mExternalEventQueue.sendExternalEventNonBlock(event);
    }

    @Override
    public void registerListener(Listener listener) {
        mListener = listener;
    }

    @Override
    public void unregisterListener(Listener listener) {
        mListener = null;
    }

    private final IALExternalEventProcess.Listener mAlExternalEventListener =
            event -> mListener.process(event);

    private final IALInternalEventProcess.Listener mAlInternalEventListener =
            event -> mListener.process(event);


    private final IALReportEventProcess.Listener mAlReportEventListener =
            event -> mListener.process(event);

}
