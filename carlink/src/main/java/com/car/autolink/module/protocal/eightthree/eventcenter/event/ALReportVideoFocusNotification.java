package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALReportVideoFocusNotification extends ALReportEvent {
    private final int mFocus;
    private final boolean mUnsolicited;

    public ALReportVideoFocusNotification(int focus, Boolean unsolicited) {
        mFocus = focus;
        mUnsolicited = unsolicited;
    }

    @Override
    public int type() {
        return VIDEO_FOCUS_NOTIFICATION;
    }

    public int getFocus() {
        return mFocus;
    }

    public boolean getUnsolicited() {
        return mUnsolicited;
    }
}
