package com.car.autolink.module.protocal.eightthree.project;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public interface InputSinkCallbacks extends NativeCallback {
    /**
     * onChannelOpened
     *
     * @return 0 or -1
     */
    int onChannelOpened();

    /**
     * discoverInputService
     *
     * @param hasTouchscreen hasTouchscreen
     * @param hasTouchpad    hasTouchpad
     * @param hasKey         hasKey
     * @return success or not
     */
    boolean discoverInputService(boolean hasTouchscreen, boolean hasTouchpad, boolean hasKey);

    /**
     * onTouchEvent
     *
     * @param timestamp   timestamp
     * @param numPointers numPointers
     * @param pointerIds  pointerIds
     * @param x           x
     * @param y           y
     * @param action      action
     * @param actionIndex actionIndex
     */
    void onTouchEvent(long timestamp, int numPointers, int[] pointerIds, int[] x, int[] y,
                      int action,
                      int actionIndex);

    /**
     * onTouchPadEvent
     *
     * @param timestamp   timestamp
     * @param numPointers numPointers
     * @param pointerIds  pointerIds
     * @param x           x
     * @param y           y
     * @param action      action
     * @param actionIndex actionIndex
     */
    void onTouchPadEvent(long timestamp, int numPointers, int[] pointerIds, int[] x, int[] y,
                         int action, int actionIndex);

    /**
     * onKeyEvent
     *
     * @param timestamp timestamp
     * @param keycode   keycode
     * @param isDown    isDown
     * @param longPress longPress
     * @param metaState metaState
     */
    void onKeyEvent(long timestamp, int keycode, boolean isDown,
                    boolean longPress, int metaState);

    /**
     * onAbsoluteEvent
     *
     * @param timestamp timestamp
     * @param keycode   keycode
     * @param value     value
     */
    void onAbsoluteEvent(long timestamp, int keycode, int value);

    /**
     * onRelativeEvent
     *
     * @param timestamp timestamp
     * @param keycode   keycode
     * @param delta     delta
     */
    void onRelativeEvent(long timestamp, int keycode, int delta);
}
