package com.car.autolink.module.protocal.eightthree;

import android.util.Log;

import androidx.annotation.NonNull;

import com.car.autolink.config.AlConfig;
import com.car.autolink.config.PhoneVideoConfig;
import com.car.autolink.config.VideoConfig;
import com.car.autolink.config.VideoPackage;
import com.car.autolink.events.StartEncodeEvent;
import com.car.autolink.events.StopEncodeEvent;
import com.car.autolink.events.VideoConfigEvent;
import com.car.autolink.module.protocal.BaseProtocol;
import com.car.autolink.module.protocal.eightthree.source.ALIntegration;
import com.car.autolink.module.protocal.eightthree.source.ITransportChannel;
import com.car.autolink.module.protocal.eightthree.source.IVideoPlayerBase;

import org.greenrobot.eventbus.EventBus;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class EightThreeProtocol extends BaseProtocol {
    static {
        System.loadLibrary("aljni");
    }

    private ALIntegration mALIntegration;

    @Override
    public void sendPermission(boolean granted) {
        if (mALIntegration != null) {
            mALIntegration.sendPermission(granted);
        }
    }

    @Override
    public String getHuMake() {
        if (mALIntegration != null) {
            return mALIntegration.getHuMake();
        }
        return null;
    }

    @Override
    protected void onStart(@NonNull AlConfig alConfig) {
        mALIntegration = new ALIntegration();
        mALIntegration.init(alConfig, mVideoPlayerBase, mTransportChannel, true);
        mALIntegration.start();
    }

    @Override
    protected void onStop() {
        if (mALIntegration != null) {
            mALIntegration.stop();
        }
    }

    @Override
    protected void onShutdown() {
        if (mALIntegration != null) {
            Log.d("EightThreeProtocol", "onShutdown");
            mALIntegration.stop();
            mALIntegration.destroy();
            mALIntegration = null;
        }
    }

    @Override
    public void sendOrientation(int isLand, int rotation) {
        if (mALIntegration != null) {
            mALIntegration.sendOrientation(isLand, rotation);
        }
    }

    @Override
    public void sendResolutionNotification(int width, int height, boolean isRequired) {
        if (mALIntegration != null) {
            mALIntegration.sendResolutionNotification(width, height, isRequired);
        }
    }

    @Override
    public void setEncoderState(int focus, int reason) {
        if (mALIntegration != null) {
            mALIntegration.sendEncoderState(focus, reason);
        }
    }

    @Override
    public void sendBluetoothStatus(int status, boolean unsolicited) {
        if (mALIntegration != null) {
            mALIntegration.sendBluetoothStatus(status, unsolicited);
        }
    }

    private final ITransportChannel mTransportChannel = new ITransportChannel() {

        @Override
        public void stopTransport() {
            if (mCallback != null) {
                mCallback.stopTransport();
            }
        }

        @Override
        public int read(byte[] buf, int offset, int len) throws IOException {
            if (mCallback != null) {
                return mCallback.requestRead(buf, offset, len);
            }
            return 0;
        }

        @Override
        public void write(byte[] buf, int offset, int len) throws IOException {
            if (mCallback != null) {
                mCallback.requestWrite(buf, offset, len);
            }
        }
    };

    private final IVideoPlayerBase mVideoPlayerBase = new IVideoPlayerBase() {

        @Override
        public void release() {
            //do nothing
        }

        @Override
        public void init(VideoConfig videoConfig) {
            EventBus.getDefault().post(new VideoConfigEvent(videoConfig));
        }

        @Override
        public int start() {
            EventBus.getDefault().post(new StartEncodeEvent());
            return 0;
        }

        @Override
        public void stop() {
            EventBus.getDefault().post(new StopEncodeEvent());
        }
    };

    @Override
    public void sendByeByeRequest(int reason) {
        if (mALIntegration != null) {
            mALIntegration.sendByeByeRequest();
        }
    }

    @Override
    public void sendParingRequest(String address) {
        if (mALIntegration != null) {
            mALIntegration.sendParingRequest(address);
        }
    }

    @Override
    public void setCarId(String id) {
        if (mALIntegration != null) {
            mALIntegration.setCarId(id);
        }
    }

    @Override
    public void sendFrame(VideoPackage pkg) {
        if (mALIntegration != null) {
            mALIntegration.sendFrame(pkg);
        }
    }
}
