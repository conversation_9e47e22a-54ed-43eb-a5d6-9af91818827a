package com.car.autolink.module.video;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.SurfaceTexture;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaFormat;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionConfig;
import android.media.projection.MediaProjectionManager;
import android.opengl.EGLContext;
import android.opengl.GLES20;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;

import com.car.autolink.base.BaseVideo;
import com.car.autolink.config.VideoConfig;
import com.car.autolink.config.VideoPackage;
import com.car.autolink.events.ProjectionPermissionEvent;
import com.car.autolink.module.video.glutils.EglTask;
import com.car.autolink.module.video.glutils.FullFrameRect;
import com.car.autolink.module.video.glutils.Texture2dProgram;
import com.car.autolink.module.video.glutils.WindowSurface;
import com.elvishew.xlog.XLog;

import org.greenrobot.eventbus.EventBus;

import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public final class ProjectionRenderer extends BaseVideo {
    private static final String TAG = "ProjectionRenderer";
    private static final int MEDIA_CODEC_VIDEO_MPEG4_ES = 5;
    private static final int BITRATE = 4 * 1024 * 1024;
    private static final int I_FRAME_INTERVAL = 3;
    private MediaCodec mCodec;
    private Surface mInputSurface;
    private final VideoPackage mPackage = new VideoPackage();
    private final MediaProjectionManager mProjectionManager;
    private MediaProjection mMediaProjection = null;
    private Thread mCaptureThread = null;
    private final Object mSync = new Object();
    private final Handler mHandler;
    private int mVideoWidth = 1280;
    private int mVideoHeight = 720;
    private int mFrameRate = 15;
    private String mVideoMimeType = "video/avc";
    private boolean mRequestDraw;
    private boolean mRequestEncode = false;
    private volatile boolean mIsRecording;
    private MediaFormat format;
    private DisplayManager displayManager;
    private int mode = 0;

    /**
     * ProjectionRenderer.
     *
     * @param context android context.
     */
    public ProjectionRenderer(Context context) {
        mProjectionManager =
                (MediaProjectionManager) context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        displayManager = (DisplayManager) context.getSystemService(Context.DISPLAY_SERVICE);
        final HandlerThread thread = new HandlerThread(TAG);
        thread.start();
        mHandler = new Handler(thread.getLooper());
    }

    @Override
    protected void onStart() {
        startEncode();
        startRender();
    }

    @Override
    protected void onStop() {
        stopRender();
        stopEncode();
    }

    @Override
    protected void onShutdown() {
        onStop();
        release();
    }

    @Override
    public void setVideoConfig(VideoConfig config) {
        mFrameRate = config.getFrameRate();
        if (config.getCodec() == MEDIA_CODEC_VIDEO_MPEG4_ES) {
            mVideoMimeType = "video/mp4v-es";
        } else {
            mVideoMimeType = "video/avc";
        }
        selectBestConfig(config);
    }

    @Override
    public boolean setPermissionResult(int resultCode, Intent intent, boolean isSecondary) {
        if (resultCode == Activity.RESULT_OK) {
            mMediaProjection = mProjectionManager.getMediaProjection(resultCode, intent);
            if (isSecondary) {
                startRender();
            }
            return true;
        } else {
            mMediaProjection = null;
            return false;
        }
    }

    @Override
    public Intent requestPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            return mProjectionManager.createScreenCaptureIntent(MediaProjectionConfig.createConfigForDefaultDisplay());
        } else {
            return mProjectionManager.createScreenCaptureIntent();
        }
    }

    @Override
    public void setProjectionMode(int mode) {
        this.mode = mode;
        onStop();
        onStart();

    }

    private void selectBestConfig(VideoConfig config) {
        format = new MediaFormat();
        format.setString(MediaFormat.KEY_MIME, mVideoMimeType);
        format.setInteger(MediaFormat.KEY_WIDTH, config.getWidth());
        format.setInteger(MediaFormat.KEY_HEIGHT, config.getHeight());
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT,
                MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
        format.setInteger(MediaFormat.KEY_BIT_RATE, BITRATE);
        format.setInteger(MediaFormat.KEY_FRAME_RATE, mFrameRate);
        format.setInteger(MediaFormat.KEY_CAPTURE_RATE, mFrameRate);
        format.setInteger(MediaFormat.KEY_REPEAT_PREVIOUS_FRAME_AFTER, 1000000 / mFrameRate);
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            format.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileHigh);
            format.setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel41);
        }
        MediaCodecList codecList = new MediaCodecList(MediaCodecList.REGULAR_CODECS);
        String result = codecList.findEncoderForFormat(format);

        if (result == null) {
            XLog.tag(TAG).w("platform video config not support:" + format);
            if (config.getWidth() > config.getHeight()) {
                mVideoWidth = 1280;
                mVideoHeight = 720;
            } else {
                mVideoWidth = 720;
                mVideoHeight = 1280;
            }
            config.setWidth(mVideoWidth);
            config.setHeight(mVideoHeight);
        } else {
            mVideoWidth = config.getWidth();
            mVideoHeight = config.getHeight();
        }
        format.setInteger(MediaFormat.KEY_WIDTH, mVideoWidth);
        format.setInteger(MediaFormat.KEY_HEIGHT, mVideoHeight);
        XLog.tag(TAG).d("best config:" + format);
    }

    /**
     * dequeueRenderFrame.
     * Note: catch IllegalStateException is not good method.
     */
    private void dequeueRenderFrame(MediaCodec codec, int index, MediaCodec.BufferInfo info) {
        try {
            ByteBuffer encodedData = codec.getOutputBuffer(index);
            if (encodedData != null) {
                encodedData.position(info.offset);
                encodedData.limit(info.offset + info.size);
                mPackage.setData(new byte[info.size]);
                mPackage.setSize(info.size);
                mPackage.setFlags(info.flags);
                encodedData.get(mPackage.getData(), 0, info.size);
                if (mCallback != null) {
                    mCallback.onRenderFrame(mPackage);
                }
                codec.releaseOutputBuffer(index, false);
            }
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

    private void startEncode() {
        try {
            mCodec = MediaCodec.createEncoderByType(mVideoMimeType);
            mCodec.setCallback(new MediaCodec.Callback() {

                @Override
                public void onInputBufferAvailable(@NonNull MediaCodec codec, int index) {
                    //do nothing
                }

                @Override
                public void onOutputBufferAvailable(@NonNull MediaCodec codec, int index,
                                                    @NonNull MediaCodec.BufferInfo info) {
                    dequeueRenderFrame(codec, index, info);
                }

                @Override
                public void onError(@NonNull MediaCodec codec,
                                    @NonNull MediaCodec.CodecException e) {
                    //do nothing
                }

                @Override
                public void onOutputFormatChanged(@NonNull MediaCodec codec,
                                                  @NonNull MediaFormat format) {
                    //do nothing
                }
            });
            mCodec.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            mInputSurface = mCodec.createInputSurface();
            mCodec.start();
        } catch (Exception ignore) {

        }
    }

    private void stopEncode() {
        if (mInputSurface != null) {
            mInputSurface.release();
            mInputSurface = null;
        }
        try {
            if (mCodec != null) {
                try {
                    mCodec.stop();
                } catch (IllegalStateException e) {
                    XLog.tag(TAG).e(e);
                }

                try {
                    mCodec.release();
                } catch (IllegalStateException e) {
                    XLog.tag(TAG).e(e);
                } finally {
                    mCodec = null;
                }
            }
        } catch (Exception e) {
            XLog.tag(TAG).e(e);
        }
    }

    private void startRender() {
        if (mMediaProjection == null) {
            XLog.tag(TAG).d("mMediaProjection is null");
            EventBus.getDefault().post(new ProjectionPermissionEvent(requestPermission(), true));
            return;
        }
        mIsRecording = true;
        if (mCaptureThread != null && mCaptureThread.isAlive()) {
            try {
                mCaptureThread.join();
            } catch (InterruptedException e) {
                XLog.tag(TAG).e("CaptureThread:" + e);
            }
            mCaptureThread = null;
        }
        DrawTask screenCaptureTask = new DrawTask(null, 0);
        mCaptureThread = new Thread(screenCaptureTask, "ScreenCaptureThread");
        mCaptureThread.start();
    }

    private void stopRender() {
        synchronized (mSync) {
            mIsRecording = false;
            mSync.notifyAll();
        }

        if (mCaptureThread != null) {
            mCaptureThread.interrupt();
        }
    }

    private void release() {
        if (mDisplay != null) {
            mDisplay.release();
            mDisplay = null;
        }

        if (mMediaProjection != null) {
            mMediaProjection.unregisterCallback(mMediaProjectionCallback);
            mMediaProjection.stop();
            mMediaProjection = null;
        }
        mHandler.getLooper().quit();
    }

    private VirtualDisplay mDisplay;
    private final MediaProjection.Callback mMediaProjectionCallback = new MediaProjection.Callback() {
        @Override
        public void onStop() {
            Log.d(TAG, "onStop");
            super.onStop();
            if (mDisplay != null) {
                mDisplay.release();
                mDisplay = null;
            }

            if (mMediaProjection != null) {
                mMediaProjection.unregisterCallback(mMediaProjectionCallback);
                mMediaProjection.stop();
                mMediaProjection = null;
            }
        }

        @Override
        public void onCapturedContentResize(int width, int height) {
            Log.d(TAG, "onCapturedContentResize" + width + "x" + height);
            super.onCapturedContentResize(width, height);
        }

        @Override
        public void onCapturedContentVisibilityChanged(boolean isVisible) {
            Log.d(TAG, "onCapturedContentVisibilityChanged: " + isVisible);
            super.onCapturedContentVisibilityChanged(isVisible);
        }
    };

    /**
     * frame control for surface data.
     * current frame rate is 30fps,mIntervals is for control.
     */
    private final class DrawTask extends EglTask {
        private long mIntervals;
        private int mTexId;
        private SurfaceTexture mSourceTexture;
        private Surface mSourceSurface;
        private WindowSurface mEncoderSurface;
        private FullFrameRect mDrawer;

        private final float[] mTexMatrix = new float[16];
        private long mLastFrameTime = System.currentTimeMillis();

        DrawTask(final EGLContext sharedContext, final int flags) {
            super(sharedContext, flags);
        }

        @Override
        protected void onStart() {
            mDrawer =
                    new FullFrameRect(new Texture2dProgram(Texture2dProgram.ProgramType.TEXTURE_EXT));
            mTexId = mDrawer.createTextureObject();
            mSourceTexture = new SurfaceTexture(mTexId, false);
            mSourceTexture.setDefaultBufferSize(mVideoWidth, mVideoHeight);
            mSourceSurface = new Surface(mSourceTexture);
            mEncoderSurface = new WindowSurface(getEglCore(), mInputSurface);
            mSourceTexture.setOnFrameAvailableListener(mOnFrameAvailableListener, mHandler);
            mIntervals = (long) (1000f / mFrameRate);
            int videoDensity = 160;
            if (mode == 0) {
                if (mDisplay == null) {
                    mMediaProjection.registerCallback(mMediaProjectionCallback, mHandler);
                    mDisplay = mMediaProjection.createVirtualDisplay("Autolink Display", mVideoWidth,
                            mVideoHeight,
                            videoDensity, DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC,
                            mSourceSurface, null, mHandler);
                } else {
                    mDisplay.resize(mVideoWidth, mVideoHeight, videoDensity);
                    mDisplay.setSurface(mSourceSurface);
                }
            } else {
                mDisplay = displayManager.createVirtualDisplay("projection Display", mVideoWidth, mVideoHeight,
                        240, mSourceSurface, DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION);
                if (mCallback != null) {
                    mCallback.onPresentationDisplayCreated(mDisplay.getDisplay());
                }
            }
            queueEvent(mDrawTask);
        }

        @Override
        protected void onStop() {
            if (mDrawer != null) {
                mDrawer.release();
                mDrawer = null;
            }
            if (mSourceSurface != null) {
                mSourceSurface.release();
                mSourceSurface = null;
            }
            if (mSourceTexture != null) {
                mSourceTexture.release();
                mSourceTexture = null;
            }
            if (mEncoderSurface != null) {
                mEncoderSurface.release();
                mEncoderSurface = null;
            }

            makeCurrent();
        }

        @Override
        protected boolean onError(final Exception e) {
            return true;
        }

        @Override
        protected boolean processRequest(final int request, final int arg1, final Object arg2) {
            return false;
        }

        private final SurfaceTexture.OnFrameAvailableListener mOnFrameAvailableListener =
                surfaceTexture -> {
                    if (mIsRecording) {
                        synchronized (mSync) {
                            mRequestDraw = true;
                            mSync.notifyAll();
                        }
                    }
                };

        private final Runnable mDrawTask = new Runnable() {

            @Override
            public void run() {
                boolean localRequestDraw;
                synchronized (mSync) {
                    localRequestDraw = mRequestDraw;
                    mRequestDraw = false;
                    if (!localRequestDraw) {
                        try {
                            mSync.wait(mIntervals);
                            localRequestDraw = mRequestDraw;
                            mRequestDraw = false;
                        } catch (final InterruptedException e) {
                            releaseSelf();
                            return;
                        }
                    }
                }
                if (mIsRecording) {
                    if (localRequestDraw) {
                        mSourceTexture.updateTexImage();
                        mSourceTexture.getTransformMatrix(mTexMatrix);
                        mRequestEncode = true;
                    }
                    long nowTime = System.currentTimeMillis();
                    if (nowTime - mLastFrameTime > mIntervals) {
                        mLastFrameTime = nowTime;
                        if (mRequestEncode) {
                            mRequestEncode = false;
                            mEncoderSurface.makeCurrent();
                            mDrawer.drawFrame(mTexId, mTexMatrix);
                            mEncoderSurface.swapBuffers();
                        }
                    }
                    makeCurrent();
                    if (localRequestDraw) {
                        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);
                        GLES20.glFlush();
                    }
                    queueEvent(this);
                } else {
                    releaseSelf();
                }
            }
        };
    }
}
