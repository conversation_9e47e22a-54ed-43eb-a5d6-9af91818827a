package com.car.autolink.module.protocal.eightthree.project;

import com.car.autolink.module.protocal.eightthree.source.NativeObject;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class UpgradeChannel implements NativeObject {
    private long mNativeUpgradeChannel;
    private UpgradeChannelCallbacks mUpgradeCallbacks;

    @Override
    public long getNativeInstance() {
        return mNativeUpgradeChannel;
    }

    @Override
    public void destroy() {
        nativeShutdown();
    }

    public UpgradeChannel(UpgradeChannelCallbacks callbacks) {
        mUpgradeCallbacks = callbacks;
    }

    // call from java
    public boolean create(int id, long nativeGalReceiver) {
        return nativeInit(id, nativeGalReceiver) == 0;
    }

    private native int nativeInit(int id, long nativeGalReceiver) throws IllegalStateException;

    private native void nativeShutdown();

    public native void sendUpgradeInfoNotif(ArrayList<FileInfo> list);

    public native void sendCheckVersionFail(int type);

    public native void sendDownloadstart();

    public native void sendDownloadFail(int type);

    public native void sendUpgradeStart();

    public native int sendUpgradeProgress();

    public native void sendDownloadProgress(int progress);

    public native void sendUpgradeFileData(byte[] buf, int len);

    public native void sendChecksumValue(boolean isSuccess);
}
