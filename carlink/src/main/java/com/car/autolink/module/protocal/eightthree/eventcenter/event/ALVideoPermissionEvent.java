package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALVideoPermissionEvent extends ALEvent {
    private final boolean mStatus;

    public ALVideoPermissionEvent(Boolean status) {
        mStatus = status;
    }

    @Override
    public int getEventType() {
        return VIDEO_PERMISSION;
    }

    public boolean getStatus() {
        return mStatus;
    }
}
