package com.car.autolink.module.protocal.eightthree.project;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class Protos {

    // VideoFocusReason
    public static final int UNKNOWN = 0;
    public static final int PHONE_SCREEN_OFF = 1;
    public static final int LAUNCH_NATIVE = 2;
    public static final int NO_PERMISSION = 3;
    public static final int WAIT_PERMISSION = 4;
    public static final int NO_VALID_VIDEO_ENCODER = 5;

    // VideoFocusMode
    public static final int VIDEO_FOCUS_PROJECTED = 1;
    public static final int VIDEO_FOCUS_NATIVE = 2;
    public static final int VIDEO_FOCUS_NATIVE_TRANSIENT = 3;

    // MediaCodecType
    public static final int MEDIA_CODEC_AUDIO_PCM = 1;
    public static final int MEDIA_CODEC_AUDIO_AAC_LC = 2;
    public static final int MEDIA_CODEC_VIDEO_H264_BP = 3;
    public static final int MEDIA_CODEC_AUDIO_AAC_LC_ADTS = 4;
    public static final int MEDIA_CODEC_VIDEO_MPEG4_ES = 5;

    // BluetoothPairingMethod
    public static final int BLUETOOTH_PAIRING_OOB = 1;
    public static final int BLUETOOTH_PAIRING_NUMERIC_COMPARISON = 2;
    public static final int BLUETOOTH_PAIRING_PASSKEY_ENTRY = 3;
    public static final int BLUETOOTH_PAIRING_PIN = 4;

    // Config_Status
    public static final int Config_Status_STATUS_WAIT = 1;
    public static final int Config_Status_STATUS_READY = 2;

    // MessageStatus
    public static final int STATUS_SUCCESS = 0;
}
