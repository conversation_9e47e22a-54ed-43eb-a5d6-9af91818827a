package com.car.autolink.module.transport.wifi;

import com.elvishew.xlog.XLog;

import java.io.IOException;
import java.net.Socket;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public class TcpTransport extends StreamTransport {
    private static final String TAG = "TcpTransport";
    private final Socket mSocket;

    TcpTransport(Socket socket) throws IOException {
        super(socket.getInputStream(), socket.getOutputStream());
        mSocket = socket;
        mSocket.setTcpNoDelay(true);
    }

    @Override
    public void stopTransport() {
        super.stopTransport();
        try {
            mSocket.close();
        } catch (IOException e) {
            XLog.tag(TAG).e("Can't close socket: " + e);
        }
    }

    public String getRemoteSocketAddress() {

        return mSocket.getInetAddress().getHostAddress();
    }
}

