package com.car.autolink.module.protocal.eightthree.project;

import com.car.autolink.module.protocal.eightthree.source.NativeObject;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class BluetoothEndpoint implements NativeObject {
    public BluetoothCallbacks mListener;
    private long mNativeBluetoothEndpoint;

    public BluetoothEndpoint(BluetoothCallbacks listener) {
        mListener = listener;
    }

    public boolean create(int serviceId, long nativeGalReceiver) {
        return nativeInit(serviceId, nativeGalReceiver) == 0;
    }

    @Override
    public void destroy() {
        nativeShutdown();
    }

    @Override
    public long getNativeInstance() {
        return mNativeBluetoothEndpoint;
    }

    private native int nativeInit(int id, long nativeGalReceiver) throws IllegalStateException;

    private native void nativeShutdown();

    public native void nativeSendPairingRequest(String address, int pairMethod);

    public native void nativeSendBluetoothStatus(int status, boolean unsolicited);
}
