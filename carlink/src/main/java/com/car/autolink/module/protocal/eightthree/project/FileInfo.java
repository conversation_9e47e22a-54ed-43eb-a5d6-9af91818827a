package com.car.autolink.module.protocal.eightthree.project;

public class FileInfo {
    private String name;
    private int versionX;
    private int versionY;
    private int versionZ;
    private int size;
    private String module;

    /**
     * Constructors FileInfo.
     */
    public FileInfo(String name, int x, int y, int z, int size, String module) {
        this.name = name;
        this.versionX = x;
        this.versionY = y;
        this.versionZ = z;
        this.size = size;
        this.module = module;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getVersionX() {
        return versionX;
    }

    public void setVersionX(int versionX) {
        this.versionX = versionX;
    }

    public int getVersionY() {
        return versionY;
    }

    public void setVersionY(int versionY) {
        this.versionY = versionY;
    }

    public int getVersionZ() {
        return versionZ;
    }

    public void setVersionZ(int versionZ) {
        this.versionZ = versionZ;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }
}
