package com.car.autolink.module.protocal.eightthree.chips;


public class ChipFactory {

    /**
     * get Chip type.
     *
     * @param chipNumber type related number
     * @return true Chip
     */
    public static Chip getChip(int chipNumber) {
        switch (chipNumber) {
            case 1:
                return new Pindola();
            case 2:
                return new <PERSON><PERSON><PERSON>();
            case 3:
                return new <PERSON><PERSON><PERSON>();
            case 0:
            default:
                return null;
        }
    }
}
