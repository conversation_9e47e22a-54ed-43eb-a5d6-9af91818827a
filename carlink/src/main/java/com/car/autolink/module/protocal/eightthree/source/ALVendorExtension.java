package com.car.autolink.module.protocal.eightthree.source;

import com.car.autolink.module.protocal.eightthree.project.VendorExtension;
import com.car.autolink.module.protocal.eightthree.project.VendorExtensionCallbacks;
import com.car.autolink.module.protocal.eightthree.eventcenter.IALEventCenter;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportUploadCarData;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALVendorExtension implements ALServiceBase {

    private final VendorExtension mVendorExtension;
    private final IALEventCenter mALEventCenter;

    ALVendorExtension(IALEventCenter eventCenter) {
        mALEventCenter = eventCenter;
        VendorExtensionCallbacks vendorExtensionCallbacks = (data, len) -> {
            ALReportEvent reportEvent = new ALReportUploadCarData(data, len);
            mALEventCenter.sendEventNonBlock(reportEvent);
            return 0;
        };
        mVendorExtension = new VendorExtension(vendorExtensionCallbacks);
    }

    @Override
    public long getNativeInstance() {
        return mVendorExtension.getNativeInstance();
    }

    @Override
    public void destroy() {
        mVendorExtension.destroy();
    }

    @Override
    public boolean create(int serviceId, long nativeGalReceiver) {
        return mVendorExtension.create(serviceId, nativeGalReceiver);
    }

    @Override
    public boolean start() {
        return false;
    }

    @Override
    public void internalEvent(InternalEvent event) {
    }

    @Override
    public void externalEvent(ALEvent event) {
    }

    @Override
    public int getServiceType() {
        return AL_SERVICE_VENDOR;
    }
}
