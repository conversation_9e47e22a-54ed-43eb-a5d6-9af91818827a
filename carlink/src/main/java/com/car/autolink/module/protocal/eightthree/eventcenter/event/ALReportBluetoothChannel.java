package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2018/06/01
 * desc
 */
public class ALReportBluetoothChannel extends ALReportEvent {

    private final String mAddress;

    public ALReportBluetoothChannel(String address) {
        this.mAddress = address;
    }

    @Override
    public int type() {
        return BT_CHANNEL_OPEN;
    }

    public String getAddress() {
        return mAddress;
    }
}
