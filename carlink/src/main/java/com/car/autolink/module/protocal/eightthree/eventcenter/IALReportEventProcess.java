package com.car.autolink.module.protocal.eightthree.eventcenter;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public interface IALReportEventProcess {
    /**
     * registerListener
     *
     * @param listener {@link Listener}
     */
    void registerListener(Listener listener);

    /**
     * unregisterListener
     *
     * @param listener {@link Listener}
     */
    void unregisterListener(Listener listener);

    /**
     * start
     *
     * @return success or not
     */
    boolean start();

    /**
     * sendReportEvent
     *
     * @param event ALReportEvent
     */
    void sendReportEvent(ALReportEvent event);

    /**
     * sendReportEventNonBlock
     *
     * @param event ALReportEvent
     */
    void sendReportEventNonBlock(ALReportEvent event);

    /**
     * shutdown
     */
    void shutdown();

    interface Listener {
        /**
         * onReportEventProcess
         *
         * @param event {@link ALReportEvent}
         */
        void onReportEventProcess(ALReportEvent event);
    }
}
