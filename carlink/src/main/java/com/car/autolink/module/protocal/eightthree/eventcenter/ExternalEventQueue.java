package com.car.autolink.module.protocal.eightthree.eventcenter;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ExternalEventQueue implements IALExternalEventProcess {

    private static final int EXTERNAL_FLAG = 2;
    private IALExternalEventProcess.Listener mListener;
    private Handler mHandler;
    private HandlerThread mExternalThread;

    ExternalEventQueue() {
        mExternalThread = new HandlerThread("ExternalThread");
        mExternalThread.start();
        mHandler = new Handler(mExternalThread.getLooper(), mCallback);
    }

    @Override
    public void registerListener(Listener listener) {
        mListener = listener;
    }

    @Override
    public void unregisterListener(Listener listener) {
        mListener = null;
    }

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public synchronized void sendExternalEvent(ALEvent event) {
        if (mListener != null) {
            mListener.onExternalEventProcess(event);
        }
    }

    @Override
    public void sendExternalEventNonBlock(ALEvent event) {
        Message msg = new Message();
        msg.what = EXTERNAL_FLAG;
        msg.obj = event;
        if (mHandler != null) {
            mHandler.sendMessage(msg);
        }
    }

    @Override
    public void shutdown() {
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
        mExternalThread = null;
        mCallback = null;
        unregisterListener(mListener);
        mListener = null;
    }

    private Handler.Callback mCallback = msg -> {
        if (msg.what == EXTERNAL_FLAG) {
            mListener.onExternalEventProcess((ALEvent) msg.obj);
        }
        return false;
    };
}
