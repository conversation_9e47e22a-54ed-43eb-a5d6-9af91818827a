package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public abstract class InternalEvent {
    public static final int BASE = 0;
    public static final int ERROR = 1;

    /**
     * getConsignee
     *
     * @return channel id
     */
    public abstract int getConsignee();

    /**
     * getType
     *
     * @return type
     */
    public abstract int getType();
}
