package com.car.autolink.module.transport;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public interface ITransport {
    /**
     * stopTransport.
     */
    void stopTransport();

    /**
     * read data from IO.
     *
     * @param b   buffer
     * @param off offset
     * @param len length
     * @return read bytes
     * @throws IOException IO error
     */
    int read(byte[] b, int off, int len) throws IOException;

    /**
     * write data to IO.
     *
     * @param b   buffer
     * @param off offset
     * @param len length
     * @throws IOException IO error
     */
    void write(byte[] b, int off, int len) throws IOException;

    /**
     * get transport mode.
     *
     * @return wifi or usb
     */
    int getType();
}
