package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2019/7/1
 */
public class ALScreenResolutionEvent extends ALEvent {
    private final int width;
    private final int height;
    private final boolean isRequired;

    /**
     * Constructors ALScreenResolutionEvent.
     *
     * @param width      screen width
     * @param height     screen height
     * @param isRequired request from car
     */
    public ALScreenResolutionEvent(int width, int height, boolean isRequired) {
        this.width = width;
        this.height = height;
        this.isRequired = isRequired;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public boolean isRequired() {
        return isRequired;
    }

    @Override
    public int getEventType() {
        return SCREEN_RESOLUTION;
    }
}
