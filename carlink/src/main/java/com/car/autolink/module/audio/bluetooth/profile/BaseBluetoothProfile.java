package com.car.autolink.module.audio.bluetooth.profile;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.Context;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/9/19
 */
public abstract class BaseBluetoothProfile<T extends BluetoothProfile>
        implements IBluetoothProfile, BluetoothProfile
        .ServiceListener {

    private static final String TAG = "BaseBluetoothProfile";
    static final int PAIRED = 0;
    static final int HEADSET_CONNECTED = 1;
    static final int A2DP_CONNECTED = 2;
    static final int HEALTH_CONNECTED = 4;
    static final int HID_CONNECTED = 8;
    private T mProxy;
    private BluetoothProfileReflect mReflect;

    BaseBluetoothProfile(Context context) {
        BluetoothAdapter.getDefaultAdapter().getProfileProxy(context, this, getProfileCode());
    }

    @Override
    public void onServiceConnected(int profile, BluetoothProfile proxy) {
        try {
            mProxy = (T) proxy;
            mReflect = new BluetoothProfileReflect(mProxy);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onServiceDisconnected(int profile) {
        mProxy = null;
    }

    @Override
    public boolean isAvailable() {
        return mProxy != null;
    }

    @Override
    public boolean connect(BluetoothDevice device) {
        if (isDisabled()) {
            return false;
        }
        return mReflect.connect(device);
    }

    @Override
    public void disconnect(BluetoothDevice device) {
        if (isDisabled()) {
            return;
        }
        mReflect.disconnect(device);
    }

    @Override
    public int getProfileConnectionState(BluetoothDevice device) {
        if (isDisabled()) {
            return BluetoothProfile.STATE_DISCONNECTED;
        }
        return mProxy.getConnectionState(device);
    }

    @Override
    public void close() {
        BluetoothAdapter.getDefaultAdapter().closeProfileProxy(getProfileCode(), mProxy);
    }

    private boolean isDisabled() {
        return mProxy == null || mReflect == null;
    }

    protected static class BluetoothProfileReflect {
        private BluetoothProfile mProfile;
        Class<?> mClass;

        BluetoothProfileReflect(BluetoothProfile profile) throws ClassNotFoundException {
            setProxy(profile);
        }

        final void setProxy(BluetoothProfile profile) throws ClassNotFoundException {
            this.mProfile = profile;
            this.mClass = Class.forName(profile.getClass().getName());
        }

        protected final BluetoothProfile getProxy() {
            return mProfile;
        }

        protected final boolean connect(BluetoothDevice device) {
            Object object = invoke("connect", new Object[]{device}, BluetoothDevice.class);
            return object != null && (boolean) object;
        }

        protected final boolean disconnect(BluetoothDevice device) {
            Object object = invoke("disconnect", new Object[]{device}, BluetoothDevice.class);
            return object != null && ((object instanceof Boolean) ? (Boolean) object : false);
        }

        protected final void disconnectAll() {
            List<BluetoothDevice> devices = getConnectDevices();
            for (BluetoothDevice device : devices) {
                disconnect(device);
            }
        }

        protected final int getConnectionState(BluetoothDevice device) {
            Object object = invoke("getConnectionState", new Object[]{device},
                    BluetoothDevice.class);
            if (object != null) {
                return (object instanceof Integer) ? (Integer) object : -1;
            }
            return -1;
        }

        @SuppressWarnings("unchecked")
        final List<BluetoothDevice> getConnectDevices() {
            Object object = invoke("getConnectedDevices", null);
            if (object != null) {
                return ((ArrayList<BluetoothDevice>) object);
            }
            return new ArrayList<>();
        }

        private Object invoke(String methodName, Object[] objects, Class<?>... parameterTypes) {
            if (mClass == null || mProfile == null) {
                return null;
            }
            try {
                Method method = mClass.getMethod(methodName, parameterTypes);
                if (objects == null || objects.length == 0) {
                    return method.invoke(mProfile, (Object) null);
                }
                return method.invoke(mProfile, objects);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }
}
