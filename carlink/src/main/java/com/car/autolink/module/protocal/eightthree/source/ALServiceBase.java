package com.car.autolink.module.protocal.eightthree.source;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public interface ALServiceBase extends NativeObject {
    int AL_SERVICE_UNKONWN = 0;
    int AL_SERVICE_ROOT = 1;
    int AL_SERVICE_ALL = 2;
    int AL_SERVICE_CONTROL = 3;
    int AL_SERVICE_VIDEO_SOURCE = 4;
    int AL_SERVICE_BLUETOOTH = 5;
    int AL_SERVICE_VENDOR = 6;
    int AL_SERVICE_INPUT_SINK = 7;

    boolean create(int serviceId, long nativeGalReceiver);

    boolean start();

    void internalEvent(InternalEvent event);

    void externalEvent(ALEvent event);

    int getServiceType();
}
