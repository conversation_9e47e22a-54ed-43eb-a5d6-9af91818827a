package com.car.autolink.module.transport.usb;

import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbManager;
import android.os.ParcelFileDescriptor;

import com.car.autolink.module.transport.DataConnection;
import com.car.autolink.module.transport.ITransport;
import com.elvishew.xlog.XLog;

import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public class UsbAccessoryTransport implements ITransport {
    private static final String TAG = "UsbAccessoryTransport";
    private final ParcelFileDescriptor mParcelAccessoryFd;
    private static final int USB_ACCESSORY_BUFFER_SIZE = 16 * 1024;
    private final OutputStream mOutputStream;
    private final InputStream mInputStream;
    private final UsbInputReader.RawUsbInputReader mRawUsbInputReader =
            new UsbInputReader.RawUsbInputReader() {
                @Override
                public int read(byte[] buffer, int byteOffset, int byteCount) throws IOException {
                    return mInputStream.read(buffer, byteOffset, byteCount);
                }
            };
    private final UsbInputReader mUsbInputReader =
            new UsbInputReader(mRawUsbInputReader, USB_ACCESSORY_BUFFER_SIZE);
    private boolean mIsOpened;

    UsbAccessoryTransport(UsbManager manager, UsbAccessory accessory) throws IOException {
        try {
            mParcelAccessoryFd = manager.openAccessory(accessory);
        }catch (IllegalArgumentException e){
            throw new IOException("Unable to open USB accessory");
        }
        if (mParcelAccessoryFd == null) {
            throw new IOException("Unable to open USB accessory");
        }
        FileDescriptor accessoryFd = mParcelAccessoryFd.getFileDescriptor();
        mOutputStream = new FileOutputStream(accessoryFd);
        mInputStream = new FileInputStream(accessoryFd);
        mIsOpened = true;
    }

    @Override
    public void stopTransport() {
        try {
            mInputStream.close();
            mOutputStream.close();
            mParcelAccessoryFd.close();
        } catch (IOException e) {
            XLog.tag(TAG).e("Can't close USB accessory: " + e);
        }
        mIsOpened = false;
    }

    @Override
    public int read(byte[] b, int off, int len) throws IOException {
        if (!mIsOpened) {
            throw new IOException("Connection was closed.");
        }
        return mUsbInputReader.read(b, off, len);
    }

    @Override
    public void write(byte[] b, int off, int len) throws IOException {
        if (!mIsOpened) {
            throw new IOException("Connection was closed.");
        }
        mOutputStream.write(b, off, len);
    }

    @Override
    public int getType() {
        return DataConnection.USB;
    }
}
