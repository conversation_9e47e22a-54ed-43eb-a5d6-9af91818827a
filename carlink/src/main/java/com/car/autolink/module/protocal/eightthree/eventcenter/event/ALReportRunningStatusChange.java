package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALReportRunningStatusChange extends ALReportEvent {
    private final int mStatus;
    public static final int START = 1;
    public static final int STOP = 2;
    public static final int PROBE_START = 3;
    public static final int PROBE_SUCCESS = 4;
    public static final int PROBE_FAIL = 5;
    public static final int VIDEO_ESTABLISHED = 6;
    public static final int BYE_BYE = 7;

    public ALReportRunningStatusChange(int status) {
        mStatus = status;
    }

    @Override
    public int type() {
        return RUNNING_STATE_CHANGE;
    }

    public int getStatus() {
        return mStatus;
    }

}
