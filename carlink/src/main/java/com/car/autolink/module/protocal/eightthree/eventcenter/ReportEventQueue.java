package com.car.autolink.module.protocal.eightthree.eventcenter;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ReportEventQueue implements IALReportEventProcess {

    private static final int REPORT_FLAG = 1;
    private Handler mHandler;
    private IALReportEventProcess.Listener mListener;
    private HandlerThread mReportThread;

    ReportEventQueue() {
        mReportThread = new HandlerThread("ReportThread");
        mReportThread.start();
        mHandler = new Handler(mReportThread.getLooper(), mCallback);
    }

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public synchronized void sendReportEvent(ALReportEvent event) {
        if (mListener != null) {
            mListener.onReportEventProcess(event);
        }
    }

    @Override
    public void sendReportEventNonBlock(ALReportEvent event) {
        Message msg = new Message();
        msg.what = REPORT_FLAG;
        msg.obj = event;
        if (mHandler != null) {
            mHandler.sendMessage(msg);
        }
    }

    @Override
    public void shutdown() {
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
        mReportThread = null;
        mCallback = null;
        unregisterListener(mListener);
        mListener = null;
    }

    @Override
    public void registerListener(Listener listener) {
        mListener = listener;
    }

    @Override
    public void unregisterListener(Listener listener) {
        mListener = null;
    }

    private Handler.Callback mCallback = msg -> {
        if (msg.what == REPORT_FLAG) {
            mListener.onReportEventProcess((ALReportEvent) msg.obj);
        }
        return false;
    };
}
