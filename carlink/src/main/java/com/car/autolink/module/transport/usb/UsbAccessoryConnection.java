package com.car.autolink.module.transport.usb;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbManager;
import android.util.Log;

import com.car.autolink.module.transport.DataConnection;
import com.elvishew.xlog.XLog;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public class UsbAccessoryConnection extends DataConnection {
    private static final String TAG = "UsbAccessoryConnection";
    private final Context mAppContext;
    private final UsbAccessory mAccessory;
    private final BroadcastReceiver mAccessoryDetachedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            XLog.tag(TAG).i("disconnected, finishing");
            shutdown();
        }
    };
    private boolean mIsReceiverRegistered;

    /**
     * Constructors UsbAccessoryConnection.
     *
     * @param context   {@link Context}
     * @param accessory {@link UsbAccessory}
     */
    public UsbAccessoryConnection(Context context, UsbAccessory accessory) {
        mAppContext = context.getApplicationContext();
        mAccessory = accessory;
        mAppContext.registerReceiver(mAccessoryDetachedReceiver,
                new IntentFilter(UsbManager.ACTION_USB_ACCESSORY_DETACHED));
        mIsReceiverRegistered = true;
    }

    @Override
    protected void onStart() throws IOException {
        UsbManager manager = (UsbManager) mAppContext.getSystemService(Context.USB_SERVICE);
        if (manager == null) {
            throw new IOException("Unable to get USB manager");
        }
        UsbAccessoryTransport transport = new UsbAccessoryTransport(manager, mAccessory);
        Log.d(TAG, "Started USB accessory connection");
        mCallback.onConnected(transport);
    }

    @Override
    protected void onShutdown() {
        if (mIsReceiverRegistered) {
            try {
                mAppContext.unregisterReceiver(mAccessoryDetachedReceiver);
            } catch (Exception e) {
                e.printStackTrace();
            }
            mIsReceiverRegistered = false;
        }
    }
}
