package com.car.autolink.module.protocal.eightthree.source;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public interface ITransportChannel {
    /**
     * stopTransport
     */
    void stopTransport();

    /**
     * read data from IO
     *
     * @param buf    buf
     * @param offset offset
     * @param len    len
     * @return read bytes
     * @throws IOException IO error
     */
    int read(byte[] buf, int offset, int len) throws IOException;

    /**
     * write data to IO
     *
     * @param buf    buf
     * @param offset offset
     * @param len    len
     * @throws IOException IO error
     */
    void write(byte[] buf, int offset, int len) throws IOException;
}
