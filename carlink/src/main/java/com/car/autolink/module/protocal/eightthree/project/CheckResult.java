package com.car.autolink.module.protocal.eightthree.project;

public class CheckResult {
    private int moduleId;
    private int failCode;
    private int value;

    /**
     * Constructors CheckResult.
     *
     * @param moduleId moduleId
     * @param failCode failCode
     * @param value    value
     */
    public CheckResult(int moduleId, int failCode, int value) {
        this.moduleId = moduleId;
        this.failCode = failCode;
        this.value = value;
    }

    public int getModuleId() {
        return moduleId;
    }

    public void setModuleId(int moduleId) {
        this.moduleId = moduleId;
    }

    public int getFailCode() {
        return failCode;
    }

    public void setFailCode(int failCode) {
        this.failCode = failCode;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }
}
