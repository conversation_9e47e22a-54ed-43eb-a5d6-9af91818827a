package com.car.autolink.module.protocal.eightthree.source;

import com.car.autolink.config.BluetoothConfig;
import com.car.autolink.module.protocal.eightthree.project.BluetoothCallbacks;
import com.car.autolink.module.protocal.eightthree.project.BluetoothEndpoint;
import com.car.autolink.module.protocal.eightthree.project.Protos;
import com.car.autolink.module.protocal.eightthree.eventcenter.IALEventCenter;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALBluetoothPairingRequest;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportBluetoothChannel;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportBluetoothPairingRequest;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;
import com.elvishew.xlog.XLog;

import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALBluetoothEndpoint implements ALServiceBase {

    private static final String TAG = "ALBluetoothEndpoint";
    private final BluetoothEndpoint mBluetoothEndpoint;
    private final BluetoothConfig mConfig;
    private final IALEventCenter mAlEventCenter;
    private String mCarBluetoothAddress;
    private ScheduledExecutorService mScheduledExecutorService;

    ALBluetoothEndpoint(BluetoothConfig config,
                        IALEventCenter eventCenter) {
        mConfig = config;
        mAlEventCenter = eventCenter;
        BluetoothCallbacks listener = new BluetoothCallbacks() {

            @Override
            public int onChannelOpened() {
                ALReportEvent reportEvent = new ALReportBluetoothChannel(mCarBluetoothAddress);
                mAlEventCenter.sendEventNonBlock(reportEvent);
                return Protos.STATUS_SUCCESS;
            }

            @Override
            public boolean discoverBluetoothService(String carAddress,
                                                    int methods) {
                mCarBluetoothAddress = carAddress;
                return true;
            }

            @Override
            public void onAuthenticationData(String authData) {
                //do nothing
            }

            @Override
            public void onPhoneBluetoothStatusInquire() {
                //do nothing
            }

            @Override
            public void onPairingResponse(int status, boolean alreadyPaired) {
                if (status == Protos.STATUS_SUCCESS) {
                    if (mScheduledExecutorService != null) {
                        mScheduledExecutorService.shutdownNow();
                        mScheduledExecutorService = null;
                    }

                    ALReportEvent reportEvent =
                            new ALReportBluetoothPairingRequest(mCarBluetoothAddress,
                                    alreadyPaired);
                    mAlEventCenter.sendEventNonBlock(reportEvent);
                } else {
                    XLog.tag(TAG).w("car's bluetooth is busy");
                }
            }
        };
        mBluetoothEndpoint = new BluetoothEndpoint(listener);
    }

    void sendBluetoothStatus(int status, boolean unsolicited) {
        if (mBluetoothEndpoint != null) {
            mBluetoothEndpoint.nativeSendBluetoothStatus(status, unsolicited);
        }
    }

    @Override
    public long getNativeInstance() {
        return mBluetoothEndpoint.getNativeInstance();
    }

    @Override
    public void destroy() {
        if (mScheduledExecutorService != null) {
            mScheduledExecutorService.shutdownNow();
            mScheduledExecutorService = null;
        }
        mBluetoothEndpoint.destroy();
    }

    @Override
    public boolean create(int serviceId, long nativeGalReceiver) {
        return mBluetoothEndpoint.create(serviceId, nativeGalReceiver);
    }

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public void internalEvent(InternalEvent event) {
        //do nothing
    }

    @Override
    public void externalEvent(ALEvent event) {
        int type = event.getEventType();
        if (type == ALEvent.BLUETOOTH_PAIRING_REQUEST) {
            ALBluetoothPairingRequest pairingRequest = (ALBluetoothPairingRequest) event;
            mConfig.mAddress = pairingRequest.getAddress();
            if (mScheduledExecutorService == null) {
                mScheduledExecutorService = new ScheduledThreadPoolExecutor(1);
                mScheduledExecutorService.scheduleAtFixedRate(mTimerTask, 0, 3, TimeUnit.SECONDS);
            }
        }
    }

    @Override
    public int getServiceType() {
        return AL_SERVICE_BLUETOOTH;
    }

    private final TimerTask mTimerTask = new TimerTask() {

        @Override
        public void run() {
            mBluetoothEndpoint.nativeSendPairingRequest(mConfig.mAddress,
                    Protos.BLUETOOTH_PAIRING_NUMERIC_COMPARISON);
        }
    };
}
