package com.car.autolink.module.protocal.eightthree.chips;

import com.car.autolink.utils.Platform;
import com.elvishew.xlog.XLog;

import java.util.ArrayList;
import java.util.List;

public class Pindola implements Chip {
    private static final String TAG = "Pindola";
    private String productType;
    private String specialMap;
    private String otpCheck;
    private final List<String> allInfo = new ArrayList<>();

    @Override
    public void addParameter(int number, byte[] data) {
        switch (number) {
            case 0:
                otpCheck = Platform.bytesToOtp(data, 0);
                XLog.tag(TAG).d("readResponseCallback: " + otpCheck);
                break;
            case 1:
                specialMap = Platform.bytesToSpecialMp(data, 0);
                XLog.tag(TAG).d("readResponseCallback: " + specialMap);
                break;
            case 2:
                productType = Platform.bytesToProductType(data, 0);
                XLog.tag(TAG).d("readResponseCallback: " + productType);
                break;
            default:
                break;
        }
    }

    @Override
    public String getParameter(int index) {
        switch (index) {
            case 0:
                return otpCheck;
            case 1:
                return specialMap;
            case 2:
                return productType;
            default:
                return null;
        }
    }

    @Override
    public List<String> getParameter() {
        allInfo.add(otpCheck);
        allInfo.add(specialMap);
        allInfo.add(productType);
        return allInfo;
    }
}
