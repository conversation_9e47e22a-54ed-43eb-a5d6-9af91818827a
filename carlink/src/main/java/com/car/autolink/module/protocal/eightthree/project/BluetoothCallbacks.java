package com.car.autolink.module.protocal.eightthree.project;

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Java perspective of the {@code IBluetoothCallbacks} native interface.
 */
public interface BluetoothCallbacks extends NativeCallback {
    /**
     * onChannelOpened
     *
     * @return 0 or -1
     */
    int onChannelOpened();

    /**
     * discoverBluetoothService
     *
     * @param carAddress    carAddress
     * @param methodsBitmap methodsBitmap
     * @return success or not
     */
    boolean discoverBluetoothService(String carAddress, int methodsBitmap);

    /**
     * onPairingResponse
     *
     * @param status        status
     * @param alreadyPaired alreadyPaired
     */
    void onPairingResponse(int status, boolean alreadyPaired);

    /**
     * onAuthenticationData
     *
     * @param authData authData
     */
    void onAuthenticationData(String authData);

    /**
     * onPhoneBluetoothStatusInquire
     */
    void onPhoneBluetoothStatusInquire();
}
