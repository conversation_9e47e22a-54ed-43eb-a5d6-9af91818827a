package com.car.autolink.module.protocal.eightthree.source;

import android.util.Log;

import com.car.autolink.config.VideoConfig;
import com.car.autolink.config.VideoPackage;
import com.car.autolink.module.protocal.eightthree.eventcenter.IALEventCenter;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportVideoFocusNotification;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportVideoSourceChannel;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALVideoFocusRequestEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALVideoPermissionEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;
import com.car.autolink.module.protocal.eightthree.project.Protos;
import com.car.autolink.module.protocal.eightthree.project.VideoSource;
import com.car.autolink.module.protocal.eightthree.project.VideoSourceCallbacks;
import com.elvishew.xlog.XLog;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALVideoSource implements ALServiceBase {

    private static class VideoRemoteConfig {
        int codec;
        int frameRate;
        int width;
        int height;

        VideoRemoteConfig(int codec, int frameRate, int width, int height) {
            this.codec = codec;
            this.frameRate = frameRate;
            this.width = width;
            this.height = height;
        }

        public void setWidth(int width) {
            this.width = width;
        }

        public void setHeight(int height) {
            this.height = height;
        }
    }

    private static final int PERMISSION_UNKNOWN = 0;
    private static final int PERMISSION_YES = 1;
    private static final int PERMISSION_NO = 2;
    private static final String TAG = ALVideoSource.class.getSimpleName();
    private final IVideoPlayerBase mPlayer;
    private final IALEventCenter mALEventCenter;
    private final VideoSource mSource;
    private int mCurrentSession;
    private int mFocus;
    private int mPermitStatus;
    private final ArrayList<VideoRemoteConfig> mRemoteConfigs = new ArrayList<>();
    private int mCurrentConfigIdx;
    private boolean mOpen = false;
    private VideoRemoteConfig mVideoRemoteConfig;
    private boolean mIsLandscape = false;


    ALVideoSource(IVideoPlayerBase player, IALEventCenter eventCenter,
                  boolean autoStart, String remoteHost) {

        mPlayer = player;
        mALEventCenter = eventCenter;
        mFocus = VideoSource.VideoFocusMode.VIDEO_FOCUS_NATIVE;
        mCurrentSession = -1;
        mCurrentConfigIdx = -1;
        mPermitStatus = PERMISSION_NO;
        VideoSourceCallbacks listener = new VideoSourceCallbacks() {

            @Override
            public int videoFocusNotifCallback(int focus, boolean unsolicited) {
                ALReportEvent reportEvent = new ALReportVideoFocusNotification(focus, unsolicited);
                mALEventCenter.sendEventNonBlock(reportEvent);
                if (mFocus == focus) {
                    return Protos.STATUS_SUCCESS;
                }
                mFocus = focus;
                if (focus == Protos.VIDEO_FOCUS_PROJECTED) {
                    if (mCurrentConfigIdx < 0) {
                        mSource.sendVideoFocusRequestNotifi(0, Protos.VIDEO_FOCUS_NATIVE,
                                Protos.NO_VALID_VIDEO_ENCODER);
                    } else if (mPermitStatus == PERMISSION_UNKNOWN) {
                        mSource.sendVideoFocusRequestNotifi(0, Protos.VIDEO_FOCUS_NATIVE,
                                Protos.WAIT_PERMISSION);
                    } else if (mPermitStatus == PERMISSION_NO) {
                        mSource.sendVideoFocusRequestNotifi(0, Protos.VIDEO_FOCUS_NATIVE,
                                Protos.NO_PERMISSION);
                    } else {
                        startVideoSession(mIsLandscape);
                    }
                } else {
                    if (mOpen) {
                        stopVideoSession();
                    }
                }
                return Protos.STATUS_SUCCESS;
            }

            @Override
            public int displayChangeCallback(int width, int height, boolean isLandscape,
                                             int density) {
                XLog.tag(TAG).d("displayChangeCallback width = %d, height = %d, isLandscape = %b, density = %d",
                        width, height, isLandscape, density);
                mIsLandscape = isLandscape;
                mSource.sendDisplayAreaChangeResponse();
                return Protos.STATUS_SUCCESS;
            }

            @Override
            public int onChannelOpened() {
                Log.d(TAG, "onChannelOpened");
                if (mPermitStatus == PERMISSION_YES) {
                    mSource.sendSetup(Protos.MEDIA_CODEC_VIDEO_H264_BP);
                } else {
                    ALReportEvent reportEvent = new ALReportVideoSourceChannel();
                    mALEventCenter.sendEventNonBlock(reportEvent);
                }
                return Protos.STATUS_SUCCESS;
            }

            @Override
            public boolean discoverVideoConfigCallback(int codec, int fps, int w, int h) {
                VideoRemoteConfig config = new VideoRemoteConfig(codec, fps, w, h);
                mRemoteConfigs.add(config);
                return isSupportConfig(config);
            }

            @Override
            public int startResponseCallback(boolean isOK) {
                mPlayer.start();
                mOpen = true;
                return 0;
            }

            @Override
            public int stopResponseCallback() {
                return 0;
            }

            @Override
            public int configCallback(int status, int maxUnack, int[] prefer, int size) {
                for (int i = 0; i < size; i++) {
                    if (isSupportConfig(mRemoteConfigs.get(prefer[i]))) {
                        mCurrentConfigIdx = prefer[i];
                        break;
                    }
                }
                if (mCurrentConfigIdx < 0) {
                    for (int i = 0; i < mRemoteConfigs.size(); i++) {
                        if (isSupportConfig(mRemoteConfigs.get(i))) {
                            mCurrentConfigIdx = i;
                            break;
                        }
                    }
                }
                mVideoRemoteConfig = mRemoteConfigs.get(mCurrentConfigIdx);
                mIsLandscape = mVideoRemoteConfig.width > mVideoRemoteConfig.height;
                if (mCurrentConfigIdx >= 0 && mFocus == Protos.VIDEO_FOCUS_PROJECTED) {
                    if (status == Protos.Config_Status_STATUS_READY) {
                        startVideoSession(mIsLandscape);
                    } else {
                        stopVideoSession();
                    }
                }
                return Protos.STATUS_SUCCESS;
            }

            @Override
            public int ackCallback(int sessionId, int numFrames) {
                return Protos.STATUS_SUCCESS;
            }
        };
        mSource = new VideoSource(listener, autoStart, remoteHost);
    }

    @Override
    public long getNativeInstance() {
        return mSource.getNativeInstance();
    }

    @Override
    public void destroy() {
        mOpen = false;
        mSource.destroy();
    }

    @Override
    public boolean create(int serviceId, long nativeGalReceiver) {
        return mSource.create(serviceId, nativeGalReceiver);
    }

    @Override
    public boolean start() {
        return true;
    }

    public void sendData(VideoPackage pkg) {
        mSource.sendData(System.currentTimeMillis(), pkg.getData(), pkg.getSize(), pkg.getFlags());
    }

    public void stop() {
        stopVideoSession();
    }

    @Override
    public void internalEvent(InternalEvent event) {
        //do nothing
    }

    @Override
    public void externalEvent(ALEvent event) {
        int type = event.getEventType();
        switch (type) {
            case ALEvent.VIDEO_FOCUS_REQUEST:
                ALVideoFocusRequestEvent focusEvent = (ALVideoFocusRequestEvent) event;
                int dispChannel = focusEvent.getDispChannel();
                int focus = focusEvent.getFocus();
                int reason = focusEvent.getReason();
                if (mPermitStatus == ALVideoSource.PERMISSION_YES) {
                    mSource.sendVideoFocusRequestNotifi(dispChannel, focus, reason);
                }
                break;

            case ALEvent.VIDEO_PERMISSION:
                ALVideoPermissionEvent permit = (ALVideoPermissionEvent) event;
                mPermitStatus = (permit.getStatus()) ? (PERMISSION_YES) : (PERMISSION_NO);
                if (mPermitStatus == ALVideoSource.PERMISSION_YES) {
                    mSource.sendSetup(Protos.MEDIA_CODEC_VIDEO_H264_BP);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public int getServiceType() {
        return AL_SERVICE_VIDEO_SOURCE;
    }

    private void startVideoSession(boolean isLandscape) {
        if (mOpen) {
            stopVideoSession();
        }

        int session = ++mCurrentSession;
        int width = mVideoRemoteConfig.width;
        int height = mVideoRemoteConfig.height;
        if (width > height && !isLandscape || width < height && isLandscape) {
            mVideoRemoteConfig.setWidth(height);
            mVideoRemoteConfig.setHeight(width);
        }
        VideoConfig videoConfig = new VideoConfig(mVideoRemoteConfig.width,
                mVideoRemoteConfig.height, mVideoRemoteConfig.codec,
                mVideoRemoteConfig.frameRate);
        mPlayer.init(videoConfig);
        XLog.tag(TAG).d("startVideoSession: width:" + videoConfig.getWidth() + "height:" +
                videoConfig.getHeight() + "isLandscape:" + isLandscape);
        mSource.sendStart(mCurrentConfigIdx, session, videoConfig.getWidth(),
                videoConfig.getHeight());
        if (!mSource.isStartResponseMessageExist()) {
            mPlayer.start();
            mOpen = true;
        }
    }

    private void stopVideoSession() {
        mOpen = false;
        mCurrentSession = -1;
        mPlayer.stop();
        mPlayer.release();
        mSource.sendStop();
    }

    void exitVideoSession() {
        mOpen = false;
        mCurrentSession = -1;
        mPlayer.stop();
        mPlayer.release();
    }

    private boolean isSupportConfig(VideoRemoteConfig config) {
        return config.codec == Protos.MEDIA_CODEC_VIDEO_H264_BP;
    }
}
