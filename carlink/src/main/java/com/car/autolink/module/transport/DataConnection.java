package com.car.autolink.module.transport;

import com.elvishew.xlog.XLog;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public abstract class DataConnection {
    public static final int USB = 1;
    public static final int WIFI_P2P = 2;
    private static final String TAG = "DataConnection";

    public interface Callback {
        void onConnected(ITransport transport);

        void onDisconnected();
    }

    protected Callback mCallback;
    private boolean mIsInitialized;

    protected DataConnection() {
        mIsInitialized = true;
    }

    /**
     * shutdown DataConnection.
     */
    public void shutdown() {
        if (mIsInitialized) {
            XLog.tag(TAG).d("Shutting down...");
            mIsInitialized = false;
            if (mCallback != null) {
                mCallback.onDisconnected();
                mCallback = null;
            }
            onShutdown();
            XLog.tag(TAG).d("Shutdown completed.");
        }
    }

    /**
     * start DataConnection.
     *
     * @param callback {@link Callback}
     * @throws IOException may be error {@link IOException}
     */
    public void start(Callback callback) throws IOException {
        XLog.tag(TAG).d("Starting...");
        mCallback = callback;
        onStart();
        XLog.tag(TAG).d("Start sequence finished.");
    }

    protected abstract void onStart() throws IOException;

    protected abstract void onShutdown();


}
