package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALVideoFocusRequestEvent extends ALEvent {
    private final int mFocus;
    private final int mReason;
    private final int mDispChannel;

    /**
     * Constructors ALVideoFocusRequestEvent.
     *
     * @param focus  native or project
     * @param reason reason for focus change
     */
    public ALVideoFocusRequestEvent(int focus, int reason) {
        mDispChannel = 0;
        mFocus = focus;
        mReason = reason;
    }

    @Override
    public int getEventType() {
        return VIDEO_FOCUS_REQUEST;
    }

    public int getFocus() {
        return mFocus;
    }

    public int getDispChannel() {
        return mDispChannel;
    }

    public int getReason() {
        return mReason;
    }
}
