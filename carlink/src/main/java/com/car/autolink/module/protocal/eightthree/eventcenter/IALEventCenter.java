package com.car.autolink.module.protocal.eightthree.eventcenter;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.ALReportEvent;
import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public interface IALEventCenter {

    /**
     * registerListener
     *
     * @param listener {@link Listener}
     */
    void registerListener(Listener listener);

    /**
     * unregisterListener
     *
     * @param listener {@link Listener}
     */
    void unregisterListener(Listener listener);

    /**
     * release module
     */
    void release();

    /**
     * start
     *
     * @return 0 or -1
     */
    int start();

    /**
     * shutdown
     *
     * @return 0 or -1
     */
    int shutdown();

    /**
     * sendEvent
     *
     * @param event InternalEvent
     */
    void sendEvent(InternalEvent event);

    /**
     * sendEvent
     *
     * @param event ALReportEvent
     */
    void sendEvent(ALReportEvent event);

    /**
     * sendEvent
     *
     * @param event ALEvent
     */
    void sendEvent(ALEvent event);

    /**
     * sendEventNonBlock
     *
     * @param event InternalEvent
     */
    void sendEventNonBlock(InternalEvent event);

    /**
     * sendEventNonBlock
     *
     * @param event ALReportEvent
     */
    void sendEventNonBlock(ALReportEvent event);

    /**
     * sendEventNonBlock
     *
     * @param event ALEvent
     */
    void sendEventNonBlock(ALEvent event);

    interface Listener {
        /**
         * process InternalEvent
         *
         * @param event InternalEvent
         */
        void process(InternalEvent event);

        /**
         * process ALEvent
         *
         * @param event ALEvent
         */
        void process(ALEvent event);

        /**
         * process ALReportEvent
         *
         * @param event ALReportEvent
         */
        void process(ALReportEvent event);
    }
}
