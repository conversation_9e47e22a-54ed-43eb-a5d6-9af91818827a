package com.car.autolink.module.protocal.eightthree.project;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public interface UpgradeChannelCallbacks extends NativeCallback {
    void onChannelOpened(String ic, String build);

    void compareServerVersion(ArrayList<FileInfo> list);

    void downladServerFile(ArrayList<FileInfo> list);

    void getData(String name, int curX, int curY, int curZ, int offset, int len);

    void checksum(String name, int curX, int curY, int curZ, int checksum);

    void transportStart();

    void transportComplete(boolean isSuccess);
}
