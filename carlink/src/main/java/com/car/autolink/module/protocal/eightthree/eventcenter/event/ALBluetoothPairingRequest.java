package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2018/06/01
 * desc
 */
public class ALBluetoothPairingRequest extends ALEvent {

    private final String address;


    public ALBluetoothPairingRequest(String address) {
        this.address = address;
    }

    @Override
    public int getEventType() {
        return BLUETOOTH_PAIRING_REQUEST;
    }

    public String getAddress() {
        return address;
    }
}
