package com.car.autolink.module.audio.bluetooth.profile;

import android.bluetooth.BluetoothA2dp;
import android.bluetooth.BluetoothProfile;
import android.content.Context;

/**
 * <AUTHOR>
 * @date 2018/9/19
 */
public class BluetoothA2dpProfile extends BaseBluetoothProfile<BluetoothA2dp> {
    BluetoothA2dpProfile(Context context) {
        super(context);
    }

    @Override
    public int getProfileCode() {
        return BluetoothProfile.A2DP;
    }
}
