package com.car.autolink.module.protocal.eightthree.source;

import android.os.Process;
import android.os.Trace;

import com.elvishew.xlog.XLog;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Input/output processing for GAL.
 */
public class ALTransport {
    private static final String TAG = "InputOutputThreads";
    private static final boolean DBG_TRACE = false;
    private static final boolean VERBOSE_LOGGING_ENABLED = false;
    private static final int MAX_FRAME_SIZE = 4 + 4 + 65536;
    private static final int READER_THREAD_PRIORITY = Process.THREAD_PRIORITY_URGENT_AUDIO;
    private static final int WRITER_THREAD_PRIORITY = Process.THREAD_PRIORITY_URGENT_AUDIO;

    public interface FrameParser {
        int getFrameSizeToRead(byte[] header, int length);

        void enqueueIncomingFrame(byte[] frame, int length);

        int dequeueOutgoingFrame(byte[] buffer);

        void readerTerminated();

        void onIoError();
    }

    private final AtomicBoolean mStopRequested = new AtomicBoolean(false);
    private final FrameParser mFrameParser;
    private ITransportChannel mTransport;

    public ALTransport(FrameParser frameParser) {
        mFrameParser = frameParser;
    }

    /**
     * start usb or wifi stream.
     *
     * @param transport {@link ITransportChannel}
     */
    public void start(ITransportChannel transport) {
        mTransport = transport;
        mStopRequested.set(false);
        mReaderThread.start();
        mWriterThread.start();
    }

    /**
     * stop usb or wifi stream.
     */
    public void stop() {
        if (requestStop() && mTransport != null) {
            mTransport.stopTransport();
            mTransport = null;
        }
    }

    private boolean isStopRequested() {
        return !mStopRequested.get();
    }

    private boolean requestStop() {
        return !mStopRequested.getAndSet(true);
    }

    private String hexdumpBuffer(byte[] buf, int len) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < len; i++) {
            sb.append(String.format(" %02x", buf[i]));
            if (i == 32) {
                sb.append("...");
                break;
            }
        }
        return sb.toString();
    }

    private final Thread mReaderThread = new Thread("ReaderThread") {
        private int read(byte[] buf, int offset, int len) {
            if (DBG_TRACE) {
                Trace.beginSection("read");
            }
            int bytesRead = 0;
            while (bytesRead < len && isStopRequested()) {
                try {
                    int nbytes = mTransport.read(buf, offset + bytesRead, len - bytesRead);
                    if (nbytes > 0) {
                        bytesRead += nbytes;
                    } else {
                        throw new IOException("the peer has performed an orderly shutdown");
                    }
                } catch (IOException e) {
                    XLog.tag(TAG).e("Caught exception on read. Exiting." + e);
                    ALTransport.this.stop();
                    mFrameParser.onIoError();
                    break;
                }
            }
            if (DBG_TRACE) {
                Trace.endSection();
            }
            return bytesRead;
        }

        private static final int HEADER_READ_LENGTH = 4;

        @Override
        public void run() {
            Process.setThreadPriority(READER_THREAD_PRIORITY);
            byte[] buf = new byte[MAX_FRAME_SIZE];
            while (isStopRequested()) {
                if (DBG_TRACE) {
                    Trace.beginSection("readerThread");
                }
                int bytesRead = read(buf, 0, HEADER_READ_LENGTH);
                if (bytesRead != HEADER_READ_LENGTH) {
                    XLog.tag(TAG).e("read returned " + bytesRead + " while expecting " + HEADER_READ_LENGTH);
                    break;
                }
                int len = mFrameParser.getFrameSizeToRead(buf, HEADER_READ_LENGTH);
                bytesRead = read(buf, HEADER_READ_LENGTH, len);
                if (bytesRead != len) {
                    XLog.tag(TAG).e("read returned " + bytesRead + " while expecting " + len);
                    break;
                }

                if (VERBOSE_LOGGING_ENABLED) {
                    XLog.tag(TAG).d("Got frame:" + hexdumpBuffer(buf, len + HEADER_READ_LENGTH));
                }

                if (isStopRequested()) {
                    mFrameParser.enqueueIncomingFrame(buf, len + HEADER_READ_LENGTH);
                }
                if (DBG_TRACE) {
                    Trace.endSection();
                }
            }
            mFrameParser.readerTerminated();
        }
    };

    private final Thread mWriterThread = new Thread("WriterThread") {
        @Override
        public void run() {
            Process.setThreadPriority(WRITER_THREAD_PRIORITY);
            byte[] buf = new byte[MAX_FRAME_SIZE];
            try {
                while (isStopRequested()) {
                    if (DBG_TRACE) {
                        Trace.beginSection("writerThread");
                    }
                    int len = mFrameParser.dequeueOutgoingFrame(buf);
                    if (len == 0) {
                        XLog.tag(TAG).e("Writer thread shutting down.");
                        break;
                    }
                    if (VERBOSE_LOGGING_ENABLED) {
                        XLog.tag(TAG).d("Sending frame:" + hexdumpBuffer(buf, len));
                    }
                    if (DBG_TRACE) {
                        Trace.beginSection("write");
                    }

                    final int retryMax = 1;
                    int retryCount = 0;
                    while (retryCount < retryMax && isStopRequested()) {
                        try {
                            mTransport.write(buf, 0, len);
                            break;
                        } catch (IOException e) {
                            e.printStackTrace();
                            retryCount++;
                        }
                    }
                    if (retryCount >= retryMax) {
                        throw new IOException("max retry reached");
                    }
                    if (DBG_TRACE) {
                        // for both writerThread and write
                        Trace.endSection();
                        Trace.endSection();
                    }
                }
            } catch (IOException e) {
                XLog.tag(TAG).e("Caught exception on write. Exiting." + e);
                ALTransport.this.stop();
                mFrameParser.onIoError();
            }
        }
    };
}
