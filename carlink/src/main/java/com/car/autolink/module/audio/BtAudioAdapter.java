package com.car.autolink.module.audio;

import android.Manifest;
import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.provider.Settings;

import androidx.core.app.ActivityCompat;

import com.car.autolink.base.BaseAudio;
import com.car.autolink.module.audio.bluetooth.BluetoothConnectManager;
import com.car.autolink.module.audio.bluetooth.profile.BluetoothHidProfile;
import com.car.autolink.module.audio.bluetooth.profile.Profile;
import com.elvishew.xlog.XLog;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
@SuppressWarnings("MissingPermission")
public class BtAudioAdapter extends BaseAudio {
    private static final String TAG = "BtAudioAdapter";
    private final BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
    private BluetoothDevice mRemoteDevice;
    private final Context mContext;
    private final BluetoothConnectManager mBluetoothConnectManager;
    private BluetoothHidProfile mBluetoothHidProfile;

    /**
     * constructor.
     *
     * @param context {@link Context}
     */
    public BtAudioAdapter(Context context) {
        mContext = context;
        mBluetoothConnectManager = new BluetoothConnectManager(context);
        mBluetoothHidProfile = new BluetoothHidProfile(mContext);
        mBluetoothConnectManager.setBluetoothCallback(new BluetoothConnectManager.BluetoothCallback() {
            @Override
            public void onBluetoothStateChange(int state) {
                if (state == BluetoothAdapter.STATE_ON) {
                    pair(mRemoteDevice);
                }
            }

            @Override
            public void onBondedStateChanged(BluetoothDevice device, int state) {
                XLog.tag(TAG).d("bondState:" + state);
                if (device != null
                        && mRemoteDevice != null
                        && device.getAddress().equals(mRemoteDevice.getAddress())
                        && state == BluetoothDevice.BOND_BONDED) {
                    connectA2dpProfile(device);
                }
            }

            @Override
            public void onA2dpConnectedStateChanged(BluetoothDevice device, int state) {
                XLog.tag(TAG).d("A2dp ConnectState:" + state);
                if (state == BluetoothProfile.STATE_DISCONNECTED) {
                    if (mCallback != null) {
                        mCallback.onParingRequest(getAddress());
                    }
                }
            }

            @Override
            public void onHidHostConnectedStateChanged(BluetoothDevice device, int state) {
                XLog.tag(TAG).d("Hid host ConnectState:" + state);
            }
        });
    }

    @Override
    public void setAudioConfig(String address, boolean alreadyPaired) {
        pairToRemote(address);
    }

    @Override
    public void connectHid() {
//        if (mRemoteDevice != null && isHidDisConnected() && mBluetoothHidProfile != null) {
//            mBluetoothHidProfile.connect(mRemoteDevice);
//        }
    }

    @Override
    public void disconnectHid() {
//        if (mRemoteDevice != null && !isHidDisConnected() && mBluetoothHidProfile != null) {
//            mBluetoothHidProfile.disconnect(mRemoteDevice);
//        }
    }

    private void pairToRemote(String address) {
        if (BluetoothAdapter.checkBluetoothAddress(address)) {
            mRemoteDevice = mBluetoothAdapter.getRemoteDevice(address);
            if (!isBluetoothConnectPermission()) {
                return;
            }
            int state = mRemoteDevice.getBondState();
            if (state == BluetoothDevice.BOND_BONDED) {
                connectA2dpProfile(mRemoteDevice);
            } else if (state == BluetoothDevice.BOND_NONE
                    && !mBluetoothConnectManager.bindDevice(mRemoteDevice)) {
                XLog.tag(TAG).w("Error while pairing with device");
            }
        } else {
            XLog.tag(TAG).d("valid bt mAddress:" + address);
        }
    }

    private void pair(BluetoothDevice device) {
        if (!isBluetoothScanPermission()) {
            return;
        }
        if (mBluetoothAdapter.isDiscovering()) {
            mBluetoothAdapter.cancelDiscovery();
        }
        if (device == null) {
            XLog.tag(TAG).e("remote device is null!");
            return;
        }
        int state = device.getBondState();
        switch (state) {
            case BluetoothDevice.BOND_BONDED:
                //fix for 先锋KB010、KB011机型
                if ("Pioneer".equalsIgnoreCase(mCallback.requestPlatformInfo())) {
                    if (mCallback != null) {
                        mCallback.onParingRequest(getAddress());
                    }
                    return;
                }

                if (!isA2dpConnected(device)) {
                    if (mCallback != null) {
                        mCallback.onParingRequest(getAddress());
                    }
                }
                break;
            case BluetoothDevice.BOND_NONE:
                if (mCallback != null) {
                    mCallback.onParingRequest(getAddress());
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void stop() {
        //do nothing
    }

    @Override
    protected void onStart(String address) {
        if (BluetoothAdapter.checkBluetoothAddress(address)) {
            mRemoteDevice = mBluetoothAdapter.getRemoteDevice(address);
        } else {
            XLog.tag(TAG).e("invalid address:" + address);
        }

        if (!mBluetoothAdapter.isEnabled()) {
            if (!isBluetoothConnectPermission()) {
                return;
            }
            if (!mBluetoothAdapter.enable()) {
                mCallback.requestBtPermission();
            }
        } else {
            pair(mRemoteDevice);
        }
    }

    @Override
    protected void onStop() {
        //do nothing
    }

    @Override
    protected void onShutdown() {
        if (mBluetoothHidProfile != null) {
            mBluetoothHidProfile.close();
            mBluetoothHidProfile = null;
        }
        mBluetoothConnectManager.destroy();
    }

    @SuppressLint("HardwareIds")
    @Override
    public String getAddress() {
        String address = getBluetoothAddressSdk23(mBluetoothAdapter);
        try {
            if (address == null) {
                address = Settings.Secure.getString(mContext.getContentResolver(), "bluetooth_address");
            }

            if (address == null) {
                if (isBluetoothConnectPermission()) {
                    address = mBluetoothAdapter.getAddress();
                }
            }
        } catch (Exception e) {
            address = "02:00:00:00:00:00";
            e.printStackTrace();
        }
        if (address == null) {
            address = "02:00:00:00:00:00";
        }
        XLog.tag(TAG).d(address);
        return address;
    }

    @SuppressLint("HardwareIds")
    private String getBluetoothAddressSdk23(BluetoothAdapter adapter) {
        if (adapter == null) {
            return null;
        }

        try {
            Field serviceField = adapter.getClass().getDeclaredField("mService");
            serviceField.setAccessible(true);
            Object btManagerService = serviceField.get(adapter);
            if (btManagerService != null) {
                return (String) btManagerService.getClass()
                        .getMethod("getAddress")
                        .invoke(btManagerService);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    private void connectA2dpProfile(BluetoothDevice device) {
        if (isBluetoothConnectPermission()) {
            if (mBluetoothConnectManager.getProfileConnectionState(device, Profile.A2dp)
                    == BluetoothProfile.STATE_DISCONNECTED) {
                mBluetoothConnectManager.connectProfile(device, Profile.A2dp);
            }
        }
    }

    private boolean isA2dpConnected(BluetoothDevice device) {
        if (isBluetoothConnectPermission()) {
            return mBluetoothConnectManager.getProfileConnectionState(device, Profile.A2dp)
                    != BluetoothProfile.STATE_DISCONNECTED;
        } else {
            return false;
        }
    }


    private boolean isHidDisConnected() {
        if (isBluetoothConnectPermission()) {
            return mBluetoothAdapter.getProfileConnectionState(4) ==
                    BluetoothAdapter.STATE_DISCONNECTED;
        } else {
            return false;
        }
    }

    private boolean isBluetoothConnectPermission() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            return true;
        }
        return ActivityCompat.checkSelfPermission(mContext,
                Manifest.permission.BLUETOOTH_CONNECT) ==
                PackageManager.PERMISSION_GRANTED;
    }

    private boolean isBluetoothScanPermission() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            return true;
        }
        return ActivityCompat.checkSelfPermission(mContext,
                Manifest.permission.BLUETOOTH_SCAN) ==
                PackageManager.PERMISSION_GRANTED;
    }
}
