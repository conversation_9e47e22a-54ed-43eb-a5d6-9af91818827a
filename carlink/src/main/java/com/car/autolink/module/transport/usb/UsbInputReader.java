package com.car.autolink.module.transport.usb;

import android.util.Log;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
public class UsbInputReader {
    private static final String TAG = "UsbInputReader";
    private final int mMinBufferLength;
    private final byte[] mBufferFragment;
    private int mFragBufferEndPosition = 0;
    private int mFragBufferStartPosition = 0;
    private final RawUsbInputReader mUsbInputReader;

    public interface RawUsbInputReader {
        int read(byte[] buffer, int byteOffset, int byteCount) throws IOException;
    }

    UsbInputReader(RawUsbInputReader usbInputReader, int minReadLength) {
        mUsbInputReader = usbInputReader;
        mMinBufferLength = minReadLength;
        mBufferFragment = new byte[2 * minReadLength];
    }

    /**
     * read data from stream.
     *
     * @param buffer     buffer
     * @param byteOffset byteOffset
     * @param byteCount  byteCount
     * @return byte len
     * @throws IOException IOException
     */
    public int read(byte[] buffer, int byteOffset, int byteCount) throws IOException {
        int leftInBuffer = mFragBufferEndPosition - mFragBufferStartPosition;
        int leftToRead = byteCount;
        if (leftInBuffer > 0) {
            System.arraycopy(
                    mBufferFragment, mFragBufferStartPosition, buffer, byteOffset,
                    Math.min(byteCount, leftInBuffer));
            if (leftInBuffer >= byteCount) {
                mFragBufferStartPosition += byteCount;
                return byteCount;
            }
            leftToRead -= leftInBuffer;
            byteOffset += leftInBuffer;
        }
        mFragBufferEndPosition = 0;
        mFragBufferStartPosition = 0;
        Thread currentThread = Thread.currentThread();
        while (leftToRead > mMinBufferLength) {
            if (currentThread.isInterrupted()) {
                throw new IOException("interrupted");
            }
            int read = mUsbInputReader.read(buffer, byteOffset, leftToRead);
            if (read > 0) {
                byteOffset += read;
                leftToRead -= read;
            } else if (read < 0) {
                throw new IOException("read failed " + read);
            }
        }
        while (leftToRead > mFragBufferEndPosition) {
            if (currentThread.isInterrupted()) {
                throw new IOException("interrupted");
            }
            int read = mUsbInputReader.read(mBufferFragment, mFragBufferEndPosition,
                    mMinBufferLength);
            if (read > 0) {
                mFragBufferEndPosition += read;
            } else if (read < 0) {
                throw new IOException("read failed " + read);
            } else {
                Log.wtf(TAG, "read data return 0");
            }
        }
        System.arraycopy(mBufferFragment, 0, buffer, byteOffset, leftToRead);
        mFragBufferStartPosition += leftToRead;
        return byteCount;
    }
}
