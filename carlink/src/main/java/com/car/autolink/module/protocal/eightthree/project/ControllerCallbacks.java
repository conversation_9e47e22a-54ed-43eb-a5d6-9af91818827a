package com.car.autolink.module.protocal.eightthree.project;

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Java perspective of the {@code IControllerCallbacks} native interface.
 */
/* package private */interface ControllerCallbacks extends NativeCallback {
    /**
     * Called when an unrecoverable error has been encountered.
     *
     * @param err err
     */
    void unrecoverableErrorCallback(int err);

    /**
     * if auth complete, this callback will be called.
     */
    void authCompleteCallback();

    /**
     * Called when the other end pings us.
     *
     * @param timestamp timestamp
     * @param bugReport bugReport
     */
    void pingRequestCallback(long timestamp,
                             boolean bugReport);

    /**
     * Called when the other end responds to our ping.
     *
     * @param timestamp timestamp
     */
    void pingResponseCallback(long timestamp);

    /**
     * Called when a navigation focus request is received from the phone. You
     * must respond to this by calling Controller::setNavigationFocus() after
     * stopping all native turn by turn guidance.
     *
     * @param type type
     */
    void navigationFocusCallback(int type);

    /**
     * Called when ByeByeRequest is received from phone. After taking necessary
     * steps, car side should send ByeByeResponse.
     *
     * @param reason The reason for the disconnection request.
     */
    void byeByeRequestCallback(int reason);

    /**
     * Called when ByeByeResponse is received from phone. Normally this is a
     * reply for ByeByeRequest message sent from car.
     */
    void byeByeResponseCallback();

    /**
     * Called when ExitRequest is received from phone. Normally this is a
     * reply for ExitRequest message sent from car.
     */
    void exitRequestCallback();

    /**
     * Called when the source wishes to acquire audio focus from JNI.
     *
     * @param request     Can be one of AUDIO_FOCUS_GAIN, AUDIO_FOCUS_GAIN_TRANSIENT,
     *                    AUDIO_FOCUS_GAIN_TRANSIENT_MAY_DUCK, AUDIO_FOCUS_RELEASE.
     * @param unsolicited unsolicited
     */
    void audioFocusNotificationCallback(int request,
                                        boolean unsolicited);

    /**
     * Called when the source wishes to force landscape.
     *
     * @param force force landscape or not
     */
    void forceLandscapeRequestCallback(boolean force);

    /**
     * Called when the source wishes acquire screenOrientation.
     */
    void screenOrientationInquire();

    /**
     * Get Car protocol version
     *
     * @param major major
     * @param minor minor
     */
    void versionResponseCallback(short major,
                                 short minor);

    /**
     * Get car information.
     *
     * @param id          id
     * @param make        make
     * @param model       model
     * @param year        year
     * @param huIc        huIc
     * @param huMake      huMake
     * @param huModel     huModel
     * @param huSwBuild   huSwBuild
     * @param huSwVersion huSwVersion
     * @param huSeries    huSeries
     * @param huMuVersion huMuVersion
     * @param checkSum    checkSum
     */
    void serviceDiscoveryResponseCallback(String id,
                                          String make,
                                          String model,
                                          String year,
                                          String huIc,
                                          String huMake,
                                          String huModel,
                                          String huSwBuild,
                                          String huSwVersion,
                                          String huSeries,
                                          String huMuVersion,
                                          int checkSum);

    /**
     * Change phone to auto rotation mode.
     *
     * @param autoed autoed
     */
    void autoRotationRequest(boolean autoed);

    /**
     * Read some data from car.
     *
     * @param data data
     */
    void readResponseCallback(byte[] data);

    /**
     * Write some data to car.
     *
     * @param isOk isOK
     */
    void writeResponseCallback(boolean isOk);

    /**
     * Get phone screen resolution.
     */
    void screenResolutionInquire();

    /**
     * Get phone current time.
     */
    void timeDateInquire();

    /**
     * Check product result.
     *
     * @param checkResult {@link CheckResult}
     */
    void checkProductResultCallBack(CheckResult checkResult);

    void wlTouchNotification(int x, int y, int action, long timestamp);
}
