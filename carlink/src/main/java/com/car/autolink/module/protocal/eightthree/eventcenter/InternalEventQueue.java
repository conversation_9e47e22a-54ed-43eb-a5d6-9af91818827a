package com.car.autolink.module.protocal.eightthree.eventcenter;

import com.car.autolink.module.protocal.eightthree.eventcenter.event.InternalEvent;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class InternalEventQueue implements IALInternalEventProcess {

    private IALInternalEventProcess.Listener mListener;

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public synchronized void sendInternalEvent(InternalEvent event) {
        if (mListener != null) {
            mListener.onInternalEventProcess(event);
        }
    }

    @Override
    public void sendInternalEventNonBlock(InternalEvent event) {
        //do nothing

    }

    @Override
    public void shutdown() {
        unregisterListener(mListener);
        mListener = null;
    }

    @Override
    public void registerListener(Listener listener) {
        mListener = listener;
    }

    @Override
    public void unregisterListener(Listener listener) {
        mListener = null;
    }
}
