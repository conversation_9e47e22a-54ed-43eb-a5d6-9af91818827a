package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class ALReadParamEvent extends ALEvent {

    private int mNumber;
    private final long[] mAddresses;
    private final String mChipName;

    /**
     * Constructors ALReadParamEvent.
     *
     * @param count     count
     * @param addresses register address
     * @param name      chip name
     */
    public ALReadParamEvent(int count, long[] addresses, String name) {
        this.mNumber = count;
        this.mAddresses = addresses;
        this.mChipName = name;
    }

    public int getNumber() {
        return mNumber;
    }

    public void increaseNumber() {
        this.mNumber++;
    }

    public String getChipName() {
        return mChipName;
    }

    public long getAddress(int index) {
        return mAddresses[index];
    }

    public boolean checkFinished() {
        return mAddresses == null || mAddresses.length <= mNumber;
    }

    @Override
    public int getEventType() {
        return READ_PARAM;
    }
}
