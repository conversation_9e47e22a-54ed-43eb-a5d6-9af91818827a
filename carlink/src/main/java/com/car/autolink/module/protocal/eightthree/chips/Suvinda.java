package com.car.autolink.module.protocal.eightthree.chips;

import com.elvishew.xlog.XLog;

import java.util.ArrayList;
import java.util.List;


public class Suvinda implements Chip {

    private static final String TAG = "Suvinda";
    private String productId;
    private String productFeature;
    private byte[] checkResult;
    private final List<String> allInfo = new ArrayList<>();

    @Override
    public void addParameter(int number, byte[] data) {
        switch (number) {
            case 0:
                productId = String.valueOf(byteArrayToInt(data));
                XLog.tag(TAG).d("productId: " + bytesToHex(data));
                break;
            case 1:
                productFeature = String.valueOf(byteArrayToInt(data));
                XLog.tag(TAG).d("productFeature: " + bytesToHex(data));
                break;
            case 2:
                checkResult = data;
                break;
            default:
                break;
        }
    }

    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    private static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    @Override
    public String getParameter(int index) {
        switch (index) {
            case 0:
                return productId;
            case 1:
                return productFeature;
            default:
                return null;
        }
    }

    @Override
    public List<String> getParameter() {
        allInfo.add(productId);
        allInfo.add(productFeature);
        return allInfo;
    }

    public byte[] getCheckResult() {
        return checkResult;
    }

    private int byteArrayToInt(byte[] b) {
        return b[0] & 0xFF
                | (b[1] & 0xFF) << 8
                | (b[2] & 0xFF) << 16
                | (b[3] & 0xFF) << 24;
    }
}
