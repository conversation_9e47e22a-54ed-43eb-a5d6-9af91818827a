package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public abstract class ALReportEvent {

    public static final int BASE = 0;
    public static final int RUNNING_STATE_CHANGE = 1;
    public static final int BLUETOOTH_READY_TO_PAIR = 2;
    public static final int BLUETOOTH_AUTH_DATA = 3;
    public static final int VIDEO_FOCUS_REQUEST = 4;
    public static final int AUDIO_FOCUS_REQUEST = 5;
    public static final int NAV_FOCUS_REQUEST = 6;
    public static final int VIDEO_ESTABLISHED = 7;
    public static final int AUDIO_SESSION_NOTIFICATION = 8;
    public static final int VOICE_SESSION_NOTIFICATION = 9;
    public static final int VIDEO_FOCUS_NOTIFICATION = 10;
    public static final int FORCE_LANDSCAPE = 11;
    public static final int INQUIRE_ORIENTATION = 12;
    public static final int UPDATE_CARID = 13;
    public static final int UPLOAD_CARINFO = 14;
    public static final int UPLOAD_CARDATA = 15;
    public static final int BT_CHANNEL_OPEN = 16;
    public static final int PROJECTION_REQUEST = 17;
    public static final int INQUIRE_RESOLUTION = 18;

    /**
     * get event type
     *
     * @return type
     */
    public abstract int type();
}
