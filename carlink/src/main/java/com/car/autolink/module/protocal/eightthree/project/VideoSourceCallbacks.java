package com.car.autolink.module.protocal.eightthree.project;

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Java perspective of the {@code IVideoSourceCallbacks} native interface.
 */
public interface VideoSourceCallbacks extends NativeCallback {
    /**
     * onChannelOpened
     *
     * @return 0 or -1
     */
    int onChannelOpened();

    /**
     * configCallback
     *
     * @param status   status
     * @param maxUnack maxUnack
     * @param prefer   prefer
     * @param size     size
     * @return 0 or -1
     */
    int configCallback(int status, int maxUnack, int[] prefer, int size);

    /**
     * ackCallback
     *
     * @param sessionId sessionId
     * @param numFrames numFrames
     * @return 0 or -1
     */
    int ackCallback(int sessionId, int numFrames);

    /**
     * videoFocusNotifCallback
     *
     * @param focus       focus
     * @param unsolicited unsolicited
     * @return 0 or -1
     */
    int videoFocusNotifCallback(int focus, boolean unsolicited);

    /**
     * displayChangeCallback
     *
     * @param width       width
     * @param height      height
     * @param isLandscape isLandscape
     * @param density     density
     * @return 0 or -1
     */
    int displayChangeCallback(int width, int height, boolean isLandscape, int density);

    /**
     * discoverVideoConfigCallback
     *
     * @param codec codec
     * @param fps   fps
     * @param w     w
     * @param h     h
     * @return success or not
     */
    boolean discoverVideoConfigCallback(int codec, int fps, int w, int h);

    /**
     * startResponseCallback
     *
     * @param isOK isOK
     * @return 0 or -1
     */
    int startResponseCallback(boolean isOK);

    /**
     * stopResponseCallback
     *
     * @return 0 or -1
     */
    int stopResponseCallback();
}
