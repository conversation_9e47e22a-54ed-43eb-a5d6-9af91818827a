package com.car.autolink.module.protocal.eightthree.eventcenter.event;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class InternalCommonEvent extends InternalEvent {
    public static final int PROTOCOL_UNRECOVERABLE_ERROR = 1;
    public static final int PROBE_SUCCESS = 2;
    public static final int PROBE_FAIL = 3;
    public static final int PROTOCOL_EXIT = 4;

    private final int mConsignee;
    private final int mErrorType;

    public InternalCommonEvent(int consignee, int error) {
        mConsignee = consignee;
        mErrorType = error;
    }

    @Override
    public int getConsignee() {
        return mConsignee;
    }

    @Override
    public int getType() {
        return ERROR;
    }

    public int getError() {
        return mErrorType;
    }

}
