package com.car.autolink.module.video;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.SurfaceTexture;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaFormat;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionConfig;
import android.media.projection.MediaProjectionManager;
import android.opengl.EGLContext;
import android.opengl.GLES20;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.view.Display;
import android.view.Surface;

import androidx.annotation.NonNull;

import com.car.autolink.base.BaseVideo;
import com.car.autolink.config.VideoConfig;
import com.car.autolink.config.VideoPackage;
import com.car.autolink.events.ProjectionPermissionEvent;
import com.car.autolink.events.ProjectionStatusEvent;
import com.car.autolink.module.video.glutils.EglTask;
import com.car.autolink.module.video.glutils.FullFrameRect;
import com.car.autolink.module.video.glutils.Texture2dProgram;
import com.car.autolink.module.video.glutils.WindowSurface;
import com.elvishew.xlog.XLog;

import org.greenrobot.eventbus.EventBus;

import java.nio.ByteBuffer;

public final class ProjectionRendererNew extends BaseVideo {
    private static final String TAG = "ProjectionRenderer";
    private static final int MEDIA_CODEC_VIDEO_MPEG4_ES = 5;
    private static final int BITRATE = 4 * 1024 * 1024;
    private static final int I_FRAME_INTERVAL = 3;
    // --- Encoder Core (Persistent) ---
    private MediaCodec mCodec;
    private Surface mInputSurface;

    // --- Data & Config ---
    private final VideoPackage mPackage = new VideoPackage();
    private final MediaProjectionManager mProjectionManager;
    private MediaProjection mMediaProjection = null;
    private MediaFormat format;
    private String mVideoMimeType = "video/avc";
    private int mVideoWidth = 1280;
    private int mVideoHeight = 720;
    private int mFrameRate = 15;

    // --- Threading & State ---
    private DrawTask mDrawTask;
    private Thread mCaptureThread = null;
    private final Object mSync = new Object();
    private final Handler mHandler;
    private boolean mRequestDraw;
    private boolean mRequestEncode = false;
    private volatile boolean mIsRendererRunning;
    private final DisplayManager displayManager;
    private int mode = 0;
    private ProjectionStatusEvent.State mCurrentState = ProjectionStatusEvent.State.IDLE;

    /**
     * ProjectionRenderer.
     *
     * @param context android context.
     */
    public ProjectionRendererNew(Context context) {
        mProjectionManager =
                (MediaProjectionManager) context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        displayManager = (DisplayManager) context.getSystemService(Context.DISPLAY_SERVICE);
        final HandlerThread thread = new HandlerThread(TAG);
        thread.start();
        mHandler = new Handler(thread.getLooper());
        postStatusUpdate();
    }

    private void postStatusUpdate() {
        EventBus.getDefault().post(new ProjectionStatusEvent(mCurrentState, this.mode));
        XLog.tag(TAG).d("Posting status update: " + mCurrentState + ", mode: " + this.mode);
    }

    private void changeState(ProjectionStatusEvent.State newState) {
        if (mCurrentState != newState) {
            mCurrentState = newState;
            postStatusUpdate();
        }
    }


    public void initializeEncoder() {
        startEncode();
    }

    @Override
    protected void onStart() {
        startRender();
    }

    @Override
    protected void onStop() {
        changeState(ProjectionStatusEvent.State.STOPPING);
        stopRender();
        changeState(ProjectionStatusEvent.State.IDLE);
    }

    @Override
    protected void onShutdown() {
        release();
    }


    @Override
    public void setVideoConfig(VideoConfig config) {
        mFrameRate = config.getFrameRate();
        if (config.getCodec() == MEDIA_CODEC_VIDEO_MPEG4_ES) {
            mVideoMimeType = "video/mp4v-es";
        } else {
            mVideoMimeType = "video/avc";
        }
        selectBestConfig(config);
        initializeEncoder();
    }

    @Override
    public boolean setPermissionResult(int resultCode, Intent intent, boolean isSecondary) {
        if (resultCode == Activity.RESULT_OK) {
            mMediaProjection = mProjectionManager.getMediaProjection(resultCode, intent);
            mMediaProjection.registerCallback(mMediaProjectionCallback, mHandler);
            if (isSecondary) {
                startRender();
            }
            return true;
        } else {
            mMediaProjection = null;
            return false;
        }
    }

    @Override
    public Intent requestPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            return mProjectionManager.createScreenCaptureIntent(MediaProjectionConfig.createConfigForDefaultDisplay());
        } else {
            return mProjectionManager.createScreenCaptureIntent();
        }
    }

    @Override
    public void setProjectionMode(int mode) {
        if (this.mode == mode) {
            return; // No change needed
        }
        this.mode = mode;
        XLog.tag(TAG).d("Switching projection mode to: " + mode);
        postStatusUpdate();
        if (mDrawTask != null) {
            mDrawTask.requestModeSwitch(mode);
        }
    }

    private void startEncode() {
        if (mCodec != null) {
            XLog.tag(TAG).w("Encoder is already running.");
            return;
        }
        try {
            mCodec = MediaCodec.createEncoderByType(mVideoMimeType);
            mCodec.setCallback(new MediaCodec.Callback() {

                @Override
                public void onInputBufferAvailable(@NonNull MediaCodec codec, int index) {
                    //do nothing
                }

                @Override
                public void onOutputBufferAvailable(@NonNull MediaCodec codec, int index,
                                                    @NonNull MediaCodec.BufferInfo info) {
                    dequeueRenderFrame(codec, index, info);
                }

                @Override
                public void onError(@NonNull MediaCodec codec,
                                    @NonNull MediaCodec.CodecException e) {
                    XLog.tag(TAG).e("MediaCodec Error", e);
                }

                @Override
                public void onOutputFormatChanged(@NonNull MediaCodec codec,
                                                  @NonNull MediaFormat format) {
                    XLog.tag(TAG).i("MediaCodec format changed: " + format);
                }
            });
            mCodec.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            mInputSurface = mCodec.createInputSurface();
            mCodec.start();
            XLog.tag(TAG).d("Encoder started successfully.");
        } catch (Exception e) {
            XLog.tag(TAG).e("Failed to start encoder", e);
            mCodec = null;
            mInputSurface = null;
            changeState(ProjectionStatusEvent.State.ERROR);
        }
    }

    private void stopEncode() {
        if (mCodec == null) return;
        try {
            mCodec.stop();
            mCodec.release();
            XLog.tag(TAG).d("Encoder stopped successfully.");
        } catch (Exception e) {
            XLog.tag(TAG).e("Error stopping encoder", e);
        } finally {
            mCodec = null;
        }
        if (mInputSurface != null) {
            mInputSurface.release();
            mInputSurface = null;
        }
    }

    private void releaseMediaProjection() {
        if (mMediaProjection != null) {
            mMediaProjection.unregisterCallback(mMediaProjectionCallback);
            mMediaProjection.stop();
            mMediaProjection = null;
            XLog.tag(TAG).d("MediaProjection released.");
        }
    }

    private void startRender() {
        changeState(ProjectionStatusEvent.State.STARTING);
        if (mInputSurface == null) {
            XLog.tag(TAG).e("Cannot start renderer, encoder not initialized.");
            // Maybe try to initialize it now?
            // initializeEncoder(); // This depends on your desired logic
            changeState(ProjectionStatusEvent.State.ERROR);
            return;
        }
        if (mode == 0 && mMediaProjection == null) {
            XLog.tag(TAG).d("Requesting permission for MediaProjection mode.");
            changeState(ProjectionStatusEvent.State.WAITING_FOR_PERMISSION);
            EventBus.getDefault().post(new ProjectionPermissionEvent(requestPermission(), true));
            return;
        }

        if (mCaptureThread != null) {
            XLog.tag(TAG).w("Renderer is already running.");
            return;
        }

        mIsRendererRunning = true;
        mDrawTask = new DrawTask(null, 0);
        mCaptureThread = new Thread(mDrawTask, "ScreenCaptureThread");
        mCaptureThread.start();
        XLog.tag(TAG).d("Renderer started for mode: " + mode);
    }

    private void stopRender() {
        if (mCaptureThread == null) return;
        synchronized (mSync) {
            mIsRendererRunning = false;
            mSync.notifyAll();
        }
        try {
            mCaptureThread.join();
        } catch (InterruptedException e) {
            XLog.tag(TAG).e("Error joining capture thread", e);
        } finally {
            mCaptureThread = null;
            mDrawTask = null;
            XLog.tag(TAG).d("Renderer stopped.");
        }
    }

    private void selectBestConfig(VideoConfig config) {
        format = new MediaFormat();
        format.setString(MediaFormat.KEY_MIME, mVideoMimeType);
        format.setInteger(MediaFormat.KEY_WIDTH, config.getWidth());
        format.setInteger(MediaFormat.KEY_HEIGHT, config.getHeight());
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT,
                MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
        format.setInteger(MediaFormat.KEY_BIT_RATE, BITRATE);
        format.setInteger(MediaFormat.KEY_FRAME_RATE, mFrameRate);
        format.setInteger(MediaFormat.KEY_CAPTURE_RATE, mFrameRate);
        format.setInteger(MediaFormat.KEY_REPEAT_PREVIOUS_FRAME_AFTER, 1000000 / mFrameRate);
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            format.setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileHigh);
            format.setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel41);
        }
        MediaCodecList codecList = new MediaCodecList(MediaCodecList.REGULAR_CODECS);
        String result = codecList.findEncoderForFormat(format);

        if (result == null) {
            XLog.tag(TAG).w("platform video config not support:" + format);
            if (config.getWidth() > config.getHeight()) {
                mVideoWidth = 1280;
                mVideoHeight = 720;
            } else {
                mVideoWidth = 720;
                mVideoHeight = 1280;
            }
            config.setWidth(mVideoWidth);
            config.setHeight(mVideoHeight);
        } else {
            mVideoWidth = config.getWidth();
            mVideoHeight = config.getHeight();
        }
        format.setInteger(MediaFormat.KEY_WIDTH, mVideoWidth);
        format.setInteger(MediaFormat.KEY_HEIGHT, mVideoHeight);
        XLog.tag(TAG).d("best config:" + format);
    }

    private void dequeueRenderFrame(MediaCodec codec, int index, MediaCodec.BufferInfo info) {
        try {
            ByteBuffer encodedData = codec.getOutputBuffer(index);
            if (encodedData != null) {
                encodedData.position(info.offset);
                encodedData.limit(info.offset + info.size);
                mPackage.setData(new byte[info.size]);
                mPackage.setSize(info.size);
                mPackage.setFlags(info.flags);
                encodedData.get(mPackage.getData(), 0, info.size);
                if (mCallback != null) {
                    mCallback.onRenderFrame(mPackage);
                }
                codec.releaseOutputBuffer(index, false);
            }
        } catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

    private final MediaProjection.Callback mMediaProjectionCallback = new MediaProjection.Callback() {
        @Override
        public void onStop() {
            Log.d(TAG, "MediaProjection onStop");
            super.onStop();
            mMediaProjection = null;
            changeState(ProjectionStatusEvent.State.STOPPING);
            shutdown();
        }

        @Override
        public void onCapturedContentResize(int width, int height) {
            Log.d(TAG, "onCapturedContentResize" + width + "x" + height);
            super.onCapturedContentResize(width, height);
        }

        @Override
        public void onCapturedContentVisibilityChanged(boolean isVisible) {
            Log.d(TAG, "onCapturedContentVisibilityChanged: " + isVisible);
            super.onCapturedContentVisibilityChanged(isVisible);
        }
    };

    private void release() {
        changeState(ProjectionStatusEvent.State.STOPPING);
        stopRender();
        stopEncode();
        releaseMediaProjection();
        mHandler.getLooper().quit();
        changeState(ProjectionStatusEvent.State.IDLE);
    }

    /**
     * frame control for surface data.
     * current frame rate is 30fps,mIntervals is for control.
     */
    private final class DrawTask extends EglTask {
        private static final int REQUEST_SWITCH_MODE = 1;
        private VirtualDisplay mVirtualDisplay;
        private long mIntervals;
        private int mTexId;
        private SurfaceTexture mSourceTexture;
        private Surface mSourceSurface;
        private WindowSurface mEncoderSurface;
        private FullFrameRect mDrawer;

        private final float[] mTexMatrix = new float[16];
        private long mLastFrameTime = System.currentTimeMillis();

        DrawTask(final EGLContext sharedContext, final int flags) {
            super(sharedContext, flags);
        }

        public void requestModeSwitch(int newMode) {
            offer(REQUEST_SWITCH_MODE, newMode, null);
        }

        @Override
        protected void onStart() {
            Log.d(TAG, "onStart");
            mDrawer =
                    new FullFrameRect(new Texture2dProgram(Texture2dProgram.ProgramType.TEXTURE_EXT));
            mTexId = mDrawer.createTextureObject();
            mSourceTexture = new SurfaceTexture(mTexId, false);
            mSourceTexture.setDefaultBufferSize(mVideoWidth, mVideoHeight);
            mSourceSurface = new Surface(mSourceTexture);
            mEncoderSurface = new WindowSurface(getEglCore(), mInputSurface);
            mSourceTexture.setOnFrameAvailableListener(mOnFrameAvailableListener, mHandler);
            mIntervals = (long) (1000f / mFrameRate);
            createVirtualDisplay(mode);
            queueEvent(mDrawTask);
            changeState(ProjectionStatusEvent.State.RUNNING);
        }

        @Override
        protected void onStop() {
            XLog.tag(TAG).enableStackTrace(5).d("onStop");
            releaseVirtualDisplay();

            if (mDrawer != null) mDrawer.release();
            if (mEncoderSurface != null) mEncoderSurface.release();
            if (mSourceSurface != null) mSourceSurface.release();
            if (mSourceTexture != null) mSourceTexture.release();
            mDrawer = null;
            mEncoderSurface = null;
            mSourceSurface = null;
            mSourceTexture = null;
            makeCurrent();
        }

        @Override
        protected boolean onError(final Exception e) {
            XLog.tag(TAG).e("EglTask onError", e);
            changeState(ProjectionStatusEvent.State.ERROR);
            return true;
        }

        @Override
        protected boolean processRequest(final int request, final int arg1, final Object arg2) {
            if (request == REQUEST_SWITCH_MODE) {
                XLog.tag(TAG).d("DrawTask: Processing mode switch request to " + arg1);
                releaseVirtualDisplay();
                createVirtualDisplay(arg1);
                return false;
            }
            return false;
        }

        private void createVirtualDisplay(int displayMode) {
            int videoDensity = 160;
            try {
                if (displayMode == 0) {
                    if (mMediaProjection == null) {
                        XLog.tag(TAG).e("DrawTask: MediaProjection is null, cannot create display for mode 0");
                        return;
                    }
                    mVirtualDisplay = mMediaProjection.createVirtualDisplay("Autolink Display", mVideoWidth, mVideoHeight,
                            videoDensity, DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC, mSourceSurface, null, mHandler);
                } else {
                    mVirtualDisplay = displayManager.createVirtualDisplay("Presentation Display", mVideoWidth, mVideoHeight,
                            320, mSourceSurface, DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION | DisplayManager.VIRTUAL_DISPLAY_FLAG_OWN_CONTENT_ONLY);
                    if (mCallback != null) {
                        Display display = mVirtualDisplay.getDisplay();
                        if (display != null) {
                            mCallback.onPresentationDisplayCreated(display);
                        } else {
                            XLog.tag(TAG).e("DrawTask: getDisplay() returned null for Presentation");
                        }
                    }
                }
                XLog.tag(TAG).d("VirtualDisplay created for mode: " + displayMode);
            } catch (Exception e) {
                XLog.tag(TAG).e("Failed to create VirtualDisplay for mode " + displayMode, e);
            }
        }

        private void releaseVirtualDisplay() {
            if (mVirtualDisplay != null) {
                mVirtualDisplay.release();
                mVirtualDisplay = null;
                mCallback.onPresentationDisplayCreated(null);
                XLog.tag(TAG).d("VirtualDisplay released.");
            }
        }

        private final SurfaceTexture.OnFrameAvailableListener mOnFrameAvailableListener =
                surfaceTexture -> {
                    if (mIsRendererRunning) {
                        synchronized (mSync) {
                            mRequestDraw = true;
                            mSync.notifyAll();
                        }
                    }
                };

        private final Runnable mDrawTask = new Runnable() {

            @Override
            public void run() {
                boolean localRequestDraw;
                synchronized (mSync) {
                    localRequestDraw = mRequestDraw;
                    mRequestDraw = false;
                    if (!localRequestDraw) {
                        try {
                            mSync.wait(mIntervals);
                            localRequestDraw = mRequestDraw;
                            mRequestDraw = false;
                        } catch (final InterruptedException e) {
                            releaseSelf();
                            return;
                        }
                    }
                }
                if (mIsRendererRunning) {
                    if (localRequestDraw) {
                        mSourceTexture.updateTexImage();
                        mSourceTexture.getTransformMatrix(mTexMatrix);
                        mRequestEncode = true;
                    }
                    long nowTime = System.currentTimeMillis();
                    if (nowTime - mLastFrameTime > mIntervals) {
                        mLastFrameTime = nowTime;
                        if (mRequestEncode) {
                            mRequestEncode = false;
                            mEncoderSurface.makeCurrent();
                            mDrawer.drawFrame(mTexId, mTexMatrix);
                            mEncoderSurface.swapBuffers();
                        }
                    }
                    makeCurrent();
                    if (localRequestDraw) {
                        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT);
                        GLES20.glFlush();
                    }
                    queueEvent(this);
                } else {
                    releaseSelf();
                }
            }
        };
    }
}
