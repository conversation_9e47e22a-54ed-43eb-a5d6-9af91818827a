package com.car.autolink.module.protocal.eightthree.project;

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
public class VendorExtension implements NativeCallback {

    private VendorExtensionCallbacks mVendorListener;
    private long mNativeVendorExtension;

    public VendorExtension(VendorExtensionCallbacks listener) {
        mVendorListener = listener;
    }

    public boolean create(int serviceId, long nativeGalReceiver) {
        return nativeInit(serviceId, nativeGalReceiver) == 0;
    }

    public void destroy() {
        nativeShutdown();
    }

    public long getNativeInstance() {
        return mNativeVendorExtension;
    }

    protected void sendData(byte[] data) {
        nativeSend(data);
    }

    private native int nativeInit(int id, long nativeGalReceiver) throws IllegalStateException;

    private native void nativeShutdown();

    private native void nativeSend(byte[] data);
}
