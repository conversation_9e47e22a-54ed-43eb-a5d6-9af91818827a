package com.car.autolink.module.protocal;

import androidx.annotation.NonNull;

import com.car.autolink.config.AlConfig;
import com.car.autolink.config.PhoneVideoConfig;
import com.car.autolink.config.VideoConfig;
import com.car.autolink.config.VideoPackage;
import com.elvishew.xlog.XLog;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2019/12/6
 */
public abstract class BaseProtocol {
    private static final String TAG = "BaseProtocol";

    public Callback mCallback;

    /**
     * start protocol.
     *
     * @param alConfig AlConfig
     */
    protected abstract void onStart(@NonNull AlConfig alConfig);

    /**
     * shutdown protocol module.
     */
    public void shutdown() {
        XLog.tag(TAG).d("Shutting down...");
        if (mCallback != null) {
            mCallback = null;
        }
        onShutdown();
        XLog.tag(TAG).d("Shutdown completed.");
    }

    /**
     * start protocol module.
     *
     * @param callback {@link Callback}
     * @param alConfig config in  {@link AlConfig}
     */
    public void start(Callback callback, @NonNull AlConfig alConfig) {
        XLog.tag(TAG).d("Starting...");
        mCallback = callback;
        onStart(alConfig);
        XLog.tag(TAG).d("Start sequence finished.");
    }

    /**
     * stop protocol module.
     */
    public void stop() {
        XLog.tag(TAG).d("Stopping...");
        onStop();
        XLog.tag(TAG).d("Stop finished.");
    }

    /**
     * stop protocol.
     */
    protected abstract void onStop();

    /**
     * shutdown protocol.
     */
    protected abstract void onShutdown();

    /**
     * set video focus change.
     *
     * @param focus  video focus
     * @param reason focus change reason
     */
    public abstract void setEncoderState(int focus, int reason);

    /**
     * set landscape or not.
     *
     * @param isLand landscape or not
     */
    public abstract void sendOrientation(int isLand, int rotation);

    /**
     * sendResolutionNotification.
     *
     * @param width      width
     * @param height     height
     * @param isRequired request from car or not
     */
    public abstract void sendResolutionNotification(int width, int height, boolean isRequired);

    /**
     * sendByeByeRequest.
     *
     * @param reason bye reason
     */
    public abstract void sendByeByeRequest(int reason);

    /**
     * sendParingRequest.
     *
     * @param address bluetooth address
     */
    public abstract void sendParingRequest(String address);

    /**
     * set unique id to car.
     *
     * @param id unique id
     */
    public abstract void setCarId(String id);

    /**
     * send video data to car.
     *
     * @param pkg VideoPackage
     */
    public abstract void sendFrame(VideoPackage pkg);

    /**
     * sendBluetoothStatus.
     *
     * @param status      status
     * @param unsolicited unsolicited
     */
    public abstract void sendBluetoothStatus(int status, boolean unsolicited);

    /**
     * send projection permission result to car.
     *
     * @param granted granted
     */
    public abstract void sendPermission(boolean granted);

    public abstract String getHuMake();

    public interface Callback {

        /**
         * read data
         *
         * @param buf    buffer
         * @param offset offset
         * @param len    length
         * @return read bytes
         * @throws IOException io error
         */
        int requestRead(byte[] buf, int offset, int len) throws IOException;

        /**
         * write data
         *
         * @param buf    buffer
         * @param offset offset
         * @param len    length
         * @throws IOException io error
         */
        void requestWrite(byte[] buf, int offset, int len) throws IOException;

        /**
         * stopTransport.
         */
        void stopTransport();
    }
}
