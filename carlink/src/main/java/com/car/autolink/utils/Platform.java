package com.car.autolink.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.view.Surface;

import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2020/04/27
 */
public class Platform {
    private static final int NORMAL = 1;
    private static final int CLOCKWISE = 2;
    private static final int FLIP = 3;
    private static final int ANTICLOCKWISE = 4;
    public static final int REQUEST_CODE_BT = 1235;
    public static final int REQUEST_CODE_ROTATE = 1236;
    public static final int REQUEST_CODE_UPDATE = 1237;

    public static Locale getCurrentLocale(Context context) {
        return context.getResources().getConfiguration().locale;
    }

    public static boolean isChina(Context context) {
        Locale locale = getCurrentLocale(context);
        return "zh".equals(locale.getLanguage());
    }

    /**
     * for 8288 h.
     *
     * @param des    des
     * @param offset offset
     * @return string
     */
    public static String bytesToProductType(byte[] des, int offset) {
        int value;
        value = des[offset] & 0xff
                | ((des[offset + 1] & 0xff) << 8)
                | ((des[offset + 2] & 0xff) << 16)
                | ((des[offset + 3] & 0xff) << 24);
        value = (value >> 16) & 0xff;
        return Integer.toHexString(value);
    }

    /**
     * for 8288 h.
     *
     * @param des    des
     * @param offset offset
     * @return string
     */
    public static String bytesToSpecialMp(byte[] des, int offset) {
        int value;
        value = des[offset] & 0xff
                | ((des[offset + 1] & 0xff) << 8)
                | ((des[offset + 2] & 0xff) << 16)
                | ((des[offset + 3] & 0xff) << 24);
        value = (value >> 24) & 0xffff;
        value = value + 1000;
        return Integer.toString(value);
    }

    /**
     * for 8288 h.
     *
     * @param des    des
     * @param offset offset
     * @return string
     */
    public static String bytesToOtp(byte[] des, int offset) {
        int value;
        value = des[offset] & 0xff
                | ((des[offset + 1] & 0xff) << 8)
                | ((des[offset + 2] & 0xff) << 16)
                | ((des[offset + 3] & 0xff) << 24);
        value = (value >> 16) & 0xffff;
        return Integer.toHexString(value);
    }


    public static int getDisplayRotation(int rotation) {
        switch (rotation) {
            case Surface.ROTATION_0:
                return NORMAL;
            case Surface.ROTATION_90:
                return CLOCKWISE;
            case Surface.ROTATION_180:
                return FLIP;
            case Surface.ROTATION_270:
                return ANTICLOCKWISE;
        }
        return NORMAL;
    }

    public static String getAppVersion(Context context) {
        PackageInfo pi;
        String versionName;
        try {
            PackageManager pm = context.getPackageManager();
            pi = pm.getPackageInfo(context.getPackageName(), PackageManager.GET_CONFIGURATIONS);
            versionName = pi.versionName;
        } catch (Exception e) {
            versionName = "0.0.0";
        }
        return versionName;
    }
}
