package com.car.autolink.logsys;

import android.content.Context;
import android.os.Environment;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @date 2019/11/29
 */
public class LogcatHelper {
    private static LogcatHelper INSTANCE = null;
    private static String PATH_LOGCAT;
    private LogDumper mLogDumper = null;
    private final int mPid;

    /**
     * init log  save dir.
     *
     * @param context {@link Context}
     */
    public void init(Context context) {
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            PATH_LOGCAT = context.getExternalFilesDir(null).getAbsolutePath();
        } else {
            PATH_LOGCAT = context.getFilesDir().getAbsolutePath();
        }
        File file = new File(PATH_LOGCAT);
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    /**
     * getInstance.
     *
     * @param context {@link Context}
     * @return LogcatHelper
     */
    public static LogcatHelper getInstance(Context context) {
        if (INSTANCE == null) {
            INSTANCE = new LogcatHelper(context);
        }
        return INSTANCE;
    }

    private LogcatHelper(Context context) {
        init(context);
        mPid = android.os.Process.myPid();
    }

    /**
     * start save log.
     */
    public void start() {
        if (mLogDumper == null) {
            mLogDumper = new LogDumper(String.valueOf(mPid));
        }
        mLogDumper.start();
    }

    /**
     * stop save log.
     */
    public void stop() {
        if (mLogDumper != null) {
            mLogDumper.stopLogs();
            mLogDumper = null;
        }
    }

    private static class LogDumper extends Thread {

        private Process mProcess;
        private BufferedReader mReader = null;
        private boolean mRunning = true;
        String mCmd;
        private final String mPid;
        private FileOutputStream mOutputStream = null;

        LogDumper(String pid) {
            mPid = pid;
            try {
                File file = new File(PATH_LOGCAT + File.separator + "global.log");
                if (file.exists()) {
                    file.delete();
                }
                file.createNewFile();
                mOutputStream = new FileOutputStream(file);
            } catch (IOException e) {
                e.printStackTrace();
            }

            /*
             *
             * 日志等级：*:v , *:d , *:w , *:e , *:f , *:s
             *
             * 显示当前mPID程序的 E和W等级的日志.
             *
             */

            // cmd = "logcat *:e *:w | grep \"(" + mPid + ")\"";
            mCmd = "logcat | grep \"(" + mPid + ")\"";
            // cmd = "logcat -s way";
            //cmd = "logcat *:e *:i | grep \"(" + mPid + ")\"";

        }

        void stopLogs() {
            mRunning = false;
        }

        @Override
        public void run() {
            try {
                mProcess = Runtime.getRuntime().exec(mCmd);
                mReader = new BufferedReader(new InputStreamReader(mProcess.getInputStream()),
                        1024);
                String line;
                while (mRunning && (line = mReader.readLine()) != null) {
                    if (line.length() == 0) {
                        continue;
                    }
                    if (mOutputStream != null && line.contains(mPid)) {
                        mOutputStream.write((line + "\n").getBytes());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (mProcess != null) {
                    mProcess.destroy();
                    mProcess = null;
                }
                if (mReader != null) {
                    try {
                        mReader.close();
                        mReader = null;
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (mOutputStream != null) {
                    try {
                        mOutputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    mOutputStream = null;
                }
            }
        }
    }
}


