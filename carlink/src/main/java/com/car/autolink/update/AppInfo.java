package com.car.autolink.update;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

/**
 * <AUTHOR>
 * @date 2017/12/28
 * @desc
 */
public class AppInfo {

    public static String getVersionName(Context context) {
        PackageInfo packInfo = null;
        try {
            PackageManager packageManager = context.getPackageManager();
            packInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return packInfo != null ? packInfo.versionName : "";
    }

    static int getVersionCode(Context context) {
        int versionCode = 0;
        try {
            versionCode =
                    context.getPackageManager().getPackageInfo(context.getPackageName(), 0).versionCode;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return versionCode;
    }

    static int getSaveVersionCode(Context context, String path) {
        if ("".equals(path)) {
            return 0;
        }
        int versionCode = 0;
        PackageInfo info =
                context.getPackageManager().getPackageArchiveInfo(path,
                        PackageManager.GET_ACTIVITIES);
        if (info != null) {
            versionCode = info.versionCode;
        }
        return versionCode;
    }
}
