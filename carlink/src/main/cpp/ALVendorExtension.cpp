// Copyright 2014 Google Inc. All Rights Reserved.

#include <jni.h>
#include "GalReceiver.h"
#include "VendorExtension.h"
#include "util/shared_ptr.h"
#include "VendorExtensionCallbacks.h"

static jclass vendorExtensionClass;
static jfieldID fidNativeVendorExtension;
static jfieldID callbackID;
static jobject vendorcallbacks;

VendorExtension *getVendorExtension(JNIEnv *env, jobject jthis) {
    jlong mNativeVendorExtension = env->GetLongField(jthis, fidNativeVendorExtension);
    return (VendorExtension *) mNativeVendorExtension;
}

void setVendorExtension(JNIEnv *env, jobject jthis, VendorExtension *vendorExtension) {
    env->SetLongField(jthis, fidNativeVendorExtension, (jlong) vendorExtension);
}

extern "C" JNIEXPORT jint JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_VendorExtension_nativeInit(
        JNIEnv *env, jobject jthis, jint id, jlong nativeGalReceiver) {
    vendorExtensionClass = env->GetObjectClass(jthis);
    fidNativeVendorExtension =
            env->GetFieldID(vendorExtensionClass, "mNativeVendorExtension", "J");
    callbackID = env->GetFieldID(vendorExtensionClass, "mVendorListener",
                                 "Lcom/car/autolink/module/protocal/eightthree/project/VendorExtensionCallbacks;");
    vendorcallbacks = env->GetObjectField(jthis, callbackID);
    VendorExtension *vendorExtension = getVendorExtension(env, jthis);
    if (vendorExtension != nullptr) {
        jclass eclass = env->FindClass("java/lang/IllegalStateException");
        env->ThrowNew(eclass, "Already initialized.");
        return -1;
    }


    auto *galReceiver = (GalReceiver *) nativeGalReceiver;
    vendorExtension = new VendorExtension(id, galReceiver->messageRouter());
    shared_ptr<IVendorExtensionCallbacks> callbacks(
            new VendorExtensionCallbacks(env, vendorcallbacks));
    vendorExtension->registerCallbacks(callbacks);
    setVendorExtension(env, jthis, vendorExtension);
    return STATUS_SUCCESS;
}

extern "C" JNIEXPORT void JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_VendorExtension_nativeShutdown(
        JNIEnv *env, jobject jthis) {
    VendorExtension *vendorExtension = getVendorExtension(env, jthis);
    if (vendorExtension != nullptr) {
        setVendorExtension(env, jthis, nullptr);
        vendorExtension->forceCloseChannel();
        delete vendorExtension;
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_VendorExtension_nativeSend(
        JNIEnv *env, jobject jthis, jbyteArray data) {
    VendorExtension *vendorExtension = getVendorExtension(env, jthis);
    jsize len_array = env->GetArrayLength(data);
    jbyte *carray = env->GetByteArrayElements(data, nullptr);
    if (vendorExtension != nullptr) {
        vendorExtension->sendData((void *) carray, static_cast<size_t>(len_array));
    }
    env->ReleaseByteArrayElements(data, carray, JNI_ABORT);
}
