# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.

cmake_minimum_required(VERSION 3.22.1)

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# <PERSON><PERSON><PERSON> automatically packages shared libraries with your APK.
add_definitions(-DHAVE_PTHREAD)
set(DIR_SRC ./src/google/protobuf)
add_library( # Sets the name of the library.
        protobuf

        # Sets the library as a shared library.
        STATIC

        # Provides a relative path to your source file(s).
        ${DIR_SRC}/stubs/atomicops_internals_x86_gcc.cc
        ${DIR_SRC}/stubs/atomicops_internals_x86_msvc.cc
        ${DIR_SRC}/stubs/common.cc
        ${DIR_SRC}/stubs/once.cc
        ${DIR_SRC}/stubs/stringprintf.cc
        ${DIR_SRC}/extension_set.cc
        ${DIR_SRC}/generated_message_util.cc
        ${DIR_SRC}/message_lite.cc
        ${DIR_SRC}/repeated_field.cc
        ${DIR_SRC}/wire_format_lite.cc
        ${DIR_SRC}/io/coded_stream.cc
        ${DIR_SRC}/io/zero_copy_stream.cc
        ${DIR_SRC}/io/zero_copy_stream_impl_lite.cc
        ${DIR_SRC}/stubs/strutil.cc
        ${DIR_SRC}/stubs/substitute.cc
        ${DIR_SRC}/stubs/structurally_valid.cc
        ${DIR_SRC}/descriptor.cc
        ${DIR_SRC}/descriptor.pb.cc
        ${DIR_SRC}/descriptor_database.cc
        ${DIR_SRC}/dynamic_message.cc
        ${DIR_SRC}/extension_set_heavy.cc
        ${DIR_SRC}/generated_message_reflection.cc
        ${DIR_SRC}/message.cc
        ${DIR_SRC}/reflection_ops.cc
        ${DIR_SRC}/service.cc
        ${DIR_SRC}/text_format.cc
        ${DIR_SRC}/unknown_field_set.cc
        ${DIR_SRC}/wire_format.cc
        ${DIR_SRC}/io/gzip_stream.cc
        ${DIR_SRC}/io/printer.cc
        ${DIR_SRC}/io/strtod.cc
        ${DIR_SRC}/io/tokenizer.cc
        ${DIR_SRC}/io/zero_copy_stream_impl.cc)

# Searches for a specified prebuilt library and stores the path as a
# variable. Because CMake includes system libraries in the search path by
# default, you only need to specify the name of the public NDK library
# you want to add. CMake verifies that the library exists before
# completing its build.
