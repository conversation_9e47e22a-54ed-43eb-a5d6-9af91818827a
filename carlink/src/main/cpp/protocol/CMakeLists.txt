# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.

cmake_minimum_required(VERSION 3.22.1)

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# <PERSON>radle automatically packages shared libraries with your APK.
add_library( # Sets the name of the library.
        protocol

        # Sets the library as a shared library.
        STATIC

        # Provides a relative path to your source file(s).
        # autolink.pb.cc
        autolink.pb.cc)
