// Copyright 2014 Google Inc. All Rights Reserved.
syntax = "proto2";
package autolink;
option java_package = "com.autolink.proto";
option optimize_for = LITE_RUNTIME;


/**
 * @section Version
 * <p>V1.5 or under: An early ptotocol version.</p>
 * <p>V1.6:
 *        1. Remove redundancy message or enumeration.</p>
 * <p>V1.7:
 *        1. Add MD screen resolution inquire.</p>
 * <p>V1.8:
 *        1. Add time and date inquire.
 *        2. Add display configuration change request.</p>
 * <p>V1.9:
 *        1. Add video_transport_mode in MEDIA_MESSAGE_CONFIG for video data.
 *        2. Add message MEDIA_MESSAGE_START_RESPONSE and MEDIA_MESSAGE_STOP_RESPONSE for media channel.
 *        3. Add section MessageExistanceSetup to set up some messages to logical existence or not.</p>
 * <p>V1.10:
 *        1. Add ScreenRotation for MESSAGE_SCREEN_ORIENTATION_NOTIFICATION to notify the rotation of <PERSON>.</p>
 * <p>V1.11:
 *        1. Add codec_width & codec_height for message VideoConfiguration to indicate the expected video resolution.
 *        2. Add configuration_video_width & configuration_video_height for MEDIA_MESSAGE_START to notify the adopted video resolution of MD.</p>
 */


/**
 * @section Introduction
 * <p>This document describes the Autolink Projection (ALP) protocol that enables communication between Android mobile devices(MD) and in-vehicle head units(HU) running the ALP Receiver Library. To
 * ensure compatibility between all MD and all HU, the ALP Receiver Library implements the protocol buffer protocol. Head unit integration code interacts with ALP by calling the Receiver Library API;
 * projected applications interact with the ALP by calling the Car Service API.
 * <div class="note"> Use this document for <b>reference only</b> and <b>do not modify</b> or re-implement any portion of this protocol. Google-defined services/protocol buffers must not be modified
 * by entities other than Google.</div></p>
 * <p>ALP includes the following assumptions:
 * <ul>
 *   <li><b>Transport</b>. The transport layer provides reliable in-order delivery with low and consistent latency on a local link.</li>
 *   <li><b>Display</b>. HU may advertise one display (the head unit display) for video projection. ALP does not handle additional displays such as back seat entertainment displays or a video display
 *        in the instrument cluster.</li>
 *   <li> <b>Input</b>. HU can use one of two touchscreen configurations: touchscreen on the advertised display or no touchscreen.</li>
 *   <li><b>Telephone</b>. Bluetooth is used to route all call audio.</li>
 *   <li><b>Custom Services</b>. ALP API allows for sending vendor specific services over the ALP transport layer (no protocol modifications required to transport proprietary vendor data).
 *        Vendor-specific services must use reverse domain name format (such as com.make.model.serviceName).</li>
 *   <li><b>Upgrade</b>. HU's rom can be upgraded use some files received from MD over the ALP transport layer.</li>
 * </ul></p>
 * <p>Unless otherwise stated, all encoding is network order (big-endian).</p>
 */


/**
 * @section Framing
 * <p>Frames are the lowest-level ALP packet type and comprise logical messages that are implicitly carried over a channel. Using frames enables messages to be fragmented and prioritized based on a
 * channel number before sending on a transport.</p>
 * <p><img src='./images/message-layout.png'/></p><br>
 * <p>A frame header contains the following fields:</p>
 * <p><img src='./images/frame-layout.png'/></p>
 * @startTable
 * Field Name           |Size   | Description
 * Channel Id           |1 byte | Identifies the channel to which the frame belongs.
 * Fragment Information |2 bits | Indicates the message fragment this frame represents and the presence of the optional Message Length field.
 * Channel Control      |1 bit  | Indicates the message is a Channel Control message.
 * Encrypted            |1 bit  | Indicates whether the payload is encrypted or not.
 * Reserved             |4 bits | Reserved for future use. Must be set to 0 when sending and ignored on receipt.
 * Frame Length         |2 bytes| Length of the frame data, excluding frame header.
 * Message Length       |4 bytes| Optional, length of the message before fragmentation. Present only in the first frame of a fragmented message.
 * @endTable
 * <h2>Fragmentation</h2>
 * <p>Message fragmentation must not be used until version negotiation is complete, after which messages are fragmented on a per-channel basis. The Fragment Information field has the following
 * interpretation:</p>
 * @startTable
 * Bit 0| Bit 1| Interpretation
 * 1    | 1    | No fragmentation. Fragment length is equal to message length, so message length field is omitted.
 * 1    | 0    | First fragment. Future frames on this channel must be defragmented to form the original message. Message Length field is included in this frame.
 * 0    | 1    | Last fragment. The original message may be reconstructed on receipt of this frame. Message Length field is omitted.
 * 0    | 0    | Continuation fragment. This frame is not the first fragment or the last fragment of a message. Message Length field is omitted.
 * @endTable
 * <h2>Channels & Priorities</h2>
 * <p>Channels are used to establish ordered communication to a service. Every channel has a priority, and both the sender and receiver may reorder frames on different channels to implement
 * prioritization. To guarantee in-order delivery, do not reorder frames of the same channel.</p>
 * @startTable
 * Channel Control Bit|Interpretation
 * 0                  |Normal frame for the service using the channel.
 * 1                  |Frame is part of a Control message specific to this channel.
 * @endTable
 * <p>After a channel is established, both sender and receiver use the same channel number with the same priority.</p>
 * @startTable
 * Channel Number|Use
 * 0             |Static: Control channel.
 * 1...255       |Dynamically allocated.
 * @endTable
 * <p>Channel 0 always has the highest priority (-128).</p>
 */


/**
 * @section MessageExistanceSetup
 * <p>some ALP messages could be set up to logical existence or not.</p>
 */
/** <p>For channel controller.</p> */
//enum ControlMessageExistanceSetup { }

/** <p>For channel media.</p> */
enum MediaMessageExistanceSetup {
    /** message MEDIA_MESSAGE_START_RESPONSE is existant. */
    MEDIA_MESSAGE_START_RESPONSE_EXISTANCE = 0x0001;
    /** message MEDIA_MESSAGE_STOP_RESPONSE is existant. */
    MEDIA_MESSAGE_STOP_RESPONSE_EXISTANCE = 0x0002;
}

/** <p>For channel input.</p> */
//enum InputMessageExistanceSetup { }

/** <p>For channel bluetooth.</p> */
//enum BluetoothMessageExistanceSetup { }

/**
 * @section Control Messages
 * <p>ALP supports two (2) types of control messages:</p>
 * <ul>
 *   <li><b>Global</b>. Sent on channel 0 and unrelated to a specific channel (Channel Control bit in frame header is not set. </li>
 *   <li><b>Channel</b>. Sent on a specific channel (Channel Control bit in frame header is set). Sent in-band to prevent reorder of channel control and data, but delivered directly to the service
 *        attached to a channel.</li>
 * </ul>
 * <p>Control messages use the following encoding scheme:</p>
 * @startTable
 * Field |Size     |Description
 * Type  |2 bytes  |Numeric identifier that describes the message type.
 * Data  |variable |Type-specific data (some messages use encoded parameters while others use a protocol buffer).
 * @endTable
 * <h2>Frame Errors</h2>
 * <p>If a frame is received that cannot be decoded into a message, a framing error must be sent on channel 0. This error is fatal and the link should be reset to restart the protocol.</p>
 * <h3>FramingError</h3>
 * <p>@startTable
 * Field                |Size   |Description
 * FramingError [0xFFFF]|2 bytes|Constant that describes the type of this message.
 * @endTable</p>
 * <h2> Unexpected Messages </h2>
 * <p>This message is not specific to channel 0 but is discussed here because it is applicable throughout this document. <b>This message can be sent on any channel</b> by either side when it sees a
 * message that is of valid form but cannot handle the message because it believes the message is invalid for the current state. Due to the lack of strong correlation between the sent and received
 * messages, this message should be considered fatal and the link reset.</p>
 * <h3> UnexpectedMessage</h3>
 * <p>@startTable
 * Field                     |Size     |Description
 * UnexpectedMessage [0x00FF]|2 bytes  |Constant that describes the type of message.
 * @endTable</p>
 * <h2>Version Negotiation</h2>
 * <p>Version negotiation is the first communication step, initiated by the HU when it detects a USB connection has been established. Negotiation is performed globally on channel 0 with the following
 * requirements:</p>
 * <ul>
 *   <li>All ALP versions (current and future) must follow version negotiation and framing as specified in ALPv1.0.</li>
 *   <li>After the MD has sent VersionResponse, all messages (including framing) follow the format specified by the selected protocol version.</li>
 *   <li>MD and HU implementing the same major number must be compatible irrespective of minor number. Changes that break backward compatibility require a major number revision.</li>
 *   <li>HU must support at minimum of one [major, minor] version pair. However the MD may support multiple [major, minor] version pairs.</li>
 *   <li>MD respond with a status code to indicate whether the version negotiation completed successfully or not. In case of error, communication between the MD and the HU stops.</li>
 * </ul>
 * @startSequenceDiagram Version Negotiation
 * identifier A: MD
 * identifier B: HU
 * message A->B: VersionRequest
 * message B->A: VersionResponse
 * @endSequenceDiagram
 * <h3>VersionRequest</h3>
 * <p>@startTable
 * Field        |Size   |Description
 * Type         |2 bytes|0x0001
 * Major Version|2 bytes|Major version of the protocol supported by the HU.
 * Minor Version|2 bytes|Minor version of the protocol supported by the HU.
 * @endTable</p>
 * <h3>VersionResponse</h3>
 * <p>@startTable
 * Field        |Size   |Description
 * Type         |2 bytes|0x0002
 * Major Version|2 bytes|Major version of the protocol that has been selected by the MD.
 * Minor Version|2 bytes|Minor version of the protocol selected by the MD.
 * Status       |2 bytes|Indicates the status of the version negotiation.
 * @endTable</p>
 * <div class="note"><b>NOTE</b>: If the MD can support the major version supported by the HU, the MD must send the same major version as sent by the HU. If the MD cannot support the same major
 * version, it must send the highest major version it does support (for debugging) and cease communication. The minor version of the protocol selected by the MD may be different from the version sent
 * by the HU.</div>
 * <h2>Authentication</h2>
 * <p>After version negotiation, authentication occurs using a TLSv1.2 handshake encapsulated over ALP on channel 0. To provide mutual authentication, the handshake uses client and server
 * certificates; after the handshake completes, all messages are sent over the established SSL channel with the Encrypted bit is set. Messages that fail to set the Encrypted bit are discarded.</p>
 * @startSequenceDiagram Authentication
 * identifier A: MD
 * identifier B: HU
 * message B->A: EncapsulatedSSL, SSL handshake data
 * message A->B: EncapsulatedSSL, SSL handshake data
 * spacer: ... SSL handshake proceeds to completion ...
 * message B->A: AuthComplete, status
 * @endSequenceDiagram
 * <p>The HU (SSL client) initiates a handshake with the MD (SSL server). At the end of the handshake, both client and server verify the certificates in use are signed by the Google CA. If signed, the
 * session proceeds; if not signed, the session does not continue.</p>
 * <h3>EncapsulatedSSL</h3>
 * <p>@startTable
 * Field   | Value
 * Type    | 0x0003
 * Payload | Unaltered SSL records.
 * @endTable</p>
 */

/**
 * @suppress
 */
enum ControlMessageType {
    MESSAGE_VERSION_REQUEST = 0x0001;
    MESSAGE_VERSION_RESPONSE = 0x0002;
    MESSAGE_ENCAPSULATED_SSL = 0x0003;
    MESSAGE_AUTH_COMPLETE = 0x0004;
    MESSAGE_SERVICE_DISCOVERY_REQUEST = 0x0005;
    MESSAGE_SERVICE_DISCOVERY_RESPONSE = 0x0006;
    MESSAGE_CHANNEL_OPEN_REQUEST = 0x0007;
    MESSAGE_CHANNEL_OPEN_RESPONSE = 0x0008;
    MESSAGE_CHANNEL_CLOSE_NOTIFICATION = 0x0009;
    MESSAGE_PING_REQUEST = 0x000B;
    MESSAGE_PING_RESPONSE = 0x000C;
    // MESSAGE_NAV_FOCUS_REQUEST = 0x000D;
    // MESSAGE_NAV_FOCUS_NOTIFICATION = 0x000E;
    MESSAGE_BYEBYE_REQUEST = 0x000F;
    MESSAGE_BYEBYE_RESPONSE = 0x0010;
    // MESSAGE_VOICE_SESSION_NOTIFICATION = 0x0011;
    // MESSAGE_AUDIO_FOCUS_REQUEST = 0x0012;
    // MESSAGE_AUDIO_FOCUS_NOTIFICATION = 0x0013;
    MESSAGE_SCREEN_ORIENTATION_INQUIRE = 0x0014;
    MESSAGE_SCREEN_ORIENTATION_NOTIFICATION = 0x0015;
    MESSAGE_FORCE_LANDSCAPE_REQUEST = 0x0016;
    MESSAGE_FORCE_LANDSCAPE_RESPONSE = 0x0017;
    MESSAGE_UPDATE_VEHICLE_ID_NOTIFICATION = 0x0018;
    MESSAGE_RUNNING_STATE_NOTIFICATION = 0x0019;
    MESSAGE_RUNNING_STATE_INQUIRE = 0x001A;
    MESSAGE_EXIT_REQUEST = 0x001B;
    MESSAGE_EXIT_RESPONSE = 0x001C;
    MESSAGE_AUTO_ROTATION_REQUEST = 0x001D;
    MESSAGE_AUTO_ROTATION_RESPONSE = 0x001E;
    MESSAGE_READ_REQUEST = 0x001F;
    MESSAGE_READ_RESPONSE = 0x0020;
    MESSAGE_WRITE_REQUEST = 0x0021;
    MESSAGE_WRITE_RESPONSE = 0x0022;
    MESSAGE_SCREEN_RESOLUTION_INQUIRE = 0x0023;
    MESSAGE_SCREEN_RESOLUTION_NOTIFICATION = 0x0024;
    MESSAGE_TIMEDATE_INQUIRE = 0x0025;
    MESSAGE_TIMEDATE_NOTIFICATION = 0x0026;
    MWSSAGE_WLTOUCH_NOTIFICATION = 0x0027;
    MESSAGE_UNEXPECTED_MESSAGE = 0x00FF;
    MESSAGE_FRAMING_ERROR = 0xFFFF;
}

/**
 * @suppress
 */
enum FragInfo {
    FRAG_CONTINUATION = 0;
    FRAG_FIRST = 1;
    FRAG_LAST = 2;
    FRAG_UNFRAGMENTED = 3;
}

/**
 * @type MESSAGE_AUTH_COMPLETE
 * <p>This message is used to indicate the status of the authentication exchange. Authentication succeeds on when both the SSL handshake and the SSL certificate checks pass.</p>
 */
message AuthResponse {
    required int32 status = 1;
}

/**
 * <h2>Service Discovery</h2>
 * <p>A service is an endpoint for communication between the HU and MD. The HU head unit advertises a set of provided services and the MD selects and connects an available channel to a service
 * (management commands specified in Connection Management). Service discovery is global and occurs on channel 0.</p>
 * <p>Service names typically contain <em>Source</em> (for services that produce data such as sensors) or <em>Sink</em> (for services that consume data such as video).</p>
 */
/**
 * @type MESSAGE_SERVICE_DISCOVERY_REQUEST
 * <p>During setup, a MD sends this message to request a list of services from the HU. The message includes a set of icons (small, medium, large) and label text the head unit can display to enable
 * users to quickly switch to projected mode.</p>
 */
message ServiceDiscoveryRequest {
    /** The menufacturer of the product. */
    optional string manufacturer = 1;
    /** The end-usr-visible name of the end product. */
    optional string model = 2;
    /** The android os version. */
    optional string version = 3;
    /** A friendly MD name. */
    optional string device_name = 4;
    /** The phone screen width. */
    optional uint32 screen_resolution_w = 5;
    /** The phone screen height. */
    optional uint32 screen_resolution_h = 6;
}

/**
 * @type MESSAGE_SERVICE_DISCOVERY_RESPONSE
 * <p>During setup, a HU sends this message to list the services it supports, HU information such as make/model/year, head unit information such as make/model/build/version, and other data.</p>
 */
message ServiceDiscoveryResponse {
    /** List of services (one per element) supported by the HU. */
    repeated Service services = 1;
    /** HU make. */
    optional string make = 2;
    /** HU model. */
    optional string model = 3;
    /** Model year. */
    optional string year = 4;
    /** Identifier for the HU (does not need to be the VIN) that is ideally regenerated for every factory reset performed on the head unit. */
    optional string vehicle_id = 5;
    /** Indicates the position (left, right, center) of the steering wheel, which may influence the position of UI elements. */
    // optional DriverPosition driver_position = 6;
    /** Head unit make. */
    optional string head_unit_make = 7;
    /** Head unit model. */
    optional string head_unit_model = 8;
    /** Head unit software build. */
    optional string head_unit_software_build = 9;
    /** Head unit software version. */
    optional string head_unit_software_version = 10;
    /** Deprecated, use SessionConfiguration flag CAN_PLAY_NATIVE_MEDIA_DURING_VR. */
    optional bool can_play_native_media_during_vr = 11 [deprecated = true];
    /** Removed this, Using session_configuration and UI_CONFIG_HIDE_CLOCK. */
    // optional bool hide_projected_clock = 12;
    /** Options the HU can use to configure the behavior of the MD during projection. */
    optional int32 session_configuration = 13;
    /** Head unit main ic type. */
    optional string head_unit_ic = 14;
    /** Head unit series number. */
    optional string head_unit_series = 15;
    /** Head unit screen size. (unit:inch) */
    optional int32 head_unit_screen_size = 16;
    /** Head unit screen touch type. 1 for capacitive, 2 for resistive */
    optional int32 head_unit_screen_touch_type = 17;
    /** Head unit screen width. (unit:pixel) */
    optional int32 head_unit_screen_width = 18;
    /** Head unit screen height. (unit:pixel) */
    optional int32 head_unit_screen_height = 19;
    /** Checksum. */
    optional uint32 checksum = 20;
    /** Head unit module version. */
    optional string head_unit_module_version = 21;
}

/**
 * <p>Protocol buffers do not support inheritance but do support composition, enabling the message to represent many different service types. A service object should include an ID and a minimum of one
 * other field.</p>
 */
message Service {
    /** Identifier used to open a channel to the specified service. Supports values between 1 and 254; values 0 and 255 are reserved. */
    required int32 id = 1;
    optional MediaSinkService media_sink_service = 3;
    optional InputSourceService input_source_service = 4;
    optional BluetoothService bluetooth_service = 6;
    optional VendorExtensionService vendor_extension_service = 12;
    optional UpgradeService upgrade_service = 14;
}

/**
 * <p>A media sink accepts data from the MD. Can be an audio sink or a video sink (but not both).</p>
 */
message MediaSinkService {
    /** Codec type of this media sink. */
    optional MediaCodecType available_type = 1;
    /** Video configuration (if a video sink). */
    repeated VideoConfiguration video_configs = 4;
}

message VideoConfiguration {
    /** Default supported video resolution. */
    optional VideoCodecResolutionType codec_resolution = 1;
    /** Supported maximum frame rate. */
    optional VideoFrameRateType frame_rate = 2;
    /** Number of pixels in the X axis in which content will not render. */
    optional uint32 width_margin = 3;
    /** Number of pixels in the Y axis in which content will not render. */
    optional uint32 height_margin = 4;
    /** Density, in dots-per-inch (DPI). */
    optional uint32 density = 5;
    /** Additional number of frames for video decoder to display a frame. By default, this is 0 which means that decoder can display the last frame without getting more frames. */
    optional uint32 decoder_additional_depth = 6;
    /** The average screen-to-eye distance in millimeters. The MD will use this value to provide a more usable projected interface. */
    optional uint32 viewing_distance = 7;
    /**
     * The aspect ratio of the physical pixel width over height, multiplied by 1e4. The Android MD will make a best effort attempt to compensate for the non-square pixels. However due to hardware
     * limitations of the MD, power constraints, and user experience considerations, it may not be possible to fully compensate for pixels whose aspect ratio differs significantly from 1.
     */
    optional uint32 pixel_aspect_ratio_e4 = 8;
    /** Media codec type. */
    optional MediaCodecType vcodec_type = 9;
	/** The expected video width. */
    optional uint32 codec_width = 10;
    /** The expected video height. */
    optional uint32 codec_height = 11;
}

/** <p>The following resolutions are supported:</p> */
enum VideoCodecResolutionType {
    VIDEO_800x480 = 1;
    VIDEO_1280x720 = 2;
    VIDEO_1920x1080 = 3;
}

/** <p>The following frame rates are supported:</p> */
enum VideoFrameRateType {
    VIDEO_FPS_60 = 1;
    VIDEO_FPS_30 = 2;
}

/** <p>The following video codecs are supported:</p> */
enum MediaCodecType {
    /** H264 NAL units. */
    MEDIA_CODEC_VIDEO_H264_BP = 3;
    /** MPEG4. */
    MEDIA_CODEC_VIDEO_MPEG4_ES = 5;
}

/**
 * <p>The InputSourceService enables HU to report human input on touch and non-touch input device to MD.</p>
 */
message InputSourceService {
    /** List of supported keycodes supported (see Appendix II). */
    repeated int32 keycodes_supported = 1 [packed=true];
    /** ALPv1.0 supports a single touch screen (the one connected to the HU display). Marked repeated for future expansion. */
    repeated TouchScreen touchscreen = 2;
    /**
     * ALPv1.0 supports a single touchpad (the one connected to the primary HU display). Marked repeated for future expansion. The touchpad resolution can be smaller than the touchscreen resolution.
     * All reported TouchEvents should be absolute within the declared coordinate space.
     */
    repeated TouchPad touchpad = 3;
    /** List of supported feedback events. If FEEDBACK_DRAG_START is supported, FEEDBACK_DRAG_END must be supported as well. */
    repeated FeedbackEvent feedback_events_supported = 4;

    message TouchScreen {
        /** Width in pixels. */
        required int32 width = 1;
        /** Height in pixels. */
        required int32 height = 2;
        /** Type of the touchscreen. */
        optional TouchScreenType type = 3;
    }

    message TouchPad {
        /** Width in pixels. */
        required int32 width = 1;
        /** Height in pixels. */
        required int32 height = 2;
        /** Set to true if this touchpad should be used to navigate through the UI. If not set to true, this touchpad will only be used for tasks such as handwriting input. */
        optional bool ui_navigation = 3;
        /** Width in mm. */
        optional int32 physical_width = 4;
        /** Height in mm. */
        optional int32 physical_height = 5;
    }
}

/** <p>The following touch screens are supported:</p> */
enum TouchScreenType {
    CAPACITIVE = 1;
    RESISTIVE = 2;
}

/** <p>The BluetoothPairingMethod service enables a MD to automatically pair a Bluetooth connection with the HU.</p> */
message BluetoothService {
    /** Bluetooth MAC address of the HU. */
    required string car_address = 1;
    /** Supported pairing methods. */
    repeated BluetoothPairingMethod supported_pairing_methods = 2 [packed=true];
}

/** <p>ALP supports the following pairing methods.</p> */
enum BluetoothPairingMethod {
    /** Secure simple pairing. */
    BLUETOOTH_PAIRING_OOB = 1;
    /** Both sides display a number, user authorizes. */
    BLUETOOTH_PAIRING_NUMERIC_COMPARISON = 2;
    /** Passkey based pairing. */
    BLUETOOTH_PAIRING_PASSKEY_ENTRY = 3;
    /** Pin based pairing. */
    BLUETOOTH_PAIRING_PIN = 4;
}

/**
 * <p>The VendorExtensionService enables implementation of vendor specific services over ALP. These services are not interpreted and are treated as raw byte transports, so the application that opens a
 * channel to such a service must interpret the contents.
 * <div class="note">NOTE: Payload of each vendor extension message is restricted to 512KB unless that specific extension or message explicitly allows bigger data. </div></p>
 */
message VendorExtensionService {
    /** Must use the form <em>com.make.model.serviceName<em>. */
    required string service_name = 1;
    repeated string package_white_list = 2;
    /** Use to describe the service. */
    optional bytes data = 3;
}

/** <p>A upgrade service accepts files from the MD and update the rom at HU side.</p> */
message UpgradeService {
    required string ic_type = 1;
    required string build = 2;
    required int32 major = 3;
    required int32 minor = 4;
}

/**
 * <h2>Channel Setup</h2>
 * <p>After discovering a service, a MD can establish a channel to the service using the Service ID from the ServiceDiscoveryResponse. Only the MD can open and close channels, making it responsible
 * for dynamic allocation of Channel ID. The MD may reuse Channel ID after receiving a successful ChannelCloseResponse.</p>
 * <p>Channel setup is performed with channel control messages (the frame contains the channel number, and the channel control bit is set). Setup and teardown proceeds as follows:</p>
 * @startSequenceDiagram Channel Setup & Teardown
 * identifier A: MD
 * identifier B: HU
 * message A->B: ChannelOpenRequest, priority, serviceId
 * message B->A: ChannelOpenResponse, status
 * spacer: ... messages exchanged over the channel ...
 * message A->B: ChannelCloseRequest
 * message B->A: ChannelCloseResponse, status
 * @endSequenceDiagram
 * <div class="note">NOTE: The Channel ID appears in the frame header of all channel control messages.</div>
 */
/**
 * @type MESSAGE_CHANNEL_OPEN_REQUEST
 * <p>This message is sent over the channel to be opened.</p>
 */
message ChannelOpenRequest {
    /** -128 is the highest priority, 127 is the lowest priority. */
    required sint32 priority = 1;
    /** Service ID from the ServiceDiscoveryResponse. */
    required int32 service_id = 2;
}

/**
 * @type MESSAGE_CHANNEL_OPEN_RESPONSE
 * <p>This message is sent over the channel to be opened (regardless of whether the channel was opened successfully or not).</p>
 */
message ChannelOpenResponse {
    /** Status of the channel open operation. */
    required MessageStatus status = 1;
    /** Message existance status. */
    optional uint32 message_existance = 2;
}

/**
 * @type MESSAGE_CHANNEL_CLOSE_NOTIFICATION
 * <p>This message is sent over the channel to be closed. Data is implicit (message does not contain fields).</p>
 */
message ChannelCloseNotification { }

/**
 * <h2>Diagnostics</h2>
 * <p>ALP includes ping request and response messages for running diagnostics.</p>
 */
/**
 * @type MESSAGE_PING_REQUEST
 * <p>This message can be sent by HU or MD to ping the other side. Recipients should respond to the ping as soon as possible. Use for measuring round trip delays and keepalives.</p>
 */
message PingRequest {
    /** Local timestamp of the sender in nanoseconds. */
    required int64 timestamp = 1;
    /** Indicates whether the other side should save a bug report. */
    optional bool bug_report = 2;
}

/**
 * @type MESSAGE_PING_RESPONSE
 * <p>This message is sent in response to a PingRequest. The timestamp is copied from the PingRequest to serve as a unique identifier and to inform the sender of the ping-computed round trip time.</p>
 */
message PingResponse {
    required int64 timestamp = 1;
}

/**
 * <h2>ByeBye</h2>
 * <p>ALP includes messages that enable each side (HU and MD) to notify  the other side that projection is complete and to provide a reason for completion. The ByeByeRequest receiver replies with a
 * ByeByeResponse, after which message exchange ceases and projection stops.</p>
 */
/**
 * @type MESSAGE_BYEBYE_REQUEST
 * <p>This message notifies the other side that projection is finished due to the supplied reason.</p>
 */
message ByeByeRequest {
    required ByeByeReason reason = 1;
}

enum ByeByeReason {
    /**
     * User selected to finish projection mode; receiver should not automatically re-start projection. If connectivity was USB, MD may do re-enumeration but the HU should not restart AOAP unless the
     * user has explicitly disconnected then re-connected the USB cable.
     */
    USER_SELECTION = 1;
}

/**
 * @type MESSAGE_BYEBYE_RESPONSE
 * <p>This message is sent in response to a ByeByeRequest. After sending this message, ALP stops.</p>
 */
message ByeByeResponse { }

/**
 * @type MESSAGE_SCREEN_ORIENTATION_INQUIRE
 * <p>This message is sent by HU. When HU want to know the screen orientation of MD, this message can be used to inquire.</p>
 */
message ScreenOrientationInquire { }

/**
 * @type MESSAGE_SCREEN_ORIENTATION_NOTIFICATION
 * <p>This message is sent by MD. it may be used in two cases:
 * 1. the screen orientation of the MD has changed.
 * 2. as the response for ScreenOrientationInquire.</p>
 */
message ScreenOrientationNotification {
    required ScreenOrientation orientation = 1;
    optional ScreenRotation rotation = 2;
}

enum ScreenOrientation {
    PORTRAIT = 1;
    LANDSCAPE = 2;
}

enum ScreenRotation {
    NORMAL = 1;
    CLOCKWISE = 2;
    FLIP = 3;
    ANTICLOCKWISE = 4;
}

/**
 * @type MESSAGE_FORCE_LANDSCAPE_REQUEST
 * <p>This message is sent by HU. If the HU want to force the MD to landscape status, this messag can be used.</p>
 */
message ForceLandscapeRequest {
    /** Set ture if want to force landscape. */
    required bool force = 1;
}
/**
 * @type MESSAGE_FORCE_LANDSCAPE_RESPONSE
 * <p>This message is sent in response to a ForceLandscapeRequest.</p>
 */
//message ForceLandscapeResponse { }

/**
 * @type MESSAGE_UPDATE_VEHICLE_ID_NOTIFICATION
 */
message UpdateVehicleIdNotification {
    required string vehicle_id = 1;
}

/**
 * @type MESSAGE_RUNNING_STATE_NOTIFICATION
 *  <p>This message is sent by MD to notify the APP's running state. Each time the running state changed, the message would be sent.</p>
 */
message RunningStateNotification {
    required RunningState state = 1;
}

enum RunningState {
    FOREGROUND = 1;
    BACKGROUND = 2;
}

/**
 * @type MESSAGE_RUNNING_STATE_INQUIRE
* <p>This message is sent by HU to inquire the APP's running state.</p>
*/
message RunningStateInquire { }

/**
 * @type MESSAGE_EXIT_REQUEST
 * <p>The HU sends this message to the MD to request that exit the phone app.</p>
 */
message ExitRequest { }

/**
 * @type MESSAGE_EXIT_RESPONSE
 * <p>This message is sent in response to an ExitRequest. After sending this message, phone app will exit, and the HU will shutdown Autolink.</p>
 */
message ExitResponse { }

/**
 * @type MESSAGE_AUTO_ROTATION_REQUEST
 * <p>The HU sends this message to the MD to request to enable or cancel auto-rotation.</p>
 */
message AutoRotationRequest {
    /** Set ture if want to enable the auto rotation switch at phone side. */
    required bool enable = 1;
}

/**
 * @type MESSAGE_AUTO_ROTATION_RESPONSE
 * <p>This message is sent in response to a AutoRotationRequest.</p>
 */
// message AutoRotationResponse { }

/**
 * @type MESSAGE_READ_REQUEST
 * <p>This message is sent by the MD to read some data from HU.</p>
 */
message ReadRequest {
    /** The start address where want to read from. */
    required uint32 address = 1;
    /** The bytes number of what want to read. */
    required uint32 length = 2;
}

/**
 * @type MESSAGE_READ_RESPONSE
 * <p>This message is sent in response to a ReadRequest.</p>
 */
message ReadResponse {
    /** The result of ReadRequest. */
    required bytes data = 1;
}

/**
 * @type MESSAGE_WRITE_REQUEST
 * <p>This message is sent by the MD to write some data to HU.</p>
 */
message WriteRequest {
    /** The bytes what want to write. */
    required bytes data = 1;
    /** The start address where want to write to. */
    required uint32 address = 2;
    /** The bytes number of what want to write. */
    required uint32 length = 3;
}

/**
 * @type MESSAGE_WRITE_RESPONSE
 * <p>This message is sent in response to a WriteRequest.</p>
 */
message WriteResponse {
    required bool ok = 1;
}

/**
 * @type MESSAGE_SCREEN_RESOLUTION_INQUIRE
 * <p>This message is sent by HU. When HU want to know the screen resolution of MD, this message can be used to inquire.</p>
 */
message ScreenResolutionInquire { }

/**
 * @type MESSAGE_SCREEN_RESOLUTION_NOTIFICATION
 * <p>This message is sent by MD. it may be used in two cases:
 * 1. the screen resolution of the MD has changed.
 * 2. as the response for ScreenResolutionInquire.</p>
 */
message ScreenResolutionNotification {
    required uint32 width = 1;
    required uint32 height = 2;
    /** False when responding to a ScreenResolutionInquire; true when unsolicited. */
    optional bool unsolicited = 3;
}

/**
 * @type MESSAGE_TIMEDATE_INQUIRE
 * <p>This message is sent by HU to get remote time and date.</p>
 */
message TimedateInquire { }

/**
 * @type MESSAGE_TIMEDATE_NOTIFICATION
 * <p>This message is sent by HU to get remote time and date.</p>
 */
message TimedateNotification {
    required uint32 year = 1;
    required uint32 month = 2;
    required uint32 day = 3;
    required uint32 hour = 4;
    required uint32 minute = 5;
    required uint32 second = 6;
    optional uint32 nsecond = 7;
    optional uint32 week = 8;
    optional uint32 day_of_week = 9;
}


/**
 * @section Media
 * <p>A media channel streams media data from source to sink. All messages in a media channel use the following encoding scheme:</p>
 * <p>@startTable
 * Field  |Size    | Description
 * Type   |2 bytes |Numeric identifier that describes the message type (list of possible values described below).
 * Payload|variable|Protocol buffer that defines a structured payload if the most significant bit of type is set. The data from here to the end of the message should be considered as a single protocol
 *                  buffer.
 * @endTable</p>
 * <h2>Media Source Messages</h2>
 * <p>A media source can be in one of three states: Init, Idle, or Sending. To transition states, the media source must send the packets shown below:</p>
 * <p><img src='./images/media_source.png'/></p>
 * <p>The diagram below depicts the high level flow between a source and a sink.
 * @startSequenceDiagram Basic Source-Sink Flow
 * identifier A: Source
 * identifier B: Sink
 * message A->B: Setup
 * message B->A: Config, status=READY
 * message A->B: Start
 * message A->B: Data
 * message A->B: Data
 * message B->A: Ack
 * message A->B: Stop
 * @endSequenceDiagram</p>
 * <p>To request a stop, the sink can send a config message with status WAIT. The receiving source should try to send a stop as soon as it receives such a message, while the sink may continue
 * receiving data in the interim.</p>
 * @startSequenceDiagram Stop By Sink
 * identifier A: Source
 * identifier B: Sink
 * message A->B: Setup
 * message B->A: Config, status=READY
 * message A->B: Start
 * message A->B: Data
 * message A->B: Data
 * message B->A: Ack
 * message B->A: Config, status=WAIT
 * message A->B: Stop
 * @endSequenceDiagram
 */

/**
 * @type MEDIA_MESSAGE_SETUP
 * <p>This message is sent by the source to the sink after channel establishment. Must be sent only once.</p>
 */
message Setup {
    /** The selected media codec. */
    required MediaCodecType type = 1;
}

/**
 * @type MEDIA_MESSAGE_START
 * <p>The Start message is by the source to the sink before the source can send media data. The video_configuration_index is selected from the allowed configurations passed from
 * Config.video_configuration_indices. Index order is based on HU preference (higher resolution configurations first) and the MD selects a configuration from that index.
 * <div class="note">NOTE: Due to differences in MD hardware, not all codec resolutions may be supported. However, support of at least 800x480 is mandatory.</div></p>
 * <p>The requested width/margins for the chosen codec resolution MUST be respected and the MD should not render any UI in the margin area. For non-zero margin values:
 * <ul>
 * <li>UI MUST be rendered to center location horizontally and vertically.</li>
 * <li>Margin area should be filled with the same default color.</li>
 * <li>HU display MUST crop margin area so it does not display to end user.</li>
 * </ul></p>
 */
message Start {
    /** Must start at 0 and increase by 1 on every Start message (for every new session). */
    required int32 session_id = 1;
    /** Index of selected video configuration, starting from zero. Use index 0 for the first item from MediaSinkService.video_configs. */
    required uint32 configuration_index = 2;
    /** The adopted video width. */
    optional uint32 configuration_video_width = 3;
    /** The adopted video height. */
    optional uint32 configuration_video_height = 4;
}

/**
 * @type MEDIA_MESSAGE_START_RESPONSE
 * <p>The StartResponse message is used to response message Start.</p>
 */
message StartResponse {
    /** Start successfully or not. */
    required bool ok = 1;
}

/**
 * @type MEDIA_MESSAGE_STOP
 * <p>This message is sent by the source to the sink before stopping media data. The sink can release media resources on receiving this message.</p>
 */
message Stop { }

/**
 * @type MEDIA_MESSAGE_STOP_RESPONSE
 * <p>The StopResponse message is used to response message Stop.</p>
 */
message StopResponse { }

/**
 * <h3>Message Type Data </h3>
 * <p>This message is sent by the source to the sink. The TimeStamp value can be used synchronize audio/video playback; when the Payload includes multiple samples (as in audio), the TimeStamp value is
 * for the first sample. A TimeStamp value of 0 indicates that contents should be played as soon as possible regardless of audio/video synchronization.</p>
 * @startTable
 * Field    |Value
 * Type     |0x0000
 * TimeStamp|uint64 value of time in source side since Jan-1 1970 in micro seconds.
 * Payload  |See Media Type table for details.
 * @endTable
 */

/**
 * @type MEDIA_MESSAGE_CONFIG
 * <p>This message is sent by the sink in response to a setup message. Used to inform the source of the sink&apos;s current status and preferred configurations. The final Config request received by
 * the source supersedes all previous requests.
 * <ul>
 *   <li>WAIT tells the source to transition to STOP. The source assumes WAIT until receipt of the first config message.</li>
 *   <li>READY tells the source to transition to SENDING with the contained configuration. If the source is already sending, it first transitions to STOP.</li>
 * </ul></p>
 * <p>An audio endpoint should send Config only once (at the beginning with STATUS_READY) as audio endpoints do not change configuration dynamically.</p>
 */
message Config {
    /** Status of media setup. */
    required Status status = 1;
    /** Maximum number of frames that can be outstanding. */
    optional uint32 max_unacked = 2;
    /**
     * Indices of configurations (as reported during service discovery) in order of preference.
     * Ignored for audio as all audio configurations declared during service discovery should be always supported.
     */
    repeated uint32 configuration_indices = 3;
    /** The video data transport mode, refer to VideoTransportMode. */
    optional VideoTransportMode video_transport_mode = 4;
    /** Indicates the current status of sink. */
    enum Status {
        /** Not ready to accept data. */
        STATUS_WAIT = 1;
        /** Ready to accept data. */
        STATUS_READY = 2;
    }
    enum VideoTransportMode {
        VIDEO_TRANSPORT_MODE_NONE = 0;
        VIDEO_TRANSPORT_MODE_AOA = 1;
        VIDEO_TRANSPORT_MODE_TCP = 2;
        VIDEO_TRANSPORT_MODE_UDP = 3;
        VIDEO_TRANSPORT_MODE_RTP_OVER_TCP = 4;
        VIDEO_TRANSPORT_MODE_RTP_OVER_UDP = 5;
    }
}

/**
 * @type MEDIA_MESSAGE_ACK
 * <p>This message is sent to acknowledge a data frame and is used for flow control. The definition of a media frame depends on the media type.</p>
 * <p>The message includes the number of frames processed by the sink since the last ACK. The source must track the number of unACKed frames, and must not send more than maxUnacked (from Config
 * message) frames unless sink fails to send ACK for too long. Additionally, the source must drop ACK messages for the wrong session.
 * <div class="note">NOTE: To confirm that it has played all data sent before releasing audio focus, an Audio sink must send ACK for all data frames received until Stop message. ACK can happen after
 * Stop message. And ACKed frames must be played by car unless higher-priority sound overrides it. </div></p>
 */
message Ack {
    required int32 session_id = 1;
    optional uint32 ack = 2;
}

/**
 * <div class="nobreak"><h2>Video Focus</h2>
 * <p>@startSequenceDiagram Video Focus - Gain & Loss.
 * identifier A: MD
 * identifier B: HU
 * spacer: ... initial setup ...
 * message A->B: VideoFocusRequestNotification, projected
 * message B->A: VideoFocusNotification, projected
 * message A->B: Start
 * message A->B: Data
 * message B->A: Ack
 * spacer: ... data transfer continues ...
 * spacer: ... user changes gear to reverse ...
 * message B->A: VideoFocusNotification, native
 * message A->B: Stop
 * spacer: ... user changes gear back to drive ...
 * message B->A: VideoFocusNotification, projected, unsolicited
 * message A->B: Start
 * message A->B: Data
 * message B->A: Ack
 * spacer: ... data transfer continues ...
 * spacer: ... user presses button to exit projected mode ...
 * message A->B VideFocusRequestNotification, native
 * message B->A VideoFocusNotification, native
 * message A->B: Stop
 * @endSequenceDiagram</p>
 *</div>
 */

/**
 * @type VIDEO_MESSAGE_FOCUS_REQUEST
 * <p>This message is sent by the MD when it want to acquire video focus. The MD should not start sending data until focus has been granted by the HU.</p>
 */
message VideoFocusRequestNotification {
    /** ALPv1.0 supports a single display (the one in the head unit). However, future versions may support additional displays. */
    optional int32 disp_channel_id = 1;
    /** Focus mode wanted by the MD. */
    optional VideoFocusMode mode = 2;
    /** Reason for the video focus request. */
    optional VideoFocusReason reason = 3;
}

/** <p>Defines reasons for focus request.</p> */
enum VideoFocusReason {
    /** Reason is unknown or not set. */
    UNKNOWN = 0;
    /** Screen on the MD is off. Used with VIDEO_FOCUS_NATIVE sent from MD. */
    PHONE_SCREEN_OFF = 1;
    /** User has selected to return to native mode. */
    LAUNCH_NATIVE = 2;
    /** No permission of capturing screen */
    NO_PERMISSION = 3;
    /** Wait for permission of capturing screen*/
    WAIT_PERMISSION = 4;
    /** Video Codec unsupport */
    NO_VALID_VIDEO_ENCODER = 5;
}

/**
 * @type VIDEO_MESSAGE_FOCUS_NOTIFICATION
 * <p>This message is sent by the HU to inform the MD of the state of video focus. This can be sent in response to a video focus request or in an unsolicited manner if the user performs an action
 * which causes them to switch to native mode.</p>
 */
message VideoFocusNotification {
    /** Current focus mode. */
    optional VideoFocusMode focus = 1;
    /** False when responding to a video focus request; true when unsolicited. */
    optional bool unsolicited = 2;
}

/**
 * @type VIDEO_MESSAGE_DISPLAY_CONFIGURATION_CHANGE_REQUEST
 * <p>This message is sent by the HU to notify MD a different video configuration is adopted.</p>
 */
message DisplayConfigurationChangeRequest {
    required int32 width = 1;
    required int32 height = 2;
    required ScreenOrientation orientation = 3;
    required int32 density = 4;
}

/**
 * @type VIDEO_MESSAGE_DISPLAY_CONFIGURATION_CHANGE_RESPONSE
 * <p>This message is sent by the HU to response a DisplayConfigurationChangeRequest.</p>
 */
message DisplayConfigurationChangeResponse { }

/** <p>Denotes the side (HU or MD) allowed to render to the head unit display.</p> */
enum VideoFocusMode {
    /** MD controls the display. */
    VIDEO_FOCUS_PROJECTED = 1;
    /** HU controls the display. */
    VIDEO_FOCUS_NATIVE = 2;
    /**
     * The HU has transient control of the display. This state indicates that the HU expects to retain focus for only a short time, for example when showing a side camera due to a turn signal. If new
     * events (such as user input) cause that assumption to no longer be valid, the HU should notify the phone of the new VideoFocusMode.
     */
    VIDEO_FOCUS_NATIVE_TRANSIENT = 3;
}

/**
 * @suppress
 */
enum MediaMessageId {
    MEDIA_MESSAGE_DATA = 0x0000;
    MEDIA_MESSAGE_CODEC_CONFIG = 0x0001;
    MEDIA_MESSAGE_SETUP = 0x0002;
    MEDIA_MESSAGE_START = 0x0003;
    MEDIA_MESSAGE_STOP = 0x0004;
    MEDIA_MESSAGE_CONFIG = 0x0005;
    MEDIA_MESSAGE_ACK = 0x0006;
    MEDIA_MESSAGE_START_RESPONSE = 0x0007;
    MEDIA_MESSAGE_STOP_RESPONSE = 0x0008;
};

enum VideoMessageId {
    VIDEO_MESSAGE_FOCUS_REQUEST = 0x1000;
    VIDEO_MESSAGE_FOCUS_NOTIFICATION = 0x1001;
    VIDEO_MESSAGE_ORIENTATION_NOTIFICATION = 0x1002;
    VIDEO_MESSAGE_DISPLAY_CONFIGURATION_CHANGE_REQUEST = 0x1003;
    VIDEO_MESSAGE_DISPLAY_CONFIGURATION_CHANGE_RESPONSE = 0x1004;
};


/**
 * @section Input
 * <p>This section describes how input events on the head unit are redirected to the MD. During service discovery, the HU reports its input device and associated keycodes, after which the MD binds to
 * the keys it is interested in. The ALP Receiver Library sends input events only for the keys to which the MD is bound.</p>
 * @startSequenceDiagram Input Events
 * identifier A: MD
 * identifier B: HU
 * message A->B: KeyBindingRequest, keycodes
 * message B->A: KeyBindingResponse, status
 * message B->A: InputReport, events
 * message B->A: InputReport, events
 * @endSequenceDiagram
 */

/**
 * @type INPUT_MESSAGE_KEY_BINDING_REQUEST
 * <p>This message is sent by a MD to notify the HU of the keys in which the MD is interested. The MD may send this message at any time to refresh and adjust bindings dynamically. The MD must refresh
 * the entire set of key bindings (no delta support).</p>
 */
message KeyBindingRequest {
    /** List of key codes the MD wants to handle. */
    repeated int32 keycodes = 1 [packed=true];
}

/**
 * @type INPUT_MESSAGE_KEY_BINDING_RESPONSE
 * <p>This message is sent by a HU to indicate the status of key binding. If this message succeeds, both sides (HU and MD) must immediately respect the new key binding.</p>
 */
message KeyBindingResponse {
    /** Status of the key binding operation. */
    required int32 status = 1;
}

/**
 * @type INPUT_MESSAGE_INPUT_REPORT
 * <p>This message is sent by HU to report input events to the MD. To reduce input latency, the HU typically sends this message as soon as the event is available (although batching is supported).</p>
 */
message InputReport {
    /** Timestamp of the input event, in nanoseconds. */
    required uint64 timestamp = 1;
    /** Channel id of the associated display (ignored in ALPv1.0).*/
    optional int32 disp_channel_id = 2;
    /** Indicates a touch event on a touchscreen. */
    optional TouchEvent touch_event = 3;
    /** Indicates a key press. */
    optional KeyEvent key_event = 4;
    /** Indicates use of input devices with absolute values (such as dials and sliders with specific ranges). */
    optional AbsoluteEvent absolute_event = 5;
    /** Indicates use of MD with relative values (such as freely rotating dials). */
    optional RelativeEvent relative_event = 6;
    /** Indicates a touch gesture on a touchpad. */
    optional TouchEvent touchpad_event = 7;
}

/** <p>Used to model key or button presses.</p> */
message KeyEvent {
    /** List of key events that occurred. */
    repeated Key keys = 1;
    /** <p>Specifies keycode and status.</p>*/
    message Key {
        /** See the appendix on key codes. */
        required uint32 keycode = 1;
        /** True if pressed, false if released. */
        required bool down = 2;
        /** Bitmap of the states of all the meta keys. */
        required uint32 metastate = 3;
        /** Set when you cannot send initial key down (pressed) but need to indicate a long press. Must follow with a key up (released). */
        optional bool longpress = 4;
    }
}

/** <p>Used to model events that occur on a touchscreen input device.</p> */
message TouchEvent {
    /** Data about all pointers. */
    repeated Pointer pointer_data = 1;
    /** Index of the pointer that changed. Index != Id. */
    optional uint32 action_index = 2;
    /** Action taken by user. */
    optional PointerAction action = 3;
    /** <p>Specifies pointer coordinates and ID.</p> */
    message Pointer {
        /** X coordinate of the pointer, in pixels. */
        required uint32 x = 1;
        /** Y coordinate of the pointer, in pixels. */
        required uint32 y = 2;
        /** ID of this pointer. */
        required uint32 pointer_id = 3;
    }
}

message WlTouchEvent {
    required uint32 x = 1;
    required uint32 y = 2;
    required PointerAction action = 3;
    required int64 timestamp = 4;
}

/** <p>Used to represent changes in pointer state.</p> */
enum PointerAction {
    /** First finger went down. */
    ACTION_DOWN = 0;
    /** Last finger went up. */
    ACTION_UP = 1;
    /** Finger(s) were moved. */
    ACTION_MOVED = 2;
    /** 2nd-nth finger went down. */
    ACTION_POINTER_DOWN = 5;
    /** nth-2nd last finger went up. */
    ACTION_POINTER_UP = 6;
}

/**
 * <p>Used to represent MD with absolute values, such as a slider or volume dial that goes to 11. As these MD may have different properties, the supplied value must be interpreted using knowledge of
 * the input device (obtained from the keycode).</p>
 */
message AbsoluteEvent {
    /** List of events. */
    repeated Abs data = 1;
    /** <p>Specifies keycode and value for the input device.</p> */
    message Abs {
        /** Unique code for this MD. Uses the same address space as other key codes.*/
        required uint32 keycode = 1;
        /** New value of this MD. Use a fixed point value to represent fractional values.*/
        required int32 value = 2;
    }
}

/**
 * <p>Used to represent MD such as a freely rotating dials that operate on deltas in a direction (and which do not have specific values). As these MD may have different properties, the supplied value
 * must be interpreted using knowledge of the input device (obtained from the keycode).</p>
 */
message RelativeEvent {
    /** The list of events. */
    repeated Rel data = 1;
    /** <p>Specifies keycode and value for the input device.</p> */
    message Rel {
        /** Unique code for this MD. Uses the same address space as other key codes.*/
        required uint32 keycode = 1;
        /** Delta (amount of change in MD value). Use a fixed point value to represent fractional values. */
        required int32 delta = 2;
    }
}

/** <p>Types of feedback events that the MD can send to the HU.</p> */
enum FeedbackEvent {
    /** Sent by the MD when a select action has occured. */
    FEEDBACK_SELECT = 1;
    /** Sent by the MD when focus changes due to a touchpad input. */
    FEEDBACK_FOCUS_CHANGE = 2;
    /** Sent by the MD when a drag event triggers a select action. */
    FEEDBACK_DRAG_SELECT = 3;
    /** Sent by the MD when a continuous drag event starts. */
    FEEDBACK_DRAG_START = 4;
    /** Sent by the MD when a continuous drag event ends. */
    FEEDBACK_DRAG_END = 5;
}

/**
 * @type INPUT_MESSAGE_INPUT_FEEDBACK
 * <p>Used by the MD to send input feedback events to the HU. Feedback events can be used to trigger haptic or audio feedback.</p>
 */
message InputFeedback {
    optional FeedbackEvent event = 1;
}

/**
 * @suppress
 */
enum InputMessageId {
    INPUT_MESSAGE_INPUT_REPORT = 0x8001;
    INPUT_MESSAGE_KEY_BINDING_REQUEST = 0x8002;
    INPUT_MESSAGE_KEY_BINDING_RESPONSE = 0x8003;
    INPUT_MESSAGE_INPUT_FEEDBACK = 0x8004;
}


/**
 * @section Automatic Bluetooth Pairing
 * <p>The Bluetooth service is used to negotiate the connection over Bluetooth. In ALPv1.0, all in-call audio is transmitted via Bluetooth, so the primary use case of this service is to negotiate a
 * Bluetooth connection for the purpose of transmitting in-call audio. During service discovery, the HU lists supported pairing methods and the MD selects one of the available methods.</p>
 * <p>
 * @startSequenceDiagram Bluetooth Pairing (Not Paired)
 * identifier A: MD
 * identifier B: HU
 * message A->B: BluetoothPairingRequest
 * spacer: ... HU ready to pair in one second ...
 * spacer: ... HU does not have pairing information for the MD ...
 * message B->A: BluetoothPairingResponse, status=STATUS_SUCCESS, already_paired=false
 * spacer: ... MD initiates pairing. ...
 * message B->A: BluetoothAuthenticationData
 * @endSequenceDiagram</p>
 * <p>
 * @startSequenceDiagram Delayed Bluetooth Pairing
 * identifier A: MD
 * identifier B: HU
 * message A->B: BluetoothPairingRequest
 * spacer: ... HU not ready to pair in one second ...
 * message B->A: BluetoothPairingResponse, status=STATUS_BLUETOOTH_PAIRING_DELAYED
 * spacer: ... HU is now ready to be paired  ...
 * spacer: ... HU does not have pairing information for the MD ...
 * message B->A: BluetoothPairingResponse, status=STATUS_SUCCESS, already_paired=false
 * spacer: ... MD initiates pairing ...
 * message B->A: BluetoothAuthenticationData
 * spacer: ... MD and HU will be paired ...
 * spacer: ... MD connects with the HU using HFP (Hands-Free Profile) ...
 * @endSequenceDiagram</p>
 * <p>
 * @startSequenceDiagram Bluetooth Pairing (Already Paired)
 * identifier A: MD
 * identifier B: HU
 * message A->B: BluetoothPairingRequest
 * spacer: ... HU ready to pair in one second ...
 * spacer: ... HU has pairing information for the MD ...
 * message B->A: BluetoothPairingResponse, status=STATUS_SUCCESS, already_paired=true
 * spacer: ... MD has pairing information for the HU ...
 * spacer: ... no need to pair ...
 * spacer: ... MD connects with the HU using HFP (Hands-Free Profile) ...
 * @endSequenceDiagram</p>
 * <p>
 * @startSequenceDiagram Bluetooth Pairing (MD Lost Pairing Info)
 * identifier A: MD
 * identifier B: HU
 * message A->B: BluetoothPairingRequest
 * spacer: ... HU ready to pair in one second ...
 * spacer: ... HU has pairing information for the MD ...
 * message B->A: BluetoothPairingResponse, status=STATUS_SUCCESS, already_paired=true
 * spacer: ... MD does not have pairing information for the HU ...
 * spacer: ... MD initiates pairing ...
 * message B->A: BluetoothAuthenticationData
 * @endSequenceDiagram</p>
 * <p>
 * @startSequenceDiagram Bluetooth Pairing (Paring Info Corrupt)
 * identifier A: MD
 * identifier B: HU
 * message A->B: BluetoothPairingRequest
 * spacer: ... HU ready to pair in one second ...
 * spacer: ... HU has pairing information for the MD ...
 * message B->A: BluetoothPairingResponse, status=STATUS_SUCCESS, already_paired=true
 * spacer: ... MD has pairing information for the HU ...
 * spacer: ... no need to pair. ...
 * spacer: ... MD fails to connect with the HU using HFP (Hands-Free Profile) ...
 * spacer: ... MD unpairs and attempts to pair again ...
 * message A->B: BluetoothPairingRequest
 * message B->A: BluetoothPairingResponse, status=STATUS_SUCCESS, already_paired=true
 * spacer: ... MD does not have pairing information for the HU ...
 * spacer: ... MD initiates pairing ...
 * message B->A: BluetoothAuthenticationData
 * @endSequenceDiagram</p>
 */

/**
 * @type BLUETOOTH_MESSAGE_PAIRING_REQUEST
 * <p>This message is sent by a MD to request exchange of Bluetooth pairing information as necessary to complete Bluetooth pairing. Receiving HU must send a BluetoothPairingResponse within 1 second.
 * This message may also be resent by a MD attempting to fix a corrupted state.</p>
 * <p>The message must include the MD Bluetooth address and selected pairing method. Android 4.4 devices support two pairing methods: BLUETOOTH_PAIRING_NUMERIC_COMPARISON and BLUETOOTH_PAIRING_PIN. If
 * the MD does not support any of the pairing methods listed service discovery message (BluetoothService) from the HU, the MD will not issue a BluetoothPairingRequest message.</p>
 */
message BluetoothPairingRequest {
    /** Bluetooth address of the MD, i.e. 00:11:22:AA:BB:CC. */
    required string phone_address = 1;
    /** Bluetooth pairing method to use. */
    required BluetoothPairingMethod pairing_method = 2;
}

/**
 * <div class="note">NOTE: All fields are little-endian following the Bluetooth convention for out-of-band (OOB) data.</div>
 */
/**
 * @type BLUETOOTH_MESSAGE_PAIRING_RESPONSE
 * <p>This message is sent by a HU in response to BluetoothPairingRequest. Because requests may be resent by a MD attempting to fix a corrupted state, HU must be able to handle multiple
 * BluetoothPairingRequest messages in the same ALP connection. In addition, a HU must send a response within 1 second of receiving the BluetoothPairingRequest.</p>
 * <p>If the HU <b>can</b> be ready to pair within 1 second of receiving the BluetoothPairingRequest, it must do so using the pairing method specified in the request before sending a
 * BluetoothPairingResponse with STATUS_SUCCESS as the status code.
 * <ul>
 * <li><em>Readiness</em>. To be ready, the HU must disconnect all other HFP (Hands-Free Profile) connections, set the ALP device priority as the highest, and make the HU Bluetooth module discoverable
 * by the MD.
 * <li><em>Previously Paired</em>. The HU must send a BluetoothPairingResponse even when already paired with the MD. (Necessary for cases where the MD loses pairing information and must re-initiate
 * pairing.)
 * <li><em>Success</em>. When the HU sends a BluetoothPairingResponse with STATUS_SUCCESS as the status code, the HU must include already_paired field in the message. This field value must be true
 * when the HU already has a link key (pairing information) for the ALP device phone (the HU believes it is already paired with the MD).
 * <li><em>Pairing</em>. After receiving a BluetoothPairingResponse with STATUS_SUCCESS as the status code, the MD initiates the pairing-and-connection process with the HU. If each side has the link
 * key (pairing information) for the other side, the pairing step is skipped and the MD initiates the HFP connection only.
 * </ul></p>
 * <p>If the HU <b>cannot</b> be ready to pair within 1 second of receiving a BluetoothPairingRequest, the HU should send a BluetoothPairingResponse with STATUS_BLUETOOTH_PAIRING_DELAYED as the status
 * code. When the MD receives this message, it ignores the already_paired field and assumes the HU will eventually send another BluetoothPairingResponse with STATUS_SUCCESS as the status code.</p>
 */
message BluetoothPairingResponse {
    /** Result of the Bluetooth pairing request. */
    required MessageStatus status = 1;
    /** Indicates if the HU already has a link key (pairing info) for the MD. */
    required bool already_paired = 2;
}

/**
 * @type BLUETOOTH_MESSAGE_AUTHENTICATION_DATA
 * <p>This message is sent by a HU to provide authentication data for Bluetooth pairing. The MD uses authentication data to complete pairing with the HU through Bluetooth. If authentication data is
 * valid, the MD and HU pair and connect to HFP.</p>
 */
message BluetoothAuthenticationData {
    /** Bluetooth pairing authentication data (for example, a passkey or PIN). */
    required string auth_data = 1;
}

/**
 * @type BLUETOOTH_MESSAGE_PHONE_STATUS_INQUIRE
 * <p>This message is sent by a HU to get the BT status at phone side.</p>
 */
message BluetoothPhoneStatusInquire { }

/**
 * @type BLUETOOTH_MESSAGE_PHONE_STATUS_NOTIFICATION
 * <p>This message can be sent unsolicited or in response to a BluetoothPhoneStatusInquire.</p>
 */
message BluetoothPhoneStatusNotification {
    /**
     * Current Bluetooth status at phone side.
     * status[0]: already paired or not
     * status[1]: HeadSet profile is connected or not
     * status[2]: A2DP profile is connected or not
     * status[3]: Health profile is connected or not
     * status[4]: HID profile is connected or not
     * status[5-31]: reserved
     */
    required int32 status = 1;
    /** False when responding to a PhoneBluetoothStatusInquire; true when unsolicited. */
    optional bool unsolicited = 2;
}

/**
 * @suppress
 */
enum BluetoothMessageId {
    BLUETOOTH_MESSAGE_PAIRING_REQUEST = 0x8001;
    BLUETOOTH_MESSAGE_PAIRING_RESPONSE = 0x8002;
    BLUETOOTH_MESSAGE_AUTHENTICATION_DATA = 0x8003;
    BLUETOOTH_MESSAGE_PHONE_STATUS_INQUIRE = 0x8004;
    BLUETOOTH_MESSAGE_PHONE_STATUS_NOTIFICATION = 0x8005;
}


/**
 * @section Upgrade
 * <p>The Upgrade service is used to upgrade rom at HU side.</p>
 */

/**
 * @type MSG_UPGRADE_INFO_REQUEST
 * <p>This message is sent by HU to obtain the upgrade information.</p>
 */
message UpgradeInfoRequest {
    /** The files information before upgrade. */
    repeated UpgradeFileInfo files = 1;
}

/**
 * @type MSG_UPGRADE_INFO_NOTIFICATION
 * <p>This message is sent by MD to response a UpgradeInfoRequest.</p>
 */
message UpgradeInfoNotification {
    /** The number of the upgradeable files. */
    required int32 file_number = 1;
    /** The size of the upgradeable files.(unit:bytes) */
    required int32 total_size = 2;
}

/**
 * @type MSG_UPGRADE_REQUEST
 * <p>This message is sent by HU to trigger upgrading. It is not necessary if MD trigger upgrading is wanted.</p>
 */
message UpgradeRequest { }

/**
 * @type MSG_UPGRADE_DOWNLOAD_START
 * <p>This message is sent by MD to notify upgradeable files will be downloaded from remote server.</p>
 */
message UpgradeDownloadStart { }

/**
 * @type MSG_UPGRADE_DOWNLOAD_FINISHED
 * <p>This message is sent by MD to notify upgradeable files finished to download.</p>
 */
message UpgradeDownloadFinished { }

/**
 * @type MSG_UPGRADE_DOWNLOAD_PROGRESS_NOTIFICATION
 * <p>This message is sent by MD to notify the progress when upgradeable files are being downloaded.</p>
 */
message UpgradeDownloadProgressNotification {
    /** Downloading progress bar. */
    required int32 percent = 1;
}

/**
 * @type MSG_UPGRADE_START
 * <p>This message is sent by MD to notify the upgrading will be started.</p>
 */
message UpgradeStart { }

/**
 * @type MSG_UPGRADE_READY
 * <p>This message is sent by HU to response a UpgradeStart. The upgradeable could be transfered later.</p>
 */
message UpgradeReady { }

/**
 * @type MSG_UPGRADE_FILE_START
 * <p>This message is sent by MD to notify a file transfer is started. HU will create a new file according to the file information.</p>
 */
message UpgradeFileStart {
    /** The file information who is upgradeable. */
    required UpgradeFileInfo file = 1;
}

/**
 * @type MSG_UPGRADE_FILE_DATA
 * <p>Upgradeable file data. HU just save the data.</p>
 */
// message UpgradeFileData { }

/**
 * @type MSG_UPGRADE_FILE_ACK
 * <p>This message is sent by HU to ack a UpgradeFileData.</p>
 */
message UpgradeFileAck {
    /** The number received bytes. */
    required int32 received_size = 1;
}

/**
 * @type MSG_UPGRADE_FILE_FINISHED
 * <p>This message is sent by MD to notify a file is transfered completely.</p>
 */
message UpgradeFileFinished { }

/**
 * @type MSG_UPGRADE_FILE_CHECKSUM
 * <p>This message is sent by HU to response a UpgradeFileFinished. MD had better check the checksum.</p>
 */
message UpgradeFileChecksum {
    /** The checksum of this upgradeable file. */
    required int32 checksum = 1;
}

/**
 * @type MSG_UPGRADE_FILE_FINISHED
 * <p>This message is sent by MD to notify the upgrading finished.</p>
 */
message UpgradeFinished { }

/**
 * @type MSG_UPGRADE_PROGRESS_NOTIFICATION
 * <p>This message is sent by MD to notify the progress of this upgradeable file.</p>
 */
message UpgradeProgressNotification {
    /** Upgrading progress bar. */
    required int32 percent = 1;
}

/**
 * @type MSG_UPGRADE_TRAPPED_ERROR
 * <p>This message is sent by MD to notify the trapped error.</p>
 */
message UpgradeTrappedError {
    /** The error code. */
    required int32 errcode = 1;
}

/** <p>For checking version, An upgradeable file must include the following information.</p> */
message UpgradeFileInfo {
    /** The name of this file. */
    required string name = 1;
    /** The size of this file.(unit:byte) */
    required int32 size = 2;
    /** The versions of this file. */
    required int32 version_x = 3;
    required int32 version_y = 4;
    required int32 version_z = 5;
    /** The module who this file belongs to. */
    required string module = 6;
}

/**
 * @suppress
 */
enum UpgradeErrorId {
    UPGRADE_ERROR_VERCHECK = 1;
    UPGRADE_ERROR_DOWNFILE = 2;
    UPGRADE_ERROR_GETDATA = 3;
}

/**
 * @suppress
 */
enum UpgradeMessageId {
    MSG_UPGRADE_INFO_REQUEST = 1;
    MSG_UPGRADE_INFO_NOTIFICATION = 2;
    MSG_UPGRADE_REQUEST = 3;
    MSG_UPGRADE_DOWNLOAD_START = 4;
    MSG_UPGRADE_DOWNLOAD_FINISHED = 5;
    MSG_UPGRADE_DOWNLOAD_PROGRESS_NOTIFICATION = 6;
    MSG_UPGRADE_START = 7;
    MSG_UPGRADE_READY = 8;
    MSG_UPGRADE_FILE_START = 9;
    MSG_UPGRADE_FILE_DATA = 10;
    MSG_UPGRADE_FILE_ACK = 11;
    MSG_UPGRADE_FILE_FINISHED = 12;
    MSG_UPGRADE_FILE_CHECKSUM = 13;
    MSG_UPGRADE_FINISHED = 14;
    MSG_UPGRADE_PROGRESS_NOTIFICATION = 15;
    MSG_UPGRADE_TRAPPED_ERROR = 16;
}


/**
 * @section Appendix I: Status Codes
 * <p>This appendix lists supported status codes.</p>
 */
/**<p>A message can return the status of an operation as one of the following status codes:
 * <div class="note">NOTE: The Android cpp compiler supports signed enums; when using a different compiler that does not support signed enums, you must redefine these values in your code.</div></p>
 */
enum MessageStatus {
    STATUS_UNSOLICITED_MESSAGE = 1;
    /** Previous message handled successfully. */
    STATUS_SUCCESS = 0;
    /** Sender and receiver cannot agree on a single version, communication cannot continue. */
    STATUS_NO_COMPATIBLE_VERSION = -1;
    /** Certificate presented was invalid. */
    STATUS_CERTIFICATE_ERROR = -2;
    /** Remote end could not be authenticated. */
    STATUS_AUTHENTICATION_FAILURE = -3;
    /** Service ID specified is not one of the services advertised during service discovery. */
    STATUS_INVALID_SERVICE = -4;
    /** Channel ID specified was invalid. */
    STATUS_INVALID_CHANNEL = -5;
    /** Priority specified was invalid. */
    STATUS_INVALID_PRIORITY = -6;
    /** Internal error, recommend to tear down connection to avoid state inconsistency. */
    STATUS_INTERNAL_ERROR = -7;
    /** Media configuration requested is not supported. */
    STATUS_MEDIA_CONFIG_MISMATCH = -8;
    /** Sensor ID specified is not requested. */
    STATUS_INVALID_SENSOR = -9;
    /** Cannot pair Bluetooth now (another MD might be connected with an active call). */
    STATUS_BLUETOOTH_PAIRING_DELAYED = -10;
    /** Bluetooth is not available. */
    STATUS_BLUETOOTH_UNAVAILABLE = -11;
    /** Bluetooth MAC address specified is invalid. */
    STATUS_BLUETOOTH_INVALID_ADDRESS = -12;
    /** Bluetooth pairing method specified is invalid. */
    STATUS_BLUETOOTH_INVALID_PAIRING_METHOD = -13;
    STATUS_BLUETOOTH_INVALID_AUTH_DATA = -14;
    STATUS_BLUETOOTH_AUTH_DATA_MISMATCH = -15;
    STATUS_BLUETOOTH_HFP_ANOTHER_CONNECTION = -16;
    STATUS_BLUETOOTH_HFP_CONNECTION_FAILURE = -17;
    /** Keycode reported is not handled by the MD. */
    STATUS_KEYCODE_NOT_BOUND = -18;
    /** Can't tune to station b/c it's invalid. */
    STATUS_RADIO_INVALID_STATION = -19;
    STATUS_INVALID_INPUT = -20;
    /** Error occurred retrieving presets. */
    STATUS_RADIO_STATION_PRESETS_NOT_SUPPORTED = -21;
    /** Radio communication error occurred. */
    STATUS_RADIO_COMM_ERROR = -22;
    /** Command not supported. */
    STATUS_COMMAND_NOT_SUPPORTED = -250;
    /** Internal error code, not sent over the protocol. */
    STATUS_FRAMING_ERROR = -251;
    /** Internal error code, not sent over the protocol. */
    STATUS_UNEXPECTED_MESSAGE = -253;
    /** Internal error code, not sent over the protocol. */
    STATUS_BUSY = -254;
    /** Internal error code, not sent over the protocol. */
    STATUS_NO_MEMORY = -255;
}


/**
 * @section Appendix II: Key Codes
 * <p>This section lists keycodes accepted by the MD. Whenever possible, each keycode has the same numeric value as the corresponding Android keycode.</p>
 */
/** <p>HU can send the following keycodes to a MD:</p> */
enum KeyCode {
    /** Copied from Android. */
    KEYCODE_UNKNOWN         = 0;
    /** Copied from Android. */
    KEYCODE_SOFT_LEFT       = 1;
    /** Copied from Android. */
    KEYCODE_SOFT_RIGHT      = 2;
    /** Copied from Android. */
    KEYCODE_HOME            = 3;
    /** Copied from Android. */
    KEYCODE_BACK            = 4;
    /** Copied from Android. */
    KEYCODE_CALL            = 5;
    /** Copied from Android. */
    KEYCODE_ENDCALL         = 6;
    /** Copied from Android. */
    KEYCODE_0               = 7;
    /** Copied from Android. */
    KEYCODE_1               = 8;
    /** Copied from Android. */
    KEYCODE_2               = 9;
    /** Copied from Android. */
    KEYCODE_3               = 10;
    /** Copied from Android. */
    KEYCODE_4               = 11;
    /** Copied from Android. */
    KEYCODE_5               = 12;
    /** Copied from Android. */
    KEYCODE_6               = 13;
    /** Copied from Android. */
    KEYCODE_7               = 14;
    /** Copied from Android. */
    KEYCODE_8               = 15;
    /** Copied from Android. */
    KEYCODE_9               = 16;
    /** Copied from Android. */
    KEYCODE_STAR            = 17;
    /** Copied from Android. */
    KEYCODE_POUND           = 18;
    /** Copied from Android. */
    KEYCODE_DPAD_UP         = 19;
    /** Copied from Android. */
    KEYCODE_DPAD_DOWN       = 20;
    /** Copied from Android. */
    KEYCODE_DPAD_LEFT       = 21;
    /** Copied from Android. */
    KEYCODE_DPAD_RIGHT      = 22;
    /** Copied from Android. */
    KEYCODE_DPAD_CENTER     = 23;
    /** Copied from Android. */
    KEYCODE_VOLUME_UP       = 24;
    /** Copied from Android. */
    KEYCODE_VOLUME_DOWN     = 25;
    /** Copied from Android. */
    KEYCODE_POWER           = 26;
    /** Copied from Android. */
    KEYCODE_CAMERA          = 27;
    /** Copied from Android. */
    KEYCODE_CLEAR           = 28;
    /** Copied from Android. */
    KEYCODE_A               = 29;
    /** Copied from Android. */
    KEYCODE_B               = 30;
    /** Copied from Android. */
    KEYCODE_C               = 31;
    /** Copied from Android. */
    KEYCODE_D               = 32;
    /** Copied from Android. */
    KEYCODE_E               = 33;
    /** Copied from Android. */
    KEYCODE_F               = 34;
    /** Copied from Android. */
    KEYCODE_G               = 35;
    /** Copied from Android. */
    KEYCODE_H               = 36;
    /** Copied from Android. */
    KEYCODE_I               = 37;
    /** Copied from Android. */
    KEYCODE_J               = 38;
    /** Copied from Android. */
    KEYCODE_K               = 39;
    /** Copied from Android. */
    KEYCODE_L               = 40;
    /** Copied from Android. */
    KEYCODE_M               = 41;
    /** Copied from Android. */
    KEYCODE_N               = 42;
    /** Copied from Android. */
    KEYCODE_O               = 43;
    /** Copied from Android. */
    KEYCODE_P               = 44;
    /** Copied from Android. */
    KEYCODE_Q               = 45;
    /** Copied from Android. */
    KEYCODE_R               = 46;
    /** Copied from Android. */
    KEYCODE_S               = 47;
    /** Copied from Android. */
    KEYCODE_T               = 48;
    /** Copied from Android. */
    KEYCODE_U               = 49;
    /** Copied from Android. */
    KEYCODE_V               = 50;
    /** Copied from Android. */
    KEYCODE_W               = 51;
    /** Copied from Android. */
    KEYCODE_X               = 52;
    /** Copied from Android. */
    KEYCODE_Y               = 53;
    /** Copied from Android. */
    KEYCODE_Z               = 54;
    /** Copied from Android. */
    KEYCODE_COMMA           = 55;
    /** Copied from Android. */
    KEYCODE_PERIOD          = 56;
    /** Copied from Android. */
    KEYCODE_ALT_LEFT        = 57;
    /** Copied from Android. */
    KEYCODE_ALT_RIGHT       = 58;
    /** Copied from Android. */
    KEYCODE_SHIFT_LEFT      = 59;
    /** Copied from Android. */
    KEYCODE_SHIFT_RIGHT     = 60;
    /** Copied from Android. */
    KEYCODE_TAB             = 61;
    /** Copied from Android. */
    KEYCODE_SPACE           = 62;
    /** Copied from Android. */
    KEYCODE_SYM             = 63;
    /** Copied from Android. */
    KEYCODE_EXPLORER        = 64;
    /** Copied from Android. */
    KEYCODE_ENVELOPE        = 65;
    /** Copied from Android. */
    KEYCODE_ENTER           = 66;
    /** Backspace. See also KEYCODE_FORWARD_DEL. */
    KEYCODE_DEL             = 67;
    /** Backtick. */
    KEYCODE_GRAVE           = 68;
    /** Copied from Android. */
    KEYCODE_MINUS           = 69;
    /** Copied from Android. */
    KEYCODE_EQUALS          = 70;
    /** Copied from Android. */
    KEYCODE_LEFT_BRACKET    = 71;
    /** Copied from Android. */
    KEYCODE_RIGHT_BRACKET   = 72;
    /** Copied from Android. */
    KEYCODE_BACKSLASH       = 73;
    /** Copied from Android. */
    KEYCODE_SEMICOLON       = 74;
    /** Copied from Android. */
    KEYCODE_APOSTROPHE      = 75;
    /** Copied from Android. */
    KEYCODE_SLASH           = 76;
    /** Copied from Android. */
    KEYCODE_AT              = 77;
    /** Copied from Android. */
    KEYCODE_NUM             = 78;
    /** Copied from Android. */
    KEYCODE_HEADSETHOOK     = 79;
    /** Camera Focus. */
    KEYCODE_FOCUS           = 80;
    /** Copied from Android. */
    KEYCODE_PLUS            = 81;
    /** Copied from Android. */
    KEYCODE_MENU            = 82;
    /** Copied from Android. */
    KEYCODE_NOTIFICATION    = 83;
    /** Copied from Android. */
    KEYCODE_SEARCH          = 84;
    /** Copied from Android. */
    KEYCODE_MEDIA_PLAY_PAUSE= 85;
    /** Copied from Android. */
    KEYCODE_MEDIA_STOP      = 86;
    /** Copied from Android. */
    KEYCODE_MEDIA_NEXT      = 87;
    /** Copied from Android. */
    KEYCODE_MEDIA_PREVIOUS  = 88;
    /** Copied from Android. */
    KEYCODE_MEDIA_REWIND    = 89;
    /** Copied from Android. */
    KEYCODE_MEDIA_FAST_FORWARD = 90;
    /** Copied from Android. */
    KEYCODE_MUTE            = 91;
    /** Copied from Android. */
    KEYCODE_PAGE_UP         = 92;
    /** Copied from Android. */
    KEYCODE_PAGE_DOWN       = 93;
    /** Copied from Android. */
    KEYCODE_PICTSYMBOLS     = 94;
    /** Copied from Android. */
    KEYCODE_SWITCH_CHARSET  = 95;
    /** Copied from Android. */
    KEYCODE_BUTTON_A        = 96;
    /** Copied from Android. */
    KEYCODE_BUTTON_B        = 97;
    /** Copied from Android. */
    KEYCODE_BUTTON_C        = 98;
    /** Copied from Android. */
    KEYCODE_BUTTON_X        = 99;
    /** Copied from Android. */
    KEYCODE_BUTTON_Y        = 100;
    /** Copied from Android. */
    KEYCODE_BUTTON_Z        = 101;
    /** Copied from Android. */
    KEYCODE_BUTTON_L1       = 102;
    /** Copied from Android. */
    KEYCODE_BUTTON_R1       = 103;
    /** Copied from Android. */
    KEYCODE_BUTTON_L2       = 104;
    /** Copied from Android. */
    KEYCODE_BUTTON_R2       = 105;
    /** Copied from Android. */
    KEYCODE_BUTTON_THUMBL   = 106;
    /** Copied from Android. */
    KEYCODE_BUTTON_THUMBR   = 107;
    /** Copied from Android. */
    KEYCODE_BUTTON_START    = 108;
    /** Copied from Android. */
    KEYCODE_BUTTON_SELECT   = 109;
    /** Copied from Android. */
    KEYCODE_BUTTON_MODE     = 110;
    /** Copied from Android. */
    KEYCODE_ESCAPE          = 111;
    /** Copied from Android. */
    KEYCODE_FORWARD_DEL     = 112;
    /** Copied from Android. */
    KEYCODE_CTRL_LEFT       = 113;
    /** Copied from Android. */
    KEYCODE_CTRL_RIGHT      = 114;
    /** Copied from Android. */
    KEYCODE_CAPS_LOCK       = 115;
    /** Copied from Android. */
    KEYCODE_SCROLL_LOCK     = 116;
    /** Copied from Android. */
    KEYCODE_META_LEFT       = 117;
    /** Copied from Android. */
    KEYCODE_META_RIGHT      = 118;
    /** Copied from Android. */
    KEYCODE_FUNCTION        = 119;
    /** Copied from Android. */
    KEYCODE_SYSRQ           = 120;
    /** Copied from Android. */
    KEYCODE_BREAK           = 121;
    /** Copied from Android. */
    KEYCODE_MOVE_HOME       = 122;
    /** Copied from Android. */
    KEYCODE_MOVE_END        = 123;
    /** Copied from Android. */
    KEYCODE_INSERT          = 124;
    /** Copied from Android. */
    KEYCODE_FORWARD         = 125;
    /** Copied from Android. */
    KEYCODE_MEDIA_PLAY      = 126;
    /** Copied from Android. */
    KEYCODE_MEDIA_PAUSE     = 127;
    /** Copied from Android. */
    KEYCODE_MEDIA_CLOSE     = 128;
    /** Copied from Android. */
    KEYCODE_MEDIA_EJECT     = 129;
    /** Copied from Android. */
    KEYCODE_MEDIA_RECORD    = 130;
    /** Copied from Android. */
    KEYCODE_F1              = 131;
    /** Copied from Android. */
    KEYCODE_F2              = 132;
    /** Copied from Android. */
    KEYCODE_F3              = 133;
    /** Copied from Android. */
    KEYCODE_F4              = 134;
    /** Copied from Android. */
    KEYCODE_F5              = 135;
    /** Copied from Android. */
    KEYCODE_F6              = 136;
    /** Copied from Android. */
    KEYCODE_F7              = 137;
    /** Copied from Android. */
    KEYCODE_F8              = 138;
    /** Copied from Android. */
    KEYCODE_F9              = 139;
    /** Copied from Android. */
    KEYCODE_F10             = 140;
    /** Copied from Android. */
    KEYCODE_F11             = 141;
    /** Copied from Android. */
    KEYCODE_F12             = 142;
    /** Copied from Android. */
    KEYCODE_NUM_LOCK        = 143;
    /** Copied from Android. */
    KEYCODE_NUMPAD_0        = 144;
    /** Copied from Android. */
    KEYCODE_NUMPAD_1        = 145;
    /** Copied from Android. */
    KEYCODE_NUMPAD_2        = 146;
    /** Copied from Android. */
    KEYCODE_NUMPAD_3        = 147;
    /** Copied from Android. */
    KEYCODE_NUMPAD_4        = 148;
    /** Copied from Android. */
    KEYCODE_NUMPAD_5        = 149;
    /** Copied from Android. */
    KEYCODE_NUMPAD_6        = 150;
    /** Copied from Android. */
    KEYCODE_NUMPAD_7        = 151;
    /** Copied from Android. */
    KEYCODE_NUMPAD_8        = 152;
    /** Copied from Android. */
    KEYCODE_NUMPAD_9        = 153;
    /** Copied from Android. */
    KEYCODE_NUMPAD_DIVIDE   = 154;
    /** Copied from Android. */
    KEYCODE_NUMPAD_MULTIPLY = 155;
    /** Copied from Android. */
    KEYCODE_NUMPAD_SUBTRACT = 156;
    /** Copied from Android. */
    KEYCODE_NUMPAD_ADD      = 157;
    /** Copied from Android. */
    KEYCODE_NUMPAD_DOT      = 158;
    /** Copied from Android. */
    KEYCODE_NUMPAD_COMMA    = 159;
    /** Copied from Android. */
    KEYCODE_NUMPAD_ENTER    = 160;
    /** Copied from Android. */
    KEYCODE_NUMPAD_EQUALS   = 161;
    /** Copied from Android. */
    KEYCODE_NUMPAD_LEFT_PAREN = 162;
    /** Copied from Android. */
    KEYCODE_NUMPAD_RIGHT_PAREN = 163;
    /** Copied from Android. */
    KEYCODE_VOLUME_MUTE     = 164;
    /** Copied from Android. */
    KEYCODE_INFO            = 165;
    /** Copied from Android. */
    KEYCODE_CHANNEL_UP      = 166;
    /** Copied from Android. */
    KEYCODE_CHANNEL_DOWN    = 167;
    /** Copied from Android. */
    KEYCODE_ZOOM_IN         = 168;
    /** Copied from Android. */
    KEYCODE_ZOOM_OUT        = 169;
    /** Copied from Android. */
    KEYCODE_TV              = 170;
    /** Copied from Android. */
    KEYCODE_WINDOW          = 171;
    /** Copied from Android. */
    KEYCODE_GUIDE           = 172;
    /** Copied from Android. */
    KEYCODE_DVR             = 173;
    /** Copied from Android. */
    KEYCODE_BOOKMARK        = 174;
    /** Copied from Android. */
    KEYCODE_CAPTIONS        = 175;
    /** Copied from Android. */
    KEYCODE_SETTINGS        = 176;
    /** Copied from Android. */
    KEYCODE_TV_POWER        = 177;
    /** Copied from Android. */
    KEYCODE_TV_INPUT        = 178;
    /** Copied from Android. */
    KEYCODE_STB_POWER       = 179;
    /** Copied from Android. */
    KEYCODE_STB_INPUT       = 180;
    /** Copied from Android. */
    KEYCODE_AVR_POWER       = 181;
    /** Copied from Android. */
    KEYCODE_AVR_INPUT       = 182;
    /** Copied from Android. */
    KEYCODE_PROG_RED        = 183;
    /** Copied from Android. */
    KEYCODE_PROG_GREEN      = 184;
    /** Copied from Android. */
    KEYCODE_PROG_YELLOW     = 185;
    /** Copied from Android. */
    KEYCODE_PROG_BLUE       = 186;
    /** Copied from Android. */
    KEYCODE_APP_SWITCH      = 187;
    /** Copied from Android. */
    KEYCODE_BUTTON_1        = 188;
    /** Copied from Android. */
    KEYCODE_BUTTON_2        = 189;
    /** Copied from Android. */
    KEYCODE_BUTTON_3        = 190;
    /** Copied from Android. */
    KEYCODE_BUTTON_4        = 191;
    /** Copied from Android. */
    KEYCODE_BUTTON_5        = 192;
    /** Copied from Android. */
    KEYCODE_BUTTON_6        = 193;
    /** Copied from Android. */
    KEYCODE_BUTTON_7        = 194;
    /** Copied from Android. */
    KEYCODE_BUTTON_8        = 195;
    /** Copied from Android. */
    KEYCODE_BUTTON_9        = 196;
    /** Copied from Android. */
    KEYCODE_BUTTON_10       = 197;
    /** Copied from Android. */
    KEYCODE_BUTTON_11       = 198;
    /** Copied from Android. */
    KEYCODE_BUTTON_12       = 199;
    /** Copied from Android. */
    KEYCODE_BUTTON_13       = 200;
    /** Copied from Android. */
    KEYCODE_BUTTON_14       = 201;
    /** Copied from Android. */
    KEYCODE_BUTTON_15       = 202;
    /** Copied from Android. */
    KEYCODE_BUTTON_16       = 203;
    /** Copied from Android. */
    KEYCODE_LANGUAGE_SWITCH = 204;
    /** Copied from Android. */
    KEYCODE_MANNER_MODE     = 205;
    /** Copied from Android. */
    KEYCODE_3D_MODE         = 206;
    /** Copied from Android. */
    KEYCODE_CONTACTS        = 207;
    /** Copied from Android. */
    KEYCODE_CALENDAR        = 208;
    /** Copied from Android. */
    KEYCODE_MUSIC           = 209;
    /** Copied from Android. */
    KEYCODE_CALCULATOR      = 210;
    /** Copied from Android. */
    KEYCODE_ZENKAKU_HANKAKU = 211;
    /** Copied from Android. */
    KEYCODE_EISU            = 212;
    /** Copied from Android. */
    KEYCODE_MUHENKAN        = 213;
    /** Copied from Android. */
    KEYCODE_HENKAN          = 214;
    /** Copied from Android. */
    KEYCODE_KATAKANA_HIRAGANA = 215;
    /** Copied from Android. */
    KEYCODE_YEN             = 216;
    /** Copied from Android. */
    KEYCODE_RO              = 217;
    /** Copied from Android. */
    KEYCODE_KANA            = 218;
    /** Copied from Android. */
    KEYCODE_ASSIST          = 219;
    /** Copied from Android. */
    KEYCODE_BRIGHTNESS_DOWN = 220;
    /** Copied from Android. */
    KEYCODE_BRIGHTNESS_UP   = 221;
    /** Copied from Android. */
    KEYCODE_MEDIA_AUDIO_TRACK = 222;
    /** Copied from Android. */
    KEYCODE_SLEEP           = 223;
    /** Copied from Android. */
    KEYCODE_WAKEUP          = 224;
    /** Copied from Android. */
    KEYCODE_PAIRING         = 225;
    /** Copied from Android. */
    KEYCODE_MEDIA_TOP_MENU  = 226;
    /** Copied from Android. */
    KEYCODE_11              = 227;
    /** Copied from Android. */
    KEYCODE_12              = 228;
    /** Copied from Android. */
    KEYCODE_LAST_CHANNEL    = 229;
    /** Copied from Android. */
    KEYCODE_TV_DATA_SERVICE = 230;
    /** Copied from Android. */
    KEYCODE_VOICE_ASSIST    = 231;
    /** Copied from Android. */
    KEYCODE_TV_RADIO_SERVICE = 232;
    /** Copied from Android. */
    KEYCODE_TV_TELETEXT     = 233;
    /** Copied from Android. */
    KEYCODE_TV_NUMBER_ENTRY = 234;
    /** Copied from Android. */
    KEYCODE_TV_TERRESTRIAL_ANALOG = 235;
    /** Copied from Android. */
    KEYCODE_TV_TERRESTRIAL_DIGITAL = 236;
    /** Copied from Android. */
    KEYCODE_TV_SATELLITE    = 237;
    /** Copied from Android. */
    KEYCODE_TV_SATELLITE_BS = 238;
    /** Copied from Android. */
    KEYCODE_TV_SATELLITE_CS = 239;
    /** Copied from Android. */
    KEYCODE_TV_SATELLITE_SERVICE = 240;
    /** Copied from Android. */
    KEYCODE_TV_NETWORK      = 241;
    /** Copied from Android. */
    KEYCODE_TV_ANTENNA_CABLE = 242;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_HDMI_1 = 243;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_HDMI_2 = 244;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_HDMI_3 = 245;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_HDMI_4 = 246;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_COMPOSITE_1 = 247;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_COMPOSITE_2 = 248;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_COMPONENT_1 = 249;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_COMPONENT_2 = 250;
    /** Copied from Android. */
    KEYCODE_TV_INPUT_VGA_1  = 251;
    /** Copied from Android. */
    KEYCODE_TV_AUDIO_DESCRIPTION = 252;
    /** Copied from Android. */
    KEYCODE_TV_AUDIO_DESCRIPTION_MIX_UP = 253;
    /** Copied from Android. */
    KEYCODE_TV_AUDIO_DESCRIPTION_MIX_DOWN = 254;
    /** Copied from Android. */
    KEYCODE_TV_ZOOM_MODE    = 255;
    /** Copied from Android. */
    KEYCODE_TV_CONTENTS_MENU = 256;
    /** Copied from Android. */
    KEYCODE_TV_MEDIA_CONTEXT_MENU = 257;
    /** Copied from Android. */
    KEYCODE_TV_TIMER_PROGRAMMING = 258;
    /** Copied from Android. */
    KEYCODE_HELP            = 259;
    /** Copied from Android. */
    KEYCODE_NAVIGATE_PREVIOUS = 260;
    /** Copied from Android. */
    KEYCODE_NAVIGATE_NEXT   = 261;
    /** Copied from Android. */
    KEYCODE_NAVIGATE_IN     = 262;
    /** Copied from Android. */
    KEYCODE_NAVIGATE_OUT    = 263;
    /** Copied from Android. */
    KEYCODE_DPAD_UP_LEFT    = 268;
    /** Copied from Android. */
    KEYCODE_DPAD_DOWN_LEFT  = 269;
    /** Copied from Android. */
    KEYCODE_DPAD_UP_RIGHT   = 270;
    /** Copied from Android. */
    KEYCODE_DPAD_DOWN_RIGHT = 271;
    /** Do not use. */
    KEYCODE_SENTINEL = 65535;
    /** MD ID for the rotary controller. */
    KEYCODE_ROTARY_CONTROLLER = 65536;
    /** Media hard key on the head unit. */
    KEYCODE_MEDIA = 65537;
    /** Navigation hard key on the head unit. */
    KEYCODE_NAVIGATION = 65538;
    /** Radio hard key on the head unit. */
    KEYCODE_RADIO = 65539;
    /** Telephone hard button on the head unit. */
    KEYCODE_TEL = 65540;
    /** MD ID for the primary button (left trackpad button). */
    KEYCODE_PRIMARY_BUTTON = 65541;
    /** MD ID for the secondary button. */
    KEYCODE_SECONDARY_BUTTON = 65542;
    /** MD ID for the tertiary button. */
    KEYCODE_TERTIARY_BUTTON = 65543;
}


/**
 * @section Appendix III: Global Constants
 * <p>This section lists constants for the ALP protocol.</p>
 */
/** <p>The ALP protocol uses the following constants:</p> */
enum GalConstants {
    /** Well-known port used for Wifi connections. */
    WIFI_PORT = 30515;
    /** ALP major version. */
    PROTOCOL_MAJOR_VERSION = 1;
    /** ALP minor version. */
    PROTOCOL_MINOR_VERSION = 11;
}
