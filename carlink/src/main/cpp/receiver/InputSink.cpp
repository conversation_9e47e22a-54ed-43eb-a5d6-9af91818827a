// Copyright 2014 Google Inc. All Rights Reserved.

#include "util/common.h"
#include "InputSink.h"


void InputSink::onChannelOpened(uint8_t channelId, uint32_t extraMessage) {
    ProtocolEndpointBase::onChannelOpened(channelId, extraMessage);
    mCallbacks->onChannelOpened();
    sendKeyBindingRequest();
}

bool InputSink::discoverService(const Service &srv) {
    bool ret = false;
    if (srv.has_input_source_service()) {
        const InputSourceService *msrv = &srv.input_source_service();
        if (msrv->keycodes_supported_size()) {
            for (int i = 0; i < msrv->keycodes_supported_size(); i++) {
                if (mKeycodes.find(msrv->keycodes_supported(i)) != mKeycodes.end()) {
                    mBoundKeycodes.insert(msrv->keycodes_supported(i));
                }
            }
        }
        if (msrv->touchscreen_size()) {
            mHasTouchScreen = true;
            mTouchScreenWidth = msrv->touchscreen(0).width();
            mTouchScreenHeight = msrv->touchscreen(0).height();
            if (msrv->touchscreen(0).has_type()) {
                mTouchScreenType = msrv->touchscreen(0).type();
            }
        }
        if (msrv->touchpad_size()) {
            mHasTouchPad = true;
            mTouchPadWidth = msrv->touchpad(0).width();
            mTouchPadHeight = msrv->touchpad(0).height();
            if (msrv->touchpad(0).has_ui_navigation()) {
                mTouchPadUiNavigation = msrv->touchpad(0).ui_navigation();
            }
            if (msrv->touchpad(0).has_physical_width()) {
                mTouchPadPhysWidth = msrv->touchpad(0).physical_width();
            }
            if (msrv->touchpad(0).has_physical_height()) {
                mTouchPadPhysHeight = msrv->touchpad(0).physical_height();
            }
        }
        ret = mCallbacks->discoverInputService(mHasTouchScreen, mHasTouchPad,
                                               !mBoundKeycodes.empty());
    }
    return ret;

}

int InputSink::routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) {
    int ret = STATUS_UNEXPECTED_MESSAGE;
    uint8_t *ptr = (uint8_t *) msg->raw() + sizeof(uint16_t);
    size_t len = msg->size() - sizeof(uint16_t);
    switch (type) {
        case INPUT_MESSAGE_INPUT_REPORT: {
            InputReport report;
            if (PARSE_PROTO(report, ptr, len)) {
                handleInputReport(report);
                return STATUS_SUCCESS;
            }
            break;
        }
        case INPUT_MESSAGE_KEY_BINDING_RESPONSE: {
            KeyBindingResponse res;
            if (PARSE_PROTO(res, ptr, len)) {
                handleKeyBindingResponse(res);
                return STATUS_SUCCESS;
            }
            break;
        }
        default:
            break;
    }
    return ret;
}


void InputSink::sendKeyBindingRequest() {
    if (!mBoundKeycodes.empty()) {
        KeyBindingRequest req;
        for (std::__ndk1::__tree_const_iterator<int, std::__ndk1::__tree_node<int, void *> *, long>::value_type mBoundKeycode : mBoundKeycodes) {
            req.add_keycodes(mBoundKeycode);
        }
        IoBuffer buf;
        mRouter->marshallProto(INPUT_MESSAGE_KEY_BINDING_REQUEST, req, &buf);
        queueOutgoing(buf.raw(), buf.size());
    }
}

void InputSink::handleKeyBindingResponse(const KeyBindingResponse &res) {
    if (res.status() != STATUS_SUCCESS) {
        mBoundKeycodes.clear();
    }
}


void InputSink::handleInputReport(const InputReport &report) {
    uint64_t timestamp = report.timestamp();
    if (mSupportTouchScreen && report.has_touch_event()) {
        receiveTouch(timestamp, &report.touch_event());
    }
    if (mSupportTouchPad && report.has_touchpad_event()) {
        receiveTouchPad(timestamp, &report.touchpad_event());
    }
    if (!mBoundKeycodes.empty()) {
        if (report.has_key_event()) {
            receiveKey(timestamp, &report.key_event());
        }
        if (report.has_absolute_event()) {
            receiveAbsolute(timestamp, &report.absolute_event());
        }
        if (report.has_relative_event()) {
            receiveRelative(timestamp, &report.relative_event());
        }
    }
}

void InputSink::receiveTouch(uint64_t timestamp, const TouchEvent *event) {
    uint32_t pointer_number = event->pointer_data_size();
    auto *x = new uint32_t[pointer_number];
    auto *y = new uint32_t[pointer_number];
    auto *ids = new uint32_t[pointer_number];
    for (int i = 0; i < event->pointer_data_size(); i++) {
        x[i] = event->pointer_data(i).x();
        y[i] = event->pointer_data(i).y();
        ids[i] = event->pointer_data(i).pointer_id();
    }
    uint32_t action_index = event->action_index();
    PointerAction action = event->action();
    mCallbacks->onTouchEvent(timestamp, pointer_number, ids, x, y, (int) action, action_index);
}

void InputSink::receiveTouchPad(uint64_t timestamp, const TouchEvent *event) {
    uint32_t pointer_number = event->pointer_data_size();
    auto *x = new uint32_t[pointer_number];
    auto *y = new uint32_t[pointer_number];
    auto *ids = new uint32_t[pointer_number];
    for (int i = 0; i < event->pointer_data_size(); i++) {
        x[i] = event->pointer_data(i).x();
        y[i] = event->pointer_data(i).y();
        ids[i] = event->pointer_data(i).pointer_id();
    }
    uint32_t action_index = event->action_index();
    PointerAction action = event->action();
    mCallbacks->onTouchPadEvent(timestamp, pointer_number, ids, x, y, (int) action, action_index);

}

bool InputSink::isBound(int32_t keycode) {
    return (mBoundKeycodes.find(keycode) != mBoundKeycodes.end());
}

void InputSink::receiveKey(uint64_t timestamp, const KeyEvent *event) {
    int32_t keycode;
    uint32_t metastate;
    bool down;
    bool longpress;
    int key_number = event->keys_size();
    for (int i = 0; i < key_number; i++) {
        keycode = event->keys(i).keycode();
        down = event->keys(i).down();
        metastate = event->keys(i).metastate();
        if (event->keys(i).has_longpress()) {
            longpress = event->keys(i).longpress();
        } else {
            longpress = false;
        }
        if (isBound(keycode)) {
            mCallbacks->onKeyEvent(timestamp, keycode, down, longpress, metastate);
        }
    }
}

void InputSink::receiveAbsolute(uint64_t timestamp, const AbsoluteEvent *event) {
    int32_t keycode;
    int32_t value;
    uint32_t number = event->data_size();
    for (int i = 0; i < number; i++) {
        keycode = event->data(i).keycode();
        value = event->data(i).value();
        if (isBound(keycode)) {
            mCallbacks->onAbsoluteEvent(timestamp, keycode, value);
        }
    }
}

void InputSink::receiveRelative(uint64_t timestamp, const RelativeEvent *event) {
    int32_t keycode;
    int32_t delta;
    uint32_t number = event->data_size();
    for (int i = 0; i < number; i++) {
        keycode = event->data(i).keycode();
        delta = event->data(i).delta();
        if (isBound(keycode)) {
            mCallbacks->onRelativeEvent(timestamp, keycode, delta);
        }
    }
}

