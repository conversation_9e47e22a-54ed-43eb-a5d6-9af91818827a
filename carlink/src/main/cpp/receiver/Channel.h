// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_CHANNEL_H
#define AUTOLINK_PROTOCOL_CHANNEL_H

#include "util/common.h"

/**
 * @internal
 * Represents a connection between the phone and a particular service endpoint.
 */
class Channel {
public:
    /* Shift the priority to the 0 - 255 range internally. */
    Channel(uint8_t channelId, int8_t prio) : mId(channelId),
                                              mPriority(0xff & (prio + 127)),
                                              mVtime(0) {}

    void enqueueIncoming(const shared_ptr<Frame> &frame);

    shared_ptr<Frame> dequeueIncoming();

    void enqueueOutgoing(const shared_ptr<Frame> &frame);

    shared_ptr<Frame> dequeueOutgoing();

    shared_ptr<Frame> peekLastIncoming();

    bool hasIncoming();

    bool hasOutgoing();

    uint8_t id() const { return mId; }

private:
    uint8_t mId;
    uint8_t mPriority;
    uint64_t mVtime;
    deque<shared_ptr<Frame> > mOutgoing;
    deque<shared_ptr<Frame> > mIncoming;
    Mutex mIncomingMutex;
    Mutex mOutgoingMutex;
};

#endif // AUTOLINK_PROTOCOL_CHANNEL_H

