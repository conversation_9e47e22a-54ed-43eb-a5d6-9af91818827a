// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROJECTION_PROTOCOL_VENDOR_EXTENSION_H
#define AUTOLINK_PROJECTION_PROTOCOL_VENDOR_EXTENSION_H

#include "util/common.h"
#include "IVendorExtensionCallbacks.h"
#include "ProtocolEndpointBase.h"

/**
 * A vendor extension endpoint is one that utilizes GAL only as a transport. Vendor extension
 * channels are completely opaque to GAL and the bits are not interpreted, they are simply sent
 * over the wire as is. This allows for OEMs to write proprietary services that can run over GAL
 * alongside with the regular GAL service endpoints while enjoying all the benefits that GAL
 * provides.
 *
 * <br>
 * <pre>
 *      galReceiver->init();
 *      ... Initialization code ...
 *      VendorExtension* endpoint = new VendorExtension(serviceId, galReceiver->messageRouter());
 *      endpoint->registerCallbacks(callbacks); // Subclassed from IVendorExtensionCallbacks.
 *      galReceiver->registerService(endpoint);
 *      ... Other Initialization code ...
 *      galReceiver->start();
 * </pre>
 */
class VendorExtension : public ProtocolEndpointBase {
public:
    VendorExtension(uint8_t id, MessageRouter *router) :
            ProtocolEndpointBase(id, router, true) {}

    void registerCallbacks(const shared_ptr<IVendorExtensionCallbacks> &callbacks) {
        mCallbacks = callbacks;
    }

    /**
     * Call this function to send data along the channel.
     * @param data The data to be sent.
     * @param len The length of the data to be sent.
     */
    void sendData(void *data, size_t len);

    bool discoverService(const Service &srv);

    virtual void onChannelOpened(uint8_t channelId, uint32_t extraMessage);

    int handleRawMessage(uint8_t channelId, const shared_ptr<IoBuffer> &msg);

private:
    shared_ptr<IVendorExtensionCallbacks> mCallbacks;
};

#endif // AUTOLINK_PROJECTION_PROTOCOL_VENDOR_EXTENSION_H
