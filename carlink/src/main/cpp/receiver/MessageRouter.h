// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_MESSAGE_ROUTER_H
#define AUTOLINK_PROTOCOL_MESSAGE_ROUTER_H

#include "util/common.h"
#include "ChannelManager.h"

class ProtocolEndpointBase;

/**
 * @internal
 * The MessageRouter is exactly what it sounds like, it maintains the channel:service_id
 * mappings and figures out which ProtocolEndpoint should get the message that was just
 * received.
 * It is worth noting that the word ProtocolEndpoint is used to represent a 'Service' in
 * the protocol specification. There are two reasons for this - 1) The word service means
 * something specific in a lot of contexts, so we avoid it. 2) The actual service names
 * are used up by the protocol buffer objects, so it is just simpler this way.
 */
class MessageRouter {
public:
    bool init(ChannelManager *channelManager);

    void shutdown();

    int queueOutgoing(uint8_t channelId, void *buf, size_t len);

    int queueOutgoingUnencrypted(uint8_t channelId, void *buf, size_t len);

    int routeMessage(uint8_t channelId, const shared_ptr<IoBuffer> &msg);

    int routeChannelControlMsg(const shared_ptr<Frame> &frame, void *message, size_t len);

    bool registerService(ProtocolEndpointBase *service);

    void marshallProto(uint16_t type, const google::protobuf::MessageLite &proto, IoBuffer *out);

    void setupMapping(uint8_t serviceId, uint8_t channelId);

    void unrecoverableError(MessageStatus err);

#ifdef SUPPPRT_SSL
    inline void setSslWrapper(SslWrapper* sslWrapper) {
        mChannelManager->setSslWrapper(sslWrapper);
    }
#endif

    bool closeChannel(uint8_t channelId);

    bool forceCloseChannel(uint8_t channelId);

    bool isChannelClosed(uint8_t channelId);

    void discoverServices(ServiceDiscoveryResponse &sdr);

private:

    int
    handleChannelOpenResponse(uint8_t channelId, MessageStatus status, uint32_t extraMessage = 0);

    int sendChannelOpenRequest(uint8_t serviceId, uint8_t channelId, uint8_t priority);

    int handleChannelCloseNotif(uint8_t channelId, const ChannelCloseNotification &notification);

    int sendChannelCloseNotifi(uint8_t channelId);

    int sendUnexpectedMessage(uint8_t channelId);

    uint16_t extractType(uint8_t *message);

    uint8_t mChannelServiceIdMap[MAX_CHANNELS];
    ProtocolEndpointBase *mServiceMap[MAX_SERVICES];
    ChannelManager *mChannelManager;
};

#endif //AUTOLINK_PROTOCOL_MESSAGE_ROUTER_H
