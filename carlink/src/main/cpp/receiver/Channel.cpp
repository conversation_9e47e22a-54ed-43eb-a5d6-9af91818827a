// Copyright 2014 Google Inc. All Rights Reserved.

#include "util/common.h"
#include "Channel.h"

void Channel::enqueueIncoming(const shared_ptr<Frame> &frame) {
    Autolock l(&mIncomingMutex);
    mIncoming.push_back(frame);
}

shared_ptr<Frame> Channel::peekLastIncoming() {
    Autolock l(&mIncomingMutex);
    return mIncoming.back();
}

bool Channel::hasIncoming() {
    Autolock l(&mIncomingMutex);
    return !mIncoming.empty();
}

shared_ptr<Frame> Channel::dequeueIncoming() {
    Autolock l(&mIncomingMutex);
    shared_ptr<Frame> frame(mIncoming.front());
    mIncoming.pop_front();
    return frame;
}

bool Channel::hasOutgoing() {
    Autolock l(&mOutgoingMutex);
    return !mOutgoing.empty();
}

void Channel::enqueueOutgoing(const shared_ptr<Frame> &frame) {
    Autolock l(&mOutgoingMutex);
    mOutgoing.push_back(frame);
}

shared_ptr<Frame> Channel::dequeueOutgoing() {
    Autolock l(&mOutgoingMutex);
    shared_ptr<Frame> frame(mOutgoing.front());
    mOutgoing.pop_front();
    return frame;
}
