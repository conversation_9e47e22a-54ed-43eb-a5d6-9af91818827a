// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_CONTROLLER_H
#define AUTOLINK_PROTOCOL_CONTROLLER_H

#include "util/common.h"
#include "IControllerCallbacks.h"
#include "ProtocolEndpointBase.h"

/**
 * @internal
 * This class manages the internal GAL state machine, authentication and common messaging. You
 * should not need to directly interact with this class.
 */
class Controller : public ProtocolEndpointBase {
public:
    Controller(MessageRouter *router) :
            ProtocolEndpointBase(CONTROLLER_SERVICE_ID, router, false),
            mHealthy(false),
            mDriverPosition(0),
            mSessionConfiguration(0) {}

    int routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg);

    void start();

    void shutdown() {
#ifdef SUPPORT_SSL
        mSslWrapper.shutdown();
#endif
    }

    void setManufacturer(string &manufacturer) {
        mManufacturer = manufacturer;
    }

    void setModel(string &model) {
        mModel = model;
    }

    void setVersion(string &version) {
        mVersion = version;
    }

    void setPhoneName(string &name) {
        mPhoneName = name;
    }

    void setScreenResolution(uint32_t width, uint32_t height) {
        mScreenWidth = width;
        mScreenHeight = height;
    }

    /**
     * Ping the other side to check to see if it is alive.
     * @param timestamp The local timestamp. This is repeated by the other side
     *        in the response so it can be used to do interesting things like
     *        computing round trip delays.
     * @param bugReport Should the other side save away a bug report locally. The
     *        other side may choose to ignore this request if it is deemed to be
     *        too high frequency.
     */
    void sendPingRequest(int64_t timestamp, bool bugReport);

    /**
     * Send ByeByeRequest to the other side. After sending this, ByeByeResponse will be coming,
     * and that is the end of the current connection.
     * @param reason reason for finishing.
     */
    void sendByeByeRequest(int32_t reason);

    /**
     * Send ByeByeResponse as a response to ByeByeRequest. This should be sent only after getting
     * ByeByeRequest, which will be notified via byeByeRequestCallback(..).
     */
    void sendByeByeResponse();

    void sendExitResponse();

    void sendNavFocusRequest(int32_t type);

    void registerCallbacks(const shared_ptr<IControllerCallbacks> &controllerCallbacks) {
        mControllerCallbacks = controllerCallbacks;
    }

    void unrecoverableError(MessageStatus err) {
        mControllerCallbacks->unrecoverableErrorCallback(err);
    }

    void sendForceLandscapeResponse(bool force);

    void sendScreenOrientationNotifi(int orientation, int rotation);

    void sendUpdateVehicleIdNotifi(string id);

    void sendRunningStateNotifi(int state);

    void sendAutoRotationNotifi(bool isAutoed);

    void sendReadRequest(int address, int length);

    void sendWriteRequest(string data, int address, int length);

    void sendScreenResolutionNotification(int width, int height, bool isRequired);

    void initChipList();

    const unsigned long *getReadAddresses(const std::string &chipName);

    bool calculateSafety();

    int checkName(const std::string &chipName);

    int checkProduct(int moduleId, int feature, int productId);

    void checkProductResult(void *buf, int32_t size);

    void sendTimeDateNotification(int year, int month, int day, int hour, int minute, int second,
                                  int nanosecond, int week, int dayOfWeek);

private:
    int handleVersionRequest(void *msg, size_t len);

    int handleServiceDiscoveryReponse(ServiceDiscoveryResponse &response);

    int handlePingRequest(const PingRequest &req);

    int handlePingResponse(const PingResponse &resp);

    int handleByeByteRequest(ByeByeReason req);

    int handleByeByeResponse();

    int handleExitRequest();

    int handleEncapsulatedSsl(void *msg, size_t len);

    int handleAuthComplete();

    int handleReadResponse(const ReadResponse &resp);

    int handleWriteResponse(const WriteResponse &resp);

    //int handleNavFocusNotification(const NavFocusNotification &nfn);
    //int handleAudioFocusNotification(const AudioFocusNotification &afn);
    int handleForceLandscapeRequest(bool force);

    int handleScreenOrientationInqure();

    int handleScreenResolutionInquire();

    int handleRunningStateInquire();

    int handleAutoRotationRequest(bool autoed);

    int handleTimeDateInquire();

    void sendVersionResponse(uint16_t status);

    void sendAuthResponse(int status);

    void sendServiceDiscoveryRequest();

    void sendPingResponse(int64_t timestamp);

    int handleWLTouchNotification(const WlTouchEvent &req);

    bool mHealthy;
/*    string mMake;
    string mModel;
    string mYear;
    string mId;
    string mHuMake;
    string mHuModel;
    string mHuSwBuild;
    string mHuSwVersion; */
    int mDriverPosition;
    int mSessionConfiguration;
    shared_ptr<IControllerCallbacks> mControllerCallbacks;
    map<string, const unsigned long *> *chipCodes;
#ifdef SUPPORT_SSL
    SslWrapper mSslWrapper;
#endif
    string mManufacturer;
    string mModel;
    string mVersion;
    string mPhoneName;
    uint32_t mScreenWidth;
    uint32_t mScreenHeight;
};

#endif //AUTOLINK_PROTOCOL_CONTROLLER_H
