//
// Created by system5 on 2017/12/28.
//

#ifndef AUTOLINK_NOUI_IUPGRADECALLBACKS_H
#define AUTOLINK_NOUI_IUPGRADECALLBACKS_H

#include "util/common.h"

class IUpgradeCallbacks {
public:
    virtual ~IUpgradeCallbacks() {};

    virtual void onChannelOpened(string &ictype, string &buildtype) = 0;

    virtual void compareServerVersion(vector<FileInfo> list) = 0;

    virtual void downladServerFile(vector<FileInfo> list) = 0;

    virtual void getData(string &name, int32_t curX, int32_t curY, int32_t curZ, int32_t offset,
                         int32_t len) = 0;

    virtual void
    checksum(string &name, int32_t curX, int32_t curY, int32_t curZ, int32_t checksum) = 0;

    virtual void transportStart() = 0;

    virtual void transportComplete(bool isSuccess) = 0;
};

#endif //AUTOLINK_NOUI_IUPGRADECALLBACKS_H
