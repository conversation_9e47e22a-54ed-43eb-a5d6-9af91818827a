// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_CHANNEL_MANAGER_H
#define AUTOLINK_PROTOCOL_CHANNEL_MANAGER_H

#include "util/common.h"
#include "Channel.h"

class MessageRouter;

/**
 * @internal
 * This class is at the lowest level which first handles the raw data that is coming
 * off the transport. It is responsible for encoding and decoding of frames and fragmentation
 * and reassembly of frames into messages. When messages are reassembled, they are passed
 * up to the MessageRouter which then hands it to the correct Protocol Endpoint. This class
 * also enlists the help of the Wfq class to decide which channel gets to transmit.
 */
class ChannelManager {
public:
    bool init(MessageRouter *messageRouter);

    void prepareShutdown();

    void shutdown();

    /**
     * @internal
     * Get the maximum payload size per fragment. Any message above this size will be fragmented
     * into two or more fragments. If for some reason the transport cannot handle fragments of
     * this size efficiently, it can be reduced. It cannot however be increased above USHORT_MAX.
     * @return The maximum payload per fragment in bytes.
     */
    int getMaxFragmentPayloadSize() { return 256 * 63; /* Minimizes ssl padding. */ }

    int allocChannel(uint8_t serviceId, int8_t priority);

    int freeChannel(uint8_t channelId);

    int queueOutgoing(int channelId, bool control, void *message, size_t len, bool encrypted);

    int queueOutgoing(int channelId, bool control, void *message, size_t len);

    int queueOutgoingUnencrypted(int channelId, bool control, void *message, size_t len);

    int queueIncoming(const shared_ptr<IoBuffer> &buf);

    bool getEncodedFrame(IoBuffer *encoded);

    size_t getAdditionalBytesToRead(const uint8_t buf[FRAME_HEADER_MIN_LENGTH]);

#ifdef SUPPORT_SSL
    inline void setSslWrapper(SslWrapper* sslWrapper) {
        mSslWrapper = sslWrapper;
    }
#endif

private:
    void sendFramingError();

    void messageToFrames(int channelId, bool control, void *message,
                         size_t len, const shared_ptr<Channel> &channel, bool encrypted);

    shared_ptr<IoBuffer> framesToMessage(const shared_ptr<Channel> &channel);

    void encodeFrame(const shared_ptr<Frame> &frame, IoBuffer *encoded);

    int decodeFrame(const shared_ptr<Frame> &frame, const shared_ptr<IoBuffer> &buf);

    int handleChannelControl(const shared_ptr<Frame> &frame, const shared_ptr<IoBuffer> &buf);

    int checkFragment(const shared_ptr<Frame> &frame, shared_ptr<Channel> *ch,
                      bool *valid, bool *complete);

    bool extractEncodedFrame(IoBuffer *encoded);

    shared_ptr<Channel> mChannels[MAX_CHANNELS];
    MessageRouter *mMessageRouter;
    bool mHealthy;
    Mutex mMutex; // Break this into multiple locks if necessary.
    Semaphore mDataAvailable;
#ifdef SUPPORT_SSL
    SslWrapper* mSslWrapper;
#endif
    int mLastChannel;
};


#endif //AUTOLINK_PROTOCOL_CHANNEL_MANAGER_H
