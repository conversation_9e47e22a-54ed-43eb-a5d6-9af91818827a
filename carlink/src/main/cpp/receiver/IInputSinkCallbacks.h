// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_INPUT_SINK_CALLBACKS_H
#define AUTOLINK_PROTOCOL_INPUT_SINK_CALLBACKS_H

#include "util/common.h"

/** 
* This class represents an interface that a sink implementation must subclass if they
* wish to recieve input events from the HU. 
*/
class IInputSinkCallbacks {
public:
    virtual ~IInputSinkCallbacks() {}

    virtual int onChannelOpened() = 0;

    virtual bool discoverInputService(bool hasTouchscreen, bool hasTouchpad, bool hasKey) = 0;

    virtual void onTouchEvent(uint64_t timestamp, int32_t numPointers, const uint32_t *pointerIds,
                              const uint32_t *x, const uint32_t *y, int action, int actionIndex) {}

    virtual void
    onTouchPadEvent(uint64_t timestamp, int32_t numPointers, const uint32_t *pointerIds,
                    const uint32_t *x, const uint32_t *y, int action, int actionIndex) {}

    virtual void onKeyEvent(uint64_t timestamp, int32_t keycode, bool isDown,
                            bool longPress, uint32_t metaState) {}

    virtual void onAbsoluteEvent(uint64_t timestamp, int32_t keycode, int32_t value) {}

    virtual void onRelativeEvent(uint64_t timestamp, int32_t keycode, int32_t delta) {}
};


#endif  //AUTOLINK_PROTOCOL_INPUT_SINK_CALLBACKS_H

