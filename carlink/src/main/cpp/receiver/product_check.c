#include <stdio.h>
#include <string.h>

#include "product_check.h"

#define TAG "PRODUCT"

#define  diag_printf(fmt, arg...) printf(fmt, ##arg)

//#define DEBUG_ENABLE
#ifdef DEBUG_ENABLE
#define DEBUG(fmt,arg...) diag_printf(TAG"[D]"fmt,##arg)
#else
#define DEBUG(a...) ((void)0)

#endif


/**
* array size
**/

#define ARRAY_SIZE(a) sizeof(a)/sizeof(a[0])


/**
* hw feature mask bit ref define
**/
typedef enum {
    FEATURE_BIT_CP_CERT = 0x0001 << 0,
    FEATURE_BIT_AA_CERT = 0x0001 << 1,
    FEATURE_BIT_WIRELESS_CP_CERT = 0x0001 << 2,
    FEATURE_BIT_WIRELESS_AA_CERT = 0x0001 << 3,
    FEATURE_BIT_CP_NON_CERT = 0x0001 << 4,
    FEATURE_BIT_AA_NON_CERT = 0x0001 << 5,
    FEATURE_BIT_WIRELESS_CP_NON_CERT = 0x0001 << 6,
    FEATURE_BIT_WIRELESS_AA_NON_CERT = 0x0001 << 7,
    FEATURE_BIT_AUTOLINK = 0x0001 << 8,
    FEATURE_BIT_VAUTOLINK = 0x0001 << 9,
    FEATURE_BIT_AIRPLAY = 0x0001 << 10,
    FEATURE_BIT_WFD = 0x0001 << 11,
    FEATURE_BIT_CARLIFE = 0x0001 << 12,

    FEATURE_BIT_UNKNOWN = 0xffffffff,

} feature_mask_bit_t;

static const struct {
    int module_id;
    int feature_bit;

} g_feature_bit_map[] = {
        {MODULE_PRODUCT_ID_AUTOLINK,  FEATURE_BIT_AUTOLINK},
        {MODULE_PRODUCT_ID_VAUTOLINK, FEATURE_BIT_VAUTOLINK},
        {MODULE_PRODUCT_ID_CARPLAY,   FEATURE_BIT_CP_NON_CERT},
        {MODULE_PRODUCT_ID_AIRPLAY,   FEATURE_BIT_AIRPLAY},
        {MODULE_PRODUCT_ID_WFD,       FEATURE_BIT_WFD},

        //module outside accessory , ex : carlife
        {MODULE_PRODUCT_ID_CARLIFE,   FEATURE_BIT_CARLIFE},

        //wifi driver
        {MODULE_PRODUCT_ID_WIFI_AP,   FEATURE_BIT_AIRPLAY},
        {MODULE_PRODUCT_ID_WIFI_P2P,  FEATURE_BIT_WFD},
        {MODULE_PRODUCT_ID_WIFI_STA,  FEATURE_BIT_UNKNOWN},

};
#define FEATURE_BIT_MAP_MAX ARRAY_SIZE(g_feature_bit_map)



/**
* product info ref define and value
**/
#define MODULE_ID_2_MASK(id) (0x00000001<<(id))

typedef enum {
    FEATURE_MASK_SAMPLE = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_SAMPLE),
    FEATURE_MASK_AUTOLINK = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_AUTOLINK),
    FEATURE_MASK_VAUTOLINK = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_VAUTOLINK),
    FEATURE_MASK_CARPLAY = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_CARPLAY),
    FEATURE_MASK_AIRPLAY = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_AIRPLAY),
    FEATURE_MASK_WFD = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_WFD),
    FEATURE_MASK_IAP2 = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_IAP2),
    FEATURE_MASK_ANDROIDAUTO = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_ANDROIDAUTO),

    FEATURE_MASK_CARLIFE = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_CARLIFE),


    FEATURE_MASK_WIFI_AP = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_WIFI_AP),
    FEATURE_MASK_WIFI_P2P = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_WIFI_P2P),
    FEATURE_MASK_WIFI_STA = MODULE_ID_2_MASK(MODULE_PRODUCT_ID_WIFI_STA),

    FEATURE_MASK_BASE = FEATURE_MASK_SAMPLE |
                        FEATURE_MASK_AUTOLINK |
                        FEATURE_MASK_VAUTOLINK,

    FEATURE_MASK_BASE_CL = FEATURE_MASK_SAMPLE |
                           FEATURE_MASK_AUTOLINK |
                           FEATURE_MASK_VAUTOLINK |
                           FEATURE_MASK_CARLIFE,


    FEATURE_MASK_BASE_CP = FEATURE_MASK_SAMPLE |
                           FEATURE_MASK_AUTOLINK |
                           FEATURE_MASK_VAUTOLINK |
                           FEATURE_MASK_CARPLAY | FEATURE_MASK_IAP2,

    FEATURE_MASK_BASE_CP_WIFI = FEATURE_MASK_SAMPLE |
                                FEATURE_MASK_AUTOLINK |
                                FEATURE_MASK_VAUTOLINK |
                                FEATURE_MASK_CARPLAY | FEATURE_MASK_IAP2 |
                                FEATURE_MASK_WIFI_AP | FEATURE_MASK_WIFI_P2P |
                                FEATURE_MASK_WIFI_STA |
                                FEATURE_MASK_WFD |
                                FEATURE_MASK_AIRPLAY,

    FEATURE_MASK_BASE_CP_WIFI_CL = FEATURE_MASK_SAMPLE |
                                   FEATURE_MASK_AUTOLINK |
                                   FEATURE_MASK_VAUTOLINK |
                                   FEATURE_MASK_CARPLAY | FEATURE_MASK_IAP2 |
                                   FEATURE_MASK_WIFI_AP | FEATURE_MASK_WIFI_P2P |
                                   FEATURE_MASK_WIFI_STA |
                                   FEATURE_MASK_WFD |
                                   FEATURE_MASK_AIRPLAY |
                                   FEATURE_MASK_CARLIFE,

    FEATURE_MASK_BASE_CRET = FEATURE_MASK_SAMPLE |
                             FEATURE_MASK_AUTOLINK |
                             FEATURE_MASK_CARPLAY | FEATURE_MASK_IAP2 |
                             FEATURE_MASK_WIFI_AP | FEATURE_MASK_WIFI_P2P | FEATURE_MASK_WIFI_STA |
                             FEATURE_MASK_WFD |
                             FEATURE_MASK_CARLIFE |
                             FEATURE_MASK_ANDROIDAUTO,


    FEATURE_MASK_ALL = 0xffffffff,

} feature_bit_t;

static const struct {

    const char *name;
    int product_id;
    feature_bit_t features;

} g_product_info_list[] = {
        {"8368-U-X",  0x9f, FEATURE_MASK_ALL},
        {"8368-U",    0x11, FEATURE_MASK_BASE_CRET},
        {"8368-UA",   0x92, FEATURE_MASK_BASE_CRET},
        {"8368-UC",   0x14, FEATURE_MASK_BASE_CRET},
        {"8368-UCL",  0x18, FEATURE_MASK_ALL},
        {"8368-UL",   0x13, FEATURE_MASK_ALL},
        {"DA-007",    0x15, FEATURE_MASK_ALL},
        {"8268K-X",   0xaf, FEATURE_MASK_ALL},
        {"8268K",     0x21, FEATURE_MASK_BASE},
        {"8268K-A",   0xa2, FEATURE_MASK_BASE},
        {"8268K-W",   0x24, FEATURE_MASK_BASE_CP_WIFI},
        {"8268K-AW",  0xa8, FEATURE_MASK_BASE_CP_WIFI},
        {"8268K-CW",  0x23, FEATURE_MASK_BASE_CP_WIFI_CL},
        {"8268K-S",   0x31, FEATURE_MASK_BASE},
        {"8268K-C",   0x32, FEATURE_MASK_BASE_CL},
        {"8268-test", 0x00, FEATURE_MASK_ALL},

};

#define PRODUCT_MAX ARRAY_SIZE(g_product_info_list)

typedef struct {
    int module_id;

    int product_id;
    int product_reg;

    int feature_value;
    int feature_reg;

    int key;

    chk_result_t result[0];
} chk_info_t;

static chk_info_t *g_chk_info = (void *) 0;


/*
* functions
*/


#if CONFIG_CHECK_RESULT_CRYPTO == 1

inline static void __encrypto(void *out, const void *in, int size, int key) {
    const char *in_ = (const char *) in;
    char *out_ = (char *) out;

    int i = 0;
    for (i = 0; i < size; i++) {
        out_[i] = in_[i] ^ key;
    }
}

inline static void __decrypto(void *out, const void *in, int size, int key) {
    const char *in_ = (const char *) in;
    char *out_ = (char *) out;

    int i = 0;
    for (i = 0; i < size; i++) {
        out_[i] = in_[i] ^ key;
    }
}

#endif


inline static void
set_result_value(chk_result_t *result, int module_id, fail_code_t fail_code, int value) {
    result->module_id = module_id;
    result->fail_code = fail_code;
    result->value = value;
}

inline static int __module_id_2_feature_bit(int module_id) {
    int bit = FEATURE_BIT_UNKNOWN;
    int i = 0;
    for (i = 0; i < FEATURE_BIT_MAP_MAX; i++) {
        if (g_feature_bit_map[i].module_id == module_id) {
            bit = g_feature_bit_map[i].feature_bit;
            break;
        }
    }

    return bit;
}

inline static int
__check(chk_result_t *result, int module_id, int feature_value, int feature_reg, int product_id,
        int product_reg) {
    int feature_check_ok = 0;

    // 1st : check feature value
    DEBUG("checking step 1 \n");
    if (feature_value != GET_PRODUCT_FEATURE_VALUE(feature_reg)) {
        set_result_value(result, module_id, FAIL_CODE_STAGE_1, feature_value);
        DEBUG("checking step 1 fail\n");
        goto check_fail;
    }
    DEBUG("checking step 1 success\n");

    //2nd : check product feature

    DEBUG("checking step 2 \n");
    if (feature_value != PRODUCT_FEATURE_VALUE()) {
        set_result_value(result, module_id, FAIL_CODE_STAGE_2, feature_value);

        DEBUG("checking step 2 fail\n");
        goto check_fail;
    }
    DEBUG("checking step 2 success \n");

    //3rd : check product  feature reg address is validate or not

    DEBUG("checking step 3 \n");
    if (REG_ADDR(feature_reg) != PRODUCT_FEATURE_REG()) {
        set_result_value(result, module_id, FAIL_CODE_STAGE_3, feature_reg);
        DEBUG("checking step 3 fail\n");
        goto check_fail;
    }
    DEBUG("checking step 3 success \n");



    //4st : check product id and address

    DEBUG("checking step 4 \n");
    if (product_id != GET_PRODUCT_ID(product_reg)) {
        set_result_value(result, module_id, FAIL_CODE_STAGE_4, product_id);
        DEBUG("checking step 4 fail\n");
        goto check_fail;
    }
    DEBUG("checking step 4 success\n");

    //5th : check product id

    DEBUG("checking step 5 \n");
    if (product_id != PRODUCT_ID()) {
        set_result_value(result, module_id, FAIL_CODE_STAGE_5, product_id);

        DEBUG("checking step 5 fail\n");
        goto check_fail;
    }
    DEBUG("checking step 5 success \n");

    //6th : check product reg address is validate or not

    DEBUG("checking step 6 \n");
    if (REG_ADDR(product_reg) != PRODUCT_REG()) {
        set_result_value(result, module_id, FAIL_CODE_STAGE_6, product_reg);
        DEBUG("checking step 6 fail\n");
        goto check_fail;
    }
    DEBUG("checking step 6 success \n");

    // check feature
    DEBUG("checking feature bit\n");
    if (feature_value) {
        int feature_bit = __module_id_2_feature_bit(module_id);
        if (feature_bit != FEATURE_BIT_UNKNOWN) {
            if (!(feature_value & feature_bit)) {
                set_result_value(result, module_id, FAIL_CODE_STAGE_7, feature_value);
                DEBUG("checking step 7 fail\n");
                goto check_fail;
            }
            feature_check_ok = 1;
        }
    }

    DEBUG("checking feature bit success \n");

    //check product  feature with product id passed in
    DEBUG("checking product feature 1\n");
    {
        int i = 0;
        int check_ok = 0;
        int pid = product_id;

        //8th : check product id is in product list or not
        for (i = 0; i < PRODUCT_MAX; i++) {
            DEBUG("check list max[%d] item[%d] product id[0x%x] \n", PRODUCT_MAX, i,
                  g_product_info_list[i].product_id);
            if (g_product_info_list[i].product_id == pid) {
                DEBUG("product id [%d] found \n", pid);
                check_ok = 1;

                //8th : check product feature
                int mask = MODULE_ID_2_MASK(module_id);
                DEBUG("check %d product feature[0x%x] mask[0x%x]\n", i,
                      g_product_info_list[i].features, mask);
                if (mask) {
                    //module feature found, check feature
                    int feature = g_product_info_list[i].features;

                    if (!(feature & mask)) {

                        DEBUG("checking step 8 fail,module id[%d] product_id[0x%08x]\n", module_id,
                              pid);
                        set_result_value(result, module_id, FAIL_CODE_STAGE_8, pid);
                        goto check_fail;
                    }
                } else {
                    //module feature unknown, skip
                    DEBUG("checking step 8 unknown,module id[%d] \n", module_id);
                    goto check_unknown;
                }

                break;
            }

        }

        if (!check_ok && !feature_check_ok) {
            //product is out of check
            DEBUG("checking step 9 fail,[0x%x]\n", product_id);
            set_result_value(result, module_id, FAIL_CODE_STAGE_9, product_id);
            goto check_fail;
        }

    }
    DEBUG("check product feature 1 pass\n");


    //check product  feature with product id in reg
    DEBUG("checking product feature 2\n");
    {
        int i = 0;
        int check_ok = 0;
        int pid = PRODUCT_ID();
        //check product id is in product list or not

        for (i = 0; i < PRODUCT_MAX; i++) {

            DEBUG("check list max[%d] item[%d] product id[0x%x] \n", PRODUCT_MAX, i,
                  g_product_info_list[i].product_id);
            if (g_product_info_list[i].product_id == pid) {
                check_ok = 1;

                //check product feature
                int mask = MODULE_ID_2_MASK(module_id);
                DEBUG("check %d product feature[0x%x] mask[0x%x]\n", i,
                      g_product_info_list[i].features, mask);
                if (mask) {
                    //module feature found, check feature
                    int feature = g_product_info_list[i].features;

                    if (!(feature & mask)) {

                        DEBUG("checking step 8 fail,module id[%d] product_id[0x%08x]\n", module_id,
                              pid);
                        set_result_value(result, module_id, FAIL_CODE_STAGE_10, pid);
                        goto check_fail;
                    }
                } else {
                    //module feature unknown, skip
                    DEBUG("checking step 8 unknown,module id[%d] \n", module_id);
                    goto check_unknown;
                }

                break;
            }
        }

        if (!check_ok && !feature_check_ok) {
            //product is out of check

            DEBUG("checking step 11 fail,[0x%x]\n", product_id);
            set_result_value(result, module_id, FAIL_CODE_STAGE_11, product_id);
            goto check_fail;
        }

    }
    DEBUG("check product feature 2 pass\n");

    //check pass
    set_result_value(result, module_id, FAIL_CODE_SUCCESS, product_id);

    DEBUG("check product pass all\n");
    return 0;

    check_unknown:
    return feature_check_ok ? 0 : -1;

    check_fail:
    return -1;

}

static void _module_get_chk_result(module_chk_result_cb cb) {
    chk_info_t *info = g_chk_info;
    chk_result_t *heap_result = (chk_result_t *) info->result;

    chk_result_t *globle_result = (chk_result_t *) PRODUCT_MEM_ANCHOR_ADDR;
    chk_result_t result;

    //1st : report all globle check result though callback
    cb(globle_result, sizeof(chk_result_t) * MODULE_PRODUCT_ID_MAX);

    //2nd : check fail code in heap
#if CONFIG_CHECK_RESULT_CRYPTO == 1
    __decrypto(&result, &heap_result[info->module_id], sizeof(result), info->key);
#else
    memcpy(&result,&heap_result[info->module_id],sizeof(result));
#endif

    if (result.fail_code != FAIL_CODE_SUCCESS) {
        goto check_fail;
    }

    //3rd : recheck all
    int fail = __check(&result, info->module_id, info->feature_value, info->feature_reg,
                       info->product_id, info->product_reg);
    if (fail < 0) {
        goto check_fail;
    }

    return;

    check_fail:
    cb(&result, sizeof(chk_result_t));
    memset((void *) FIRMWARE_ENTRY_ADDR, 0, FIRMWARE_SIZE);
    return;

}


module_get_chk_result
module_product_check(int module_id, int feature_value, int feature_reg, int product_id,
                     int product_reg, int mem_anchor) {
    chk_info_t *info = (chk_info_t *) mem_anchor;
#ifndef PRODUCT_CHECK_REMOTE_SUPPORT
    chk_result_t * globle_result = (chk_result_t *)PRODUCT_MEM_ANCHOR_ADDR;
#endif
    chk_result_t *heap_result = info->result;

    chk_result_t result;
    DEBUG("%s %d \n", __FUNCTION__, __LINE__);

#if CONFIG_CHECK_RESULT_CRYPTO == 1
    int key = *((int *) ((int *) info - 2000));
    info->key = key;
#endif
    info->module_id = module_id;
    info->product_id = product_id;
    info->product_reg = product_reg;
    info->feature_value = feature_value;
    info->feature_reg = feature_reg;
    DEBUG("%s %d \n", __FUNCTION__, __LINE__);

    g_chk_info = info;

    DEBUG("module id : %d feature : 0x%x feature reg : 0x%08x product id : 0x%x product reg : 0x%08x memachor : 0x%08x\n",
          module_id, feature_value, feature_reg, product_id, product_reg, mem_anchor);

    int fail = __check(&result, module_id, feature_value, feature_reg, product_id, product_reg);

    DEBUG("check result : %d\n", fail);
    DEBUG("check result : fail_code[%d] module_id[%d] value[0x%x]\n", result.fail_code,
          result.module_id, result.value);

#if CONFIG_CHECK_RESULT_CRYPTO == 1
    __encrypto(&heap_result[module_id], &result, sizeof(result), key);
#else
    memcpy(&heap_result[module_id],&result,sizeof(result));
#endif
#ifndef PRODUCT_CHECK_REMOTE_SUPPORT
    memcpy(&globle_result[module_id],&result,sizeof(result));
#endif
    if (fail < 0) {
#ifndef PRODUCT_CHECK_REMOTE_SUPPORT
#if CONFIG_CHECK_RESULT_UPLOAD == 1
        //just autolink support check result uplaod
        if(module_id != MODULE_PRODUCT_ID_AUTOLINK)
#endif
        {
            memset((void*)FIRMWARE_ENTRY_ADDR,0,FIRMWARE_SIZE);
        }
#endif
        return (void *) 0;
    }

    return _module_get_chk_result;
}


int module_product_check_simple(int module_id) {

    chk_info_t mem_anchor;

    DEBUG("%s %d \n", __FUNCTION__, __LINE__);

    int arg_pid = (int) PRODUCT_ID();
    int arg_pid_reg = (int) PRODUCT_REG();
    int arg_feature = (int) PRODUCT_FEATURE_VALUE();
    int arg_feature_reg = (int) PRODUCT_FEATURE_REG();
    DEBUG("%s %d \n", __FUNCTION__, __LINE__);

    int ret = 0;

    if (!module_product_check(module_id, arg_feature, arg_feature_reg, arg_pid, arg_pid_reg,
                              (int) &mem_anchor)) {
        ret = -1;
    }
    DEBUG("%s %d \n", __FUNCTION__, __LINE__);
    return ret;
}

#ifdef PRODUCT_CHECK_REMOTE_SUPPORT

int module_product_remote_check(int module_id, int *feature, int *product_id) {

    chk_info_t mem_anchor;

    int arg_pid = *product_id;
    int arg_pid_reg = (int) product_id;
    int arg_feature = *feature;
    int arg_feature_reg = (int) feature;

    int ret = 0;

    if (!module_product_check(module_id, arg_feature, arg_feature_reg, arg_pid, arg_pid_reg,
                              (int) &mem_anchor)) {
        ret = -1;
    }
    return ret;
}

typedef void (*module_product_remote_check_result_cb)(const chk_result_t *result);

int module_product_remote_check_result(const void *result, int size,
                                       module_product_remote_check_result_cb cb) {
    const chk_result_t *result_ = (const chk_result_t *) result;
    int i = 0;

    for (i = 0; i < MODULE_PRODUCT_ID_MAX; i++) {
        cb(&result_[i]);
    }
}


#endif

#ifdef PRODUCT_CHECK_TEST

#include <stdlib.h>
#include <assert.h>

#define TEST_OUT(fmt, arg...) diag_printf(TAG"[D]"fmt,##arg)

static void module_chk_result(const void *result, int size) {
    assert(result);
    const chk_result_t *res = (chk_result_t *) result;
    if (size == sizeof(chk_result_t)) {
        TEST_OUT("----product check result for fail----\n");
        TEST_OUT("id[0x%x] value[0x%x] fail code[%d] \n", res->module_id, res->value,
                 res->fail_code);
    } else if (size == sizeof(chk_result_t) * MODULE_PRODUCT_ID_MAX) {
        TEST_OUT("----product check result all---- \n");
        int i = 0;
        for (i = 0; i < MODULE_PRODUCT_ID_MAX; i++) {
            TEST_OUT("product idx[%d] id[0x%x] value[0x%x] fail code[%d] \n", i, res[i].module_id,
                     res[i].value, res[i].fail_code);
        }
    } else {

        TEST_OUT("TEST flow error ,size [%d]\n", size);
        assert(0);
    }
}

#ifdef PRODUCT_REG_USER_DEFINE


#define PID_SUPPORT         0x18
#define PID_SUPPORT_PART    0x21
#define PID_UNKNOWN         0xf2

static const struct {
    int feature;
    int product_id;

    int module_id;
    int expect;
    void (*result)(const void* result, int size);

} g_test_patten[] = {
    //patten 0 , feature support,  product support , success
    {
        FEATURE_BIT_AUTOLINK,
        PID_SUPPORT,

        MODULE_PRODUCT_ID_AUTOLINK,

        0,// success
        module_chk_result
    },
    //patten 1, feature support ,  product unsupport, fail
    {
        FEATURE_BIT_AIRPLAY,
        PID_SUPPORT_PART,

        MODULE_PRODUCT_ID_AIRPLAY,

        -1,// fail
        module_chk_result
    },
    //patten 2, feature support ,  product unknown, success
    {
        FEATURE_BIT_AIRPLAY,
        PID_UNKNOWN,

        MODULE_PRODUCT_ID_AIRPLAY,

        0,// success
        module_chk_result
    },


    //patten 3, feature  unsupport , product support, fail
    {
        FEATURE_BIT_AIRPLAY,
        PID_SUPPORT,

        MODULE_PRODUCT_ID_CARPLAY,
        -1,// fail
        module_chk_result
    },
    //patten 4, feature  unsupport , product unsupport, fail
    {
        FEATURE_BIT_AIRPLAY,
        PID_SUPPORT_PART,

        MODULE_PRODUCT_ID_CARPLAY,
        -1,// fail
        module_chk_result
    },
    //patten 5, feature  unsupport , product unknown, fail
    {
        FEATURE_BIT_AIRPLAY,
        PID_UNKNOWN,

        MODULE_PRODUCT_ID_CARPLAY,
        -1,// fail
        module_chk_result
    },


    //patten 6, feature unknown(support), product support , success
    {
        FEATURE_BIT_AIRPLAY,
        PID_SUPPORT,

        MODULE_PRODUCT_ID_CARLIFE,
        0,// success
        module_chk_result
    },
    //patten 7, feature unknown(support), product unsupport , fail
    {
        FEATURE_BIT_AIRPLAY,
        PID_SUPPORT_PART,

        MODULE_PRODUCT_ID_CARLIFE,
        -1,// fail
        module_chk_result
    },
    //patten 8, feature unknown(support), product unknown(unsupport) , fail
    {
        FEATURE_BIT_AIRPLAY,
        PID_UNKNOWN,

        MODULE_PRODUCT_ID_CARLIFE,
        -1,// fail
        module_chk_result
    },

};

#define TEST_PATTEN_MAX ARRAY_SIZE(g_test_patten)

#endif


void product_check_test(void) {
    void *mem_anchor = malloc(2048);
    module_get_chk_result chk_cb = NULL;
    int i = 0;
    int id = 0;

#ifdef PRODUCT_REG_USER_DEFINE
    for(i = 0 ; i < TEST_PATTEN_MAX ; i++){
        TEST_OUT("TEST PATTEN[%d] ENTER \n", i);
        TEST_OUT("TEST PATTEN module id : %d feature : 0x%08x product id : 0x%08x expect : %d\n",
            g_test_patten[i].module_id,g_test_patten[i].feature, g_test_patten[i].product_id,g_test_patten[i].expect);

        // set otp values
        SET_PRODUCT_ID(g_test_patten[i].product_id);
        SET_PRODUCT_FEATURE(g_test_patten[i].feature);
        id = g_test_patten[i].module_id;

        // get otp
        int product_id = (int)PRODUCT_ID();
        int product_reg = (int)PRODUCT_REG();

        int feature_value = (int)PRODUCT_FEATURE_VALUE();
        int feature_reg = (int)PRODUCT_FEATURE_REG();

        TEST_OUT("module id : %d feature : 0x%x feature reg : 0x%08x product id : 0x%x product reg : 0x%08x memachor : 0x%08x\n",
            id,feature_value, feature_reg, product_id,product_reg,mem_anchor);

        chk_cb = module_product_check(id,feature_value, feature_reg ,product_id,product_reg,(int)mem_anchor);
        if(g_test_patten[i].expect == 0){
            if(chk_cb){
                chk_cb(g_test_patten[i].result);
            }else{
                TEST_OUT("TEST PATTEN[%d] FAIL EXPECT[%d] RESULT[%d]\n",i,g_test_patten[i].expect,1);
                break;
            }
        }else{
            if(chk_cb){
                TEST_OUT("TEST PATTEN[%d] FAIL EXPECT[%d] RESULT[%d]\n",i,g_test_patten[i].expect,0);
                break;
            }
        }

        TEST_OUT("TEST PATTEN[%d] EXIT \n\n\n", i);
    }

#else

    //1st : test check pass

    for (id = 0; id < MODULE_PRODUCT_ID_MAX; id++) {

        TEST_OUT("TEST[%d] ENTER \n", id);

        int product_id = (int) PRODUCT_ID();
        int product_reg = (int) PRODUCT_REG();

        int feature_value = (int) PRODUCT_FEATURE_VALUE();
        int feature_reg = (int) PRODUCT_FEATURE_REG();

        TEST_OUT(
                "module id : %d feature : 0x%x feature reg : 0x%08x product id : 0x%x product reg : 0x%08x memachor : 0x%08x\n",
                id, feature_value, feature_reg, product_id, product_reg, mem_anchor);

        chk_cb = module_product_check(id, feature_value, feature_reg, product_id, product_reg,
                                      (int) mem_anchor);
        if (chk_cb) {
            chk_cb(module_chk_result);
            TEST_OUT("product check pass \n");
        } else {
            TEST_OUT("product check fail \n");
        }

        TEST_OUT("TEST[%d] EXIT \n\n\n", id);
    }

    //2nd : test all the case of product id
    for (id = 0; id < MODULE_PRODUCT_ID_MAX; id++) {
        int feature_value = (int) PRODUCT_FEATURE_VALUE();
        int feature_reg = (int) PRODUCT_FEATURE_REG();

        for (i = 0; i < 256; i++) {
            TEST_OUT("TEST[%d] ENTER \n", id);
            TEST_OUT("module id : %d product id : 0x%x product reg : 0x%08x memachor : 0x%08x\n",
                     id, i, PRODUCT_REG(), mem_anchor);
            chk_cb = module_product_check(id, feature_value, feature_reg, i, (int) PRODUCT_REG(),
                                          (int) mem_anchor);
            if (chk_cb) {
                TEST_OUT("product check pass \n");
                chk_cb(module_chk_result);
            } else {
                TEST_OUT("product check fail \n");
            }
            TEST_OUT("TEST[%d] EXIT \n\n\n", id);
        }
    }
    //3rd : test all the case of product id with error product reg
    for (id = 0; id < MODULE_PRODUCT_ID_MAX; id++) {
        int feature_value = (int) PRODUCT_FEATURE_VALUE();
        int feature_reg = (int) PRODUCT_FEATURE_REG();

        for (i = 0; i < 256; i++) {
            TEST_OUT("TEST[%d] ENTER \n", id);
            TEST_OUT("module id : %d product id : 0x%x product reg : 0x%08x memachor : 0x%08x\n",
                     id, i, (int) 0x00500000, mem_anchor);
            chk_cb = module_product_check(id, feature_value, feature_reg, i, (int) PRODUCT_REG(),
                                          (int) mem_anchor);
            assert(!chk_cb);

            TEST_OUT("TEST[%d] EXIT \n\n\n", id);
        }
    }
#endif

    free(mem_anchor);
}

#endif


