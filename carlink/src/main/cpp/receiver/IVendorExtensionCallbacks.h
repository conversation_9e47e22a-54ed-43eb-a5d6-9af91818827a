// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_IVENDOR_EXTENSION_CALLBACKS_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_IVENDOR_EXTENSION_CALLBACKS_H

#include "util/common.h"

/**
 * This class describes the interface that every sink implementation must subclass
 * to implement vendor extension channels over the GAL protocol. You should look at
 * the class VendorExtension for more documentation.
 */
class IVendorExtensionCallbacks {
public:
    virtual ~IVendorExtensionCallbacks() {}

    /**
     * Gets called when a new message is received on this endpoint.
     * @param data A pointer to the data.
     * @param len The size of the data.
     * @return STATUS_SUCCESS on success, an appropriate error otherwise.
     */
    virtual int dataAvailable(uint8_t *data, uint32_t len) = 0;
};

#endif // ANDROID_AUTO_PROJECTION_PROTOCOL_IVENDOR_EXTENSION_CALLBACKS_H
