// Copyright 2014 Google Inc. All Rights Reserved.

#include "Transport.h"

bool Transport::start() {
    bool ret = mReaderThread.start();
    mReaderThread.setPriority(80);
    ret |= mWriterThread.start();
    return ret;
}

void Transport::requestStop() {
    mStopping = true;
    mReceiver->prepareShutdown();
    abortReads();
}

void Transport::waitForExit() {
    mReaderThread.join();
    LOG("Reader thread joined");
    mWriterThread.join();
    LOG("Writer thread joined");
}

bool Transport::readOrFail(void *buf, size_t len) {
    int bytesRemaining = len;
    auto *ptr = (uint8_t *) buf;
    while (bytesRemaining > 0 && !mStopping) {
        int ret = read(ptr, bytesRemaining);
        if (ret == TRANSPORT_ERROR_FATAL) {
            LOGE("Encounted fatal error on read.");
            return false;
        } else if (ret == TRANSPORT_ERROR_NONFATAL) {
            // Keep going till it succeeds or a stop is requested.
            continue;
        }
        ptr += ret;
        bytesRemaining -= ret;
    }

    // Still need to return false in the mStopping case.
    return bytesRemaining == 0;
}

bool Transport::writeOrFail(void *buf, size_t len) {
    int bytesRemaining = len;
    auto *ptr = (uint8_t *) buf;
    while (bytesRemaining > 0 && !mStopping) {
        int ret = write(ptr, bytesRemaining);
        if (ret == TRANSPORT_ERROR_FATAL) {
            LOGE("Encounted fatal error on write.");
            return false;
        } else if (ret == TRANSPORT_ERROR_NONFATAL) {
            // Keep going till it succeeds or a stop is requested.
            continue;
        }
        ptr += ret;
        bytesRemaining -= ret;
    }

    // Still need to return false in the mStopping case.
    return bytesRemaining == 0;
}

void GalReaderThread::run() {
    uint8_t header[FRAME_HEADER_MIN_LENGTH];
    while (!mTransport->mStopping) {
        bool ret = mTransport->readOrFail(header, sizeof(header));
        // If we are stopping we can afford to throw away the data anyway.
        if (mTransport->mStopping) {
            LOG("Reader thread shutting down on request.");
            break;
        }

        if (!ret) {
            mTransport->requestStop();
            break;
        }

        int len = mTransport->mReceiver->getAdditionalBytesToRead(header);
        if (len <= 0) {
            // Something is horribly broken and the length we read out of the header
            // was negative. Log an error and abort.
            LOGE("Bad payload length %d. Shutting down due to potential framing error.", len);
            mTransport->requestStop();
            break;
        }

        size_t total = len + sizeof(header);
        shared_ptr<IoBuffer> buf = mTransport->mReceiver->allocateBuffer(total);
        memcpy(buf->raw(), header, sizeof(header));
        ret = mTransport->readOrFail((uint8_t *) buf->raw() + sizeof(header), len);
        if (!ret) {
            mTransport->requestStop();
            break;
        }
        mTransport->mReceiver->queueIncoming(buf);
        // Don't touch buf after this.
    }
    LOG("Reader thread exiting.");
}

void GalWriterThread::run() {
    IoBuffer buf;
    while (!mTransport->mStopping) {
        bool ret = mTransport->mReceiver->getEncodedFrame(&buf);
        if (ret && !mTransport->mStopping) {
            ret = mTransport->writeOrFail(buf.raw(), buf.size());
            if (!ret) {
                mTransport->requestStop();
                break;
            }
        }
    }
    LOG("Writer thread exiting.");
}
