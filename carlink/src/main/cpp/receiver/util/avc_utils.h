//
// Created by w.feng on 2020/6/2.
//

#ifndef AUTOLINK_AVC_UTILS_H
#define AUTOLINK_AVC_UTILS_H

#include "util/common.h"

status_t getNextNALUnit(
        const uint8_t **_data, size_t *_size,
        const uint8_t **nalStart, size_t *nalSize,
        bool startCodeFollows = false);

bool IsIDR(const uint8_t *data, size_t size);

bool IsAVCReferenceFrame(const shared_ptr<IoBuffer> &accessUnit);


#endif //AUTOLINK_AVC_UTILS_H
