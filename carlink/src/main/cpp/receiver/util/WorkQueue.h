// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_CORE_UTIL_WORK_QUEUE_H
#define AUTOLINK_PROTOCOL_CORE_UTIL_WORK_QUEUE_H

#include <deque>

#include "Mutex.h"
#include "shared_ptr.h"
#include "SemaphoreWrapper.h"
#include "Thread.h"

using std::deque;

/**
 * A class that represents a work item that can be submitted to a work queue. You
 * must extend this class and implement the run method.
 */
class WorkItem {
public:
    virtual ~WorkItem() {}

    /**
     * Implement this method to do the work that you want when the workqueue gets around
     * to running this work item. Given that you are extending this class, you should be
     * able to set up any state while initializing the object.
     */
    virtual void run() = 0;
};

class WorkQueue;

/**
 * @internal
 * The executor thread for the workqueue. You should not have to instantiate this class directly.
 */
class WorkQueueThread : public Thread {
public:
    WorkQueueThread() : mQueue(NULL) {}

    void setQueue(WorkQueue *queue) { mQueue = queue; }

    virtual void run();

private:
    WorkQueue *mQueue;
};

#define DEFAULT_WORKQUEUE_THREADS   4 /**< The number of workqueue threads if unspecified. **/

/**
 * A generic work queue. Accepts work items and executes them on worker threads.
 */
class WorkQueue {
public:
    /**
     * Creates a normal priority workqueue with the default number of threads.
     */
    WorkQueue()
            : mThreads(NULL),
              mNumThreads(DEFAULT_WORKQUEUE_THREADS),
              mHighPriority(false),
              mIsStopping(false) {}

    /**
     * Create a workqueue with the specified priority and threads.
     * @param numThreads How many threads should be created.
     * @param highPriority True if this should use high priority threads, false otherwise.
     */
    WorkQueue(int numThreads, bool highPriority)
            : mThreads(NULL),
              mNumThreads(numThreads),
              mHighPriority(highPriority),
              mIsStopping(false) {}

    ~WorkQueue();

    /**
     * Starts up the work queue.
     */
    bool start();

    /**
     * Add a new work item to be executed.
     * @param work The work item to be executed.
     */
    void queueWork(const shared_ptr<WorkItem> &work);

    /**
     * Shuts down the work queue and waits for all the threads to exit. In flight work items
     * will be completed, work items that have not been started yet will be discarded.
     */
    void shutdown();

    /**
     * Sets the name of the workqueue and by extension the name of the threads in the work
     * queue. For instance if you call setName("foo"), the threads will be named foo-0 .. foo-n - 1.
     * @param name The name of the work queue. Should be 24 characters or less.
     */
    void setName(const char *name);

    friend class WorkQueueThread;

private:
    /**
     * Dequeue a work item to be executed. Will block.
     * @return The work item to be executed.
     */
    shared_ptr<WorkItem> dequeueWork();

    deque<shared_ptr<WorkItem> > mWorkItems;
    Semaphore mAvailable;
    Mutex mLock;
    WorkQueueThread *mThreads;
    int mNumThreads;
    bool mHighPriority;
    volatile bool mIsStopping;
};

#endif //AUTOLINK_PROTOCOL_CORE_UTIL_WORK_QUEUE_H
