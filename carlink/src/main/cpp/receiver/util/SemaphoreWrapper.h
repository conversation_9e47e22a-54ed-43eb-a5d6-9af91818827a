// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef SUNPLUS_AUTOLINK_PROTOCOL_CORE_UTIL_SEM_H
#define SUNPLUS_AUTOLINK_PROTOCOL_CORE_UTIL_SEM_H

#include <semaphore.h>

/**
 * Wrap a semaphore. Customize this to suit the target platform & OS.
 * The android implementation uses a standard linux semaphore.
 */
class Semaphore {
public:
    Semaphore();

    Semaphore(int value);

    ~Semaphore();

    bool down();

    bool up();

private:
    sem_t mSem;
};

#endif // SUNPLUS_AUTOLINK_PROTOCOL_CORE_UTIL_SEM_H
