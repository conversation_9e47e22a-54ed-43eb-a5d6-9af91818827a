// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_CORE_UTIL_TRANSPORT_H
#define AUTOLINK_PROTOCOL_CORE_UTIL_TRANSPORT_H

#include "shared_ptr.h"
#include "../GalReceiver.h"
#include "Thread.h"

class Transport;

/**
 * @internal
 * This class is used internally by the Transport class. You should not instantiate
 * it directly. Reads from the transport and writes to the GalReceiver.
 */
class GalReaderThread : public Thread {
public:
    GalReaderThread(Transport *transport) : mTransport(transport) {}

protected:
    virtual void run();

private:
    Transport *mTransport;
};

/**
 * @internal
 * This class is used internally by the Transport class. You should not instantiate
 * it directly. Reads from the GalReceiver and writes out to the transport.
 */
class GalWriterThread : public Thread {
public:
    GalWriterThread(Transport *transport) : mTransport(transport) {}

protected:
    virtual void run();

private:
    Transport *mTransport;
};


#define TRANSPORT_ERROR_FATAL       -1 /**< The transport cannot continue to operate. */
#define TRANSPORT_ERROR_NONFATAL    -2 /**< Operations may succeed if retried. */

/**
 * Forms a base class that helps out with the handling the underlying transport
 * in GAL. It spins up threads for reading and writing to the transport and calls
 * the appropriate GAL interfaces in the GalReceiver.You may extend this class to
 * work with USB or WiFi or any other physical transport medium as you deem necessary.
 *
 * Typically your implementation would then look like this:
 * <pre>
 *      shared_ptr<GalReceiver> receiver(new GalReceiver());
 *      // Set up the receiver object and call start on it.
 *      UsbTransport transport(receiver); // Extends Transport.
 *      transport.start();
 *      // Keeps running till requestStop is called or the transport disconnects.
 *      transport.waitForExit();
 *      receiver->shutdown();
 */
class Transport {
public:
    Transport(const shared_ptr<GalReceiver> receiver)
            : mReceiver(receiver),
              mReaderThread(this),
              mWriterThread(this),
              mStopping(false) {}

    virtual ~Transport() {}

    /**
     * Start the transport.
     * @return True on success, false otherwise.
     */
    bool start();

    /**
     * Call this method to request that the reader and writer thread stop. Follow up
     * with a call to waitForExit() to make sure that they have actually stopped.
     */
    void requestStop();

    /**
     * Wait for the reader and writer thread to terminate.
     */
    void waitForExit();

    /**
     * Get a pointer to the reader thread. Use with caution.
     * @return A pointer to the reader thread.
     */
    Thread *getReaderThread() {
        return &mReaderThread;
    }

    /**
     * Get a pointer to the writer thread. Use with caution.
     * @return A pointer to the writer thread.
     */
    Thread *getWriterThread() {
        return &mWriterThread;
    }

    friend class GalReaderThread;

    friend class GalWriterThread;

protected:
    /**
     * Override this function to allow it to abort any reads that threads may be blocked
     * on at the moment. This allows us to shut down cleanly.
     */
    virtual void abortReads() = 0;

    /**
     * Reads from the transport.
     * @param buf The buffer to read into.
     * @param len The number of bytes to read.
     * @return The number of bytes read on success, TRANSPORT_ERROR_FATAL or
     *         TRANSPORT_ERROR_NONFATAL in case of a failure.
     */
    virtual int read(void *buf, size_t len) = 0;

    /**
     * Writes to the transport.
     * @param buf The buffer to write out.
     * @param len The number of bytes to write.
     * @return The number of bytes written on success, TRANSPORT_ERROR_FATAL or
     *         TRANSPORT_ERROR_NONFATAL in case of a failure.
     */
    virtual int write(void *buf, size_t len) = 0;

private:
    /**
     * Read from the transport, handle retries.
     * @param buf The buffer to read into.
     * @param len The number of bytes to read.
     * @return true if all the bytes were read, false otherwise.
     */
    bool readOrFail(void *buf, size_t len);

    /**
     * Write to the transport, handle retries.
     * @param buf The buffer to write from.
     * @param len The number of bytes to write.
     * @return true if all the bytes were written, false otherwise.
     */
    bool writeOrFail(void *buf, size_t len);

    shared_ptr<GalReceiver> mReceiver;
    GalReaderThread mReaderThread;
    GalWriterThread mWriterThread;
    bool mStopping;
};

#endif // AUTOLINK_PROTOCOL_CORE_UTIL_TRANSPORT_H
