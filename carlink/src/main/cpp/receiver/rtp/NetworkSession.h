//
// Created by w.feng on 2020/6/1.
//

#ifndef AUTOLINK_NETWORKSESSION_H
#define AUTOLINK_NETWORKSESSION_H

#include "util/common.h"


struct NetworkSession {
    NetworkSession();


    status_t start();

    status_t stop();

    status_t createUDPSession(
            unsigned localPort,
            int32_t *sessionID);

    status_t createUDPSession(
            unsigned localPort,
            const char *remoteHost,
            unsigned remotePort,
            int32_t *sessionID);

    status_t createUDPSession(
            int32_t sessionID,
            const char *remoteHost,
            unsigned remotePort);

    status_t destroySession(int32_t sessionID);

    status_t sendRequest(
            int32_t sessionID,
            const shared_ptr<IoBuffer> &packet,
            bool timeValid = false,
            int64_t timeUs = -1ll);

    virtual ~NetworkSession();

private:
    struct NetworkThread;
    struct Session;

    Mutex mLock;
    shared_ptr<NetworkThread> mThread;

    int32_t mNextSessionID;

    int mPipeFd[2]{};

    map<int32_t, shared_ptr<Session>> mSessions;

    enum Mode {
        kModeCreateUDPSession,
        kModeCreateTCPDatagramSessionPassive,
        kModeCreateTCPDatagramSessionActive,
        kModeCreateRTSPServer,
        kModeCreateRTSPClient,
    };

    status_t createClientOrServer(
            Mode mode,
            const struct in_addr *addr,
            unsigned port,
            const char *remoteHost,
            unsigned remotePort,
            int32_t *sessionID);

    void threadLoop();

    void interrupt();

    static status_t MakeSocketNonBlocking(int s);
};


#endif //AUTOLINK_NETWORKSESSION_H
