//
// Created by w.feng on 2020/6/1.
//

#ifndef AUTOLINK_RTPSENDER_H
#define AUTOLINK_RTPSENDER_H

#include "util/common.h"
#include "RTPBase.h"
#include "NetworkSession.h"

struct NetworkSession;

struct RTPSender : public RTPBase {
    RTPSender(const shared_ptr<NetworkSession> &netSession);

    status_t initAsync(
            const char *remoteHost,
            int32_t remoteRTPPort,
            TransportMode rtpMode,
            int32_t remoteRTCPPort,
            TransportMode rtcpMode,
            int32_t *outLocalRTPPort);

    status_t queueBuffer(
            void *data,
            size_t len,
            uint8_t packType,
            PacketizationMode mode);

    virtual  ~RTPSender();

private:
    enum {
        kMaxNumTSPacketsPerRTPPacket = (kMaxUDPPacketSize - 12) / 188,
        kMaxHistorySize = 1024,
        kSourceID = 0xdeadbeef,
    };

    shared_ptr<NetworkSession> mNetSession;
    TransportMode mRTPMode;
    TransportMode mRTCPMode;
    int32_t mRTPSessionID;
    int32_t mRTCPSessionID;

    bool mRTPConnected;
    bool mRTCPConnected;
    uint32_t mRTPSeqNo;
    list<shared_ptr<IoBuffer>> mHistory;
    size_t mHistorySize;

    status_t queueAVCBuffer(void *data, size_t len, uint8_t packetType);

    status_t queueTSPackets(void *data, size_t len, uint8_t packetType);

    status_t sendRTPPacket(
            const shared_ptr<IoBuffer> &packet,
            bool storeInHistory,
            bool timeValid = false,
            int64_t timeUs = -1ll);

};

#endif //AUTOLINK_RTPSENDER_H
