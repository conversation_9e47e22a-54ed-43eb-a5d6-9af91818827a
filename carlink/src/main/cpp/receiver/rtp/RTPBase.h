//
// Created by w.feng on 2020/6/1.
//

#ifndef AUTOLINK_RTPBASE_H
#define AUTOLINK_RTPBASE_H

#include "util/common.h"

struct RTPBase {
    enum PacketizationMode {
        PACKETIZATION_TRANSPORT_STREAM,
        PACKETIZATION_H264,
        PACKETIZATION_NONE,
    };

    enum TransportMode {
        TRANSPORT_UNDEFINED,
        TRANSPORT_NONE,
        TRANSPORT_UDP,
        TRANSPORT_TCP,
        TRANSPORT_TCP_INTERLEAVED,
    };

    enum {
        kMaxUDPPacketSize = 1472,
    };

    static int32_t PickRandomRTPPort();

};

#endif //AUTOLINK_RTPBASE_H
