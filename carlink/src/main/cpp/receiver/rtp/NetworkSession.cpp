//
// Created by w.feng on 2020/6/1.
//

#include "NetworkSession.h"
#include "util/Timers.h"

static const size_t kMaxUDPSize = 1500;
static const int32_t kMaxUDPRetries = 50;

struct NetworkSession::NetworkThread : public Thread {
    explicit NetworkThread(NetworkSession *session);

    void stop();

    virtual ~NetworkThread();

protected:
    virtual void run();

private:
    bool mStopping;
    NetworkSession *mSession;

};

struct NetworkSession::Session {
    enum Mode {
        MODE_RTSP,
        MODE_DATAGRAM,
    };

    enum State {
        CONNECTING,
        CONNECTED,
        LISTENING_RTSP,
        LISTENING_TCP_DGRAMS,
        DATAGRAM,
    };

    Session(int32_t sessionID, State state, int s);

    int32_t sessionID() const;

    int socket() const;

    bool wantsToWrite();

    status_t writeMore();

    status_t sendRequest(
            const shared_ptr<IoBuffer> &packet,
            bool timeValid,
            int64_t timeUs);

    void setMode(Mode mode);

    virtual ~Session();

private:
    enum {
        FRAGMENT_FLAG_TIME_VALID = 1,
    };

    struct Fragment {
        uint32_t mFlags;
        int64_t mTimeUs;
        shared_ptr<IoBuffer> mBuffer;
    };

    int32_t mSessionID;
    State mState;
    Mode mMode;
    int mSocket;
    bool mSawSendFailure;
    int32_t mUDPRetries;
    list<Fragment> mOutFragments;

    void dumpFragmentStats(const Fragment &frag);
};

/******************************NetworkThread****************************/

NetworkSession::NetworkThread::NetworkThread(NetworkSession *session) : mSession(session) {
    mStopping = false;
}

NetworkSession::NetworkThread::~NetworkThread() = default;

void NetworkSession::NetworkThread::run() {
    while (!mStopping) {
        mSession->threadLoop();
    }
}

void NetworkSession::NetworkThread::stop() {
    LOGD("NetworkThread stop\n");
    mStopping = true;
}

/******************************Session****************************/

NetworkSession::Session::Session(
        int32_t sessionID,
        NetworkSession::Session::State state,
        int s)
        : mSessionID(sessionID),
          mState(state),
          mMode(MODE_DATAGRAM),
          mSocket(s),
          mSawSendFailure(false),
          mUDPRetries(kMaxUDPRetries) {

}

NetworkSession::Session::~Session() {
    LOGD("Session %d gone\n", mSessionID);
    close(mSocket);
    mSocket = -1;
}

int32_t NetworkSession::Session::sessionID() const {
    return mSessionID;
}

int NetworkSession::Session::socket() const {
    return mSocket;
}

void NetworkSession::Session::setMode(Mode mode) {
    mMode = mode;
}

bool NetworkSession::Session::wantsToWrite() {
    return !mSawSendFailure &&
           (mState == CONNECTING
            || (mState == DATAGRAM && !mOutFragments.empty())
            || (mState == CONNECTED && !mOutFragments.empty()));
}

status_t NetworkSession::Session::writeMore() {
    if (mOutFragments.empty()) {
        return INVALID_OPERATION;
    }
    int err;
    do {
        const Fragment &frag = *mOutFragments.begin();
        const shared_ptr<IoBuffer> &datagram = frag.mBuffer;
        int n;
        do {
            n = send(mSocket, datagram->raw(), datagram->size(), 0);
        } while (n < 0 && errno == EINTR);

        err = OK;

        if (n > 0) {
            mOutFragments.erase(mOutFragments.begin());
        } else if (n < 0) {
            err = -errno;
        } else if (n == 0) {
            err = -ECONNRESET;
        }
    } while (err == OK && !mOutFragments.empty());

    if (err == -EAGAIN) {
        if (!mOutFragments.empty()) {
            LOGD("%zu datagram remain queued.\n", mOutFragments.size());
        }
        err = OK;
    }

    if (err != OK) {
        if (!mUDPRetries) {
            LOGE("Send datagram failed");
            mSawSendFailure = true;
        } else {
            mUDPRetries--;
            LOGE("Send datagram failed,%d/%d retries left\n", mUDPRetries, kMaxUDPRetries);
            err = OK;
        }
    } else {
        mUDPRetries = kMaxUDPRetries;
    }
    return err;
}

status_t NetworkSession::Session::sendRequest(
        const shared_ptr<IoBuffer> &packet,
        bool timeValid,
        int64_t timeUs) {
    Fragment frag;
    frag.mFlags = 0;
    if (timeValid) {
        frag.mFlags = FRAGMENT_FLAG_TIME_VALID;
        frag.mTimeUs = timeUs;
    }
    frag.mBuffer = packet;
    mOutFragments.push_back(frag);
    return OK;
}

void NetworkSession::Session::dumpFragmentStats(const NetworkSession::Session::Fragment &frag) {
#if 1
    int64_t nowUs = systemTime(SYSTEM_TIME_MONOTONIC) / 1000ll;
    int64_t delayMs = (nowUs - frag.mTimeUs) / 1000ll;

    static const int64_t kMinDelayMs = 0;
    static const int64_t kMaxDelayMs = 300;

    const char *kPattern = "########################################";
    size_t kPatternSize = strlen(kPattern);

    int n = (kPatternSize * (delayMs - kMinDelayMs))
            / (kMaxDelayMs - kMinDelayMs);

    if (n < 0) {
        n = 0;
    } else if ((size_t) n > kPatternSize) {
        n = kPatternSize;
    }

    LOGD("[%lld]: (%4lld ms) %s\n",
         frag.mTimeUs / 1000,
         delayMs,
         kPattern + kPatternSize - n);
#endif
}


/******************************NetworkSession****************************/

NetworkSession::NetworkSession()
        : mNextSessionID(1) {
    mPipeFd[0] = mPipeFd[1] = -1;
}

NetworkSession::~NetworkSession() {
    stop();
}

status_t NetworkSession::start() {
    if (mThread != nullptr) {
        return -ENOSYS;
    }

    int res = pipe(mPipeFd);
    if (res != 0) {
        mPipeFd[0] = mPipeFd[1] = -1;
        return -errno;
    }
    mThread = new NetworkThread(this);
    bool err = mThread->start();
    if (!err) {
        mThread = nullptr;
        close(mPipeFd[0]);
        close(mPipeFd[1]);
        mPipeFd[0] = mPipeFd[1] = -1;
        return -ENOSYS;
    }
    return OK;
}

status_t NetworkSession::stop() {
    if (mThread == nullptr) {
        return -ENOSYS;
    }
    mThread->stop();
    interrupt();
    mThread->join();
    mThread = nullptr;
    close(mPipeFd[0]);
    close(mPipeFd[1]);
    mPipeFd[0] = mPipeFd[1] = -1;
    return OK;
}

status_t NetworkSession::createUDPSession(
        unsigned int localPort,
        int32_t *sessionID) {
    return createUDPSession(localPort, nullptr, 0, sessionID);
}

status_t NetworkSession::createUDPSession(
        unsigned int localPort,
        const char *remoteHost,
        unsigned int remotePort,
        int32_t *sessionID) {

    return createClientOrServer(
            kModeCreateUDPSession,
            nullptr,
            localPort,
            remoteHost,
            remotePort,
            sessionID);
}

status_t NetworkSession::destroySession(int32_t sessionID) {
    Autolock autoLock(&mLock);
    map<int32_t, shared_ptr<Session>>::iterator iter;
    iter = mSessions.find(sessionID);
    if (iter == mSessions.end()) {
        return -ENOENT;
    }
    mSessions.erase(sessionID);
    interrupt();
    return OK;
}

status_t NetworkSession::MakeSocketNonBlocking(int s) {
    int flags = fcntl(s, F_GETFL, 0);
    if (flags < 0) {
        flags = 0;
    }
    int res = fcntl(s, F_SETFL, flags | O_NONBLOCK);
    if (res < 0) {
        return -errno;
    }
    return OK;
}

status_t NetworkSession::createClientOrServer(
        NetworkSession::Mode mode,
        const struct in_addr *localAddr,
        unsigned port,
        const char *remoteHost,
        unsigned remotePort,
        int32_t *sessionID) {
    Autolock autoLock(&mLock);
    *sessionID = 0;
    status_t err;
    int s, res;
    shared_ptr<Session> session;

    s = socket(AF_INET, (mode == kModeCreateUDPSession) ? SOCK_DGRAM : SOCK_STREAM, 0);

    if (s < 0) {
        err = -errno;
        goto bail;
    }

    if (mode == kModeCreateUDPSession) {
        int size = 512 * 1024;
        res = setsockopt(s, SOL_SOCKET, SO_RCVBUF, &size, sizeof(size));
        if (res < 0) {
            err = -errno;
            goto bail2;
        }
        res = setsockopt(s, SOL_SOCKET, SO_SNDBUF, &size, sizeof(size));
        if (res < 0) {
            err = -errno;
            goto bail2;
        }

    }

    err = MakeSocketNonBlocking(s);

    if (err != OK) {
        goto bail2;
    }

    struct sockaddr_in addr;
    memset(addr.sin_zero, 0, sizeof(addr.sin_zero));
    addr.sin_family = AF_INET;

    if (localAddr != nullptr) {
        addr.sin_addr = *localAddr;
        addr.sin_port = htons(port);
    } else {
        addr.sin_addr.s_addr = htonl(INADDR_ANY);
        addr.sin_port = htons(port);
    }

    res = bind(s, (const struct sockaddr *) &addr, sizeof(addr));
    if (res == 0) {
        if (remoteHost != nullptr) {
            struct sockaddr_in remoteAddr;
            memset(remoteAddr.sin_zero, 0, sizeof(remoteAddr.sin_zero));
            remoteAddr.sin_family = AF_INET;
            remoteAddr.sin_port = htons(remotePort);
            struct hostent *ent = gethostbyname(remoteHost);
            if (ent == nullptr) {
                err = -h_errno;
                goto bail2;
            }
            remoteAddr.sin_addr.s_addr = *(in_addr_t *) ent->h_addr;
            res = connect(s, (const struct sockaddr *) &remoteAddr, sizeof(remoteAddr));
        }
    }

    if (res < 0) {
        err = -errno;
        goto bail2;
    }

    Session::State state;
    switch (mode) {
        case kModeCreateRTSPClient:
        case kModeCreateTCPDatagramSessionActive:
            state = Session::CONNECTING;
            break;

        case kModeCreateTCPDatagramSessionPassive:
            state = Session::LISTENING_TCP_DGRAMS;
            break;

        case kModeCreateRTSPServer:
            state = Session::LISTENING_RTSP;
            break;

        default:
            state = Session::DATAGRAM;
            break;
    }

    session = new Session(mNextSessionID++, state, s);
    mSessions.insert(pair<int32_t, shared_ptr<Session>>(session->sessionID(), session));
    interrupt();
    *sessionID = session->sessionID();

    goto bail;
    bail2:
    close(s);
    s = -1;
    bail:
    return err;
}

int32_t NetworkSession::createUDPSession(int32_t sessionID, const char *remoteHost,
                                         unsigned int remotePort) {
    Autolock autoLock(&mLock);

    map<int32_t, shared_ptr<Session>>::iterator iter;
    iter = mSessions.find(sessionID);

    if (iter == mSessions.end()) {
        return -ENOENT;
    }
    const shared_ptr<Session> session = iter->second;
    int s = session->socket();

    struct sockaddr_in remoteAddr;
    memset(remoteAddr.sin_zero, 0, sizeof(remoteAddr.sin_zero));
    remoteAddr.sin_family = AF_INET;
    remoteAddr.sin_port = htons(remotePort);

    status_t err = OK;
    struct hostent *ent = gethostbyname(remoteHost);
    if (ent == nullptr) {
        err = -h_errno;
    } else {
        remoteAddr.sin_addr.s_addr = *(in_addr_t *) ent->h_addr;

        int res = connect(
                s,
                (const struct sockaddr *) &remoteAddr,
                sizeof(remoteAddr));

        if (res < 0) {
            err = -errno;
        }
    }

    return err;
}

int32_t NetworkSession::sendRequest(
        int32_t sessionID,
        const shared_ptr<IoBuffer> &packet,
        bool timeValid,
        int64_t timeUs) {
    Autolock autoLock(&mLock);
    map<int32_t, shared_ptr<Session>>::iterator iter;
    iter = mSessions.find(sessionID);

    if (iter == mSessions.end()) {
        return -ENOENT;
    }
    const shared_ptr<Session> session = iter->second;
    status_t err = session->sendRequest(packet, timeValid, timeUs);

    interrupt();

    return err;
}

void NetworkSession::interrupt() {
    static const char dummy = 0;
    ssize_t n;
    do {
        n = write(mPipeFd[1], &dummy, 1);
    } while (n < 0 && errno == EINTR);

    if (n < 0) {
        LOGW("Error writing to pipe (%s)", strerror(errno));
    }
}

void NetworkSession::threadLoop() {
    fd_set rs, ws;
    FD_ZERO(&rs);
    FD_ZERO(&ws);

    FD_SET(mPipeFd[0], &rs);
    int maxFd = mPipeFd[0];

    {
        Autolock autoLock(&mLock);

        for (const auto &item : mSessions) {
            const shared_ptr<Session> &session = item.second;
            int s = session->socket();
            if (s < 0) {
                continue;
            }
            if (session->wantsToWrite()) {
                FD_SET(s, &ws);
                if (s > maxFd) {
                    maxFd = s;
                }
            }
        }
    }

    int res = select(maxFd + 1, &rs, &ws, nullptr, nullptr);
    if (res == 0) {
        LOGW("timeout\n");
        return;
    }

    if (res < 0) {
        if (errno == EINTR) {
            return;
        }
        LOGE("select failed w/ error %d (%s)\n", errno, strerror(errno));
        return;
    }

    if (FD_ISSET(mPipeFd[0], &rs)) {
        char c;
        ssize_t n;
        do {
            n = read(mPipeFd[0], &c, 1);
        } while (n < 0 && errno == EINTR);

        if (n < 0) {
            LOGW("Error reading from pipe (%s)\n", strerror(errno));
        }
        --res;
    }

    {
        Autolock autoLock(&mLock);
        for (const auto &item : mSessions) {
            if (res <= 0) {
                break;
            }
            const shared_ptr<Session> &session = item.second;
            int s = session->socket();

            if (s < 0) {
                continue;
            }

            if (FD_ISSET(s, &ws)) {
                --res;
            }

            if (FD_ISSET(s, &ws)) {
                status_t err = session->writeMore();
                if (err != OK) {
                    LOGE("writeMore on socket %d failed w/ error %d (%s)\n", s, err,
                         strerror(-err));
                }
            }
        }
    }
}