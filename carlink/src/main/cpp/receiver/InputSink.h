// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_INPUT_SINK_H
#define AUTOLINK_PROTOCOL_INPUT_SINK_H

#include "util/common.h"
#include "IInputSinkCallbacks.h"
#include "ProtocolEndpointBase.h"

class InputSink : public ProtocolEndpointBase {
public:
    InputSink(uint8_t id, MessageRouter *router)
            : ProtocolEndpointBase(id, router, false),
              mSupportTouchScreen(false), mSupportTouchPad(false),
              mHasTouchScreen(false), mHasTouchPad(false) {}

    virtual void onChannelOpened(uint8_t channelId, uint32_t extraMessage);

    void
    registerCallbacks(const shared_ptr<IInputSinkCallbacks> &callbacks) { mCallbacks = callbacks; }

    bool discoverService(const Service &srv);

    int routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg);

    void setTouchScreenSupport(bool support) { mSupportTouchScreen = support; }

    void setTouchPadSupport(bool support) { mSupportTouchPad = support; }

    void setKeyCodesSupport(const set<int32_t> &keycodes) { mKeycodes = keycodes; }

private:
    void sendKeyBindingRequest();

    void handleInputReport(const InputReport &report);

    void handleKeyBindingResponse(const KeyBindingResponse &res);

    bool isBound(int32_t keycode);

    void receiveTouch(uint64_t timestamp, const TouchEvent *event);

    void receiveTouchPad(uint64_t timestamp, const TouchEvent *event);

    void receiveKey(uint64_t timestamp, const KeyEvent *event);

    void receiveAbsolute(uint64_t timestamp, const AbsoluteEvent *event);

    void receiveRelative(uint64_t timestamp, const RelativeEvent *event);

private:
    shared_ptr<IInputSinkCallbacks> mCallbacks;

    bool mSupportTouchScreen;
    bool mHasTouchScreen;
    int32_t mTouchScreenWidth;
    int32_t mTouchScreenHeight;
    TouchScreenType mTouchScreenType;

    bool mSupportTouchPad;
    bool mHasTouchPad;
    int32_t mTouchPadWidth;
    int32_t mTouchPadHeight;
    bool mTouchPadUiNavigation;
    int32_t mTouchPadPhysWidth;
    int32_t mTouchPadPhysHeight;

    set<int32_t> mKeycodes;
    set<int32_t> mBoundKeycodes;
};


#endif //AUTOLINK_PROTOCOL_VIDEO_SOURCE_H

