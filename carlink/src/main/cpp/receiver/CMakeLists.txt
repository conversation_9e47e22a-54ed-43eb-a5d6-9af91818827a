# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.

cmake_minimum_required(VERSION 3.22.1)

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# <PERSON>rad<PERSON> automatically packages shared libraries with your APK.

add_library( # Sets the name of the library.
        receiver

        # Sets the library as a shared library.
        STATIC

        # Provides a relative path to your source file(s).
        Channel.cpp
        ChannelManager.cpp
        Controller.cpp
        GalReceiver.cpp
        MediaSourceBase.cpp
        MessageRouter.cpp
        ProtocolEndpointBase.cpp
        VideoSource.cpp
        BluetoothEndpoint.cpp
        VendorExtension.cpp
        InputSink.cpp
        util/CircularBuffer.cpp
        util/Mutex.cpp
        util/Semaphore.cpp
        util/Thread.cpp
        util/Transport.cpp
        util/WorkQueue.cpp
        util/Timers.cpp
        util/avc_utils.cpp
        rtp/RTPSender.cpp
        rtp/NetworkSession.cpp
        product_check.c)
