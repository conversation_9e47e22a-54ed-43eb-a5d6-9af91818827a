// Copyright 2014 Google Inc. All Rights Reserved.

#include "VendorExtension.h"

void VendorExtension::sendData(void *data, size_t len) {
    queueOutgoing(data, len);
}

void VendorExtension::onChannelOpened(uint8_t channelId, uint32_t extraMessage) {
    ProtocolEndpointBase::onChannelOpened(channelId, extraMessage);
}

bool VendorExtension::discoverService(const Service &srv) {
    bool ret = false;
    if (srv.has_vendor_extension_service()) {
        const VendorExtensionService *ves = &srv.vendor_extension_service();
        const string &serviceName = ves->service_name();
        if (!serviceName.compare("com.sunplus.collector")) {
            printf("discoverService!!\n");
            ret = true;
        }
    }
    return ret;
}

int VendorExtension::handleRawMessage(uint8_t channelId, const shared_ptr<IoBuffer> &msg) {
    return mCallbacks->dataAvailable((uint8_t *) msg->raw(), (uint32_t) msg->size());
}


