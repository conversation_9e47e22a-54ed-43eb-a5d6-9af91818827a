// Copyright 2014 Google Inc. All Rights Reserved.

#include "Controller.h"

extern "C" {
#include "product_check.h"
}

int Controller::routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) {
    int ret = STATUS_UNEXPECTED_MESSAGE;
    uint8_t *ptr = (uint8_t *) msg->raw() + sizeof(uint16_t);
    size_t len = msg->size() - sizeof(uint16_t);
    switch (type) {
        case MESSAGE_VERSION_REQUEST: {
            LOG("[CONTROL] <- VersionRequest\n");
            ret = handleVersionRequest(ptr, len);
            break;
        }
        case MESSAGE_ENCAPSULATED_SSL: {
            LOG("[CONTROL] <- EncapsulatedSsl\n");
            ret = handleEncapsulatedSsl(ptr, len);
            break;
        }
        case MESSAGE_AUTH_COMPLETE: {
            LOG("[CONTROL] <- AuthComplete\n");
            ret = handleAuthComplete();
            break;
        }
        case MESSAGE_SERVICE_DISCOVERY_RESPONSE: {
            LOG("[CONTROL] <- ServiceDiscoversyResponse\n");
            ServiceDiscoveryResponse serviceResp;
            if (PARSE_PROTO(serviceResp, ptr, len)) {
                ret = handleServiceDiscoveryReponse(serviceResp);
            }
            break;
        }
        case MESSAGE_PING_REQUEST: {
            //LOG("PingRequest\n");
            PingRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handlePingRequest(req);
            }
            break;
        }
        case MESSAGE_PING_RESPONSE: {
            //LOG("PingResponse\n");
            PingResponse resp;
            if (PARSE_PROTO(resp, ptr, len)) {
                ret = handlePingResponse(resp);
            }
            break;
        }
        case MESSAGE_EXIT_REQUEST: {
            LOG("[CONTROL] <- ExitRequest\n");
            ExitRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleExitRequest();
            }
            break;
        }
        case MESSAGE_BYEBYE_REQUEST: {
            LOG("[CONTROL] <- ByeByeRequest\n");
            ByeByeRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleByeByteRequest(req.reason());
            }
            break;
        }
        case MESSAGE_BYEBYE_RESPONSE: {
            LOG("[CONTROL] <- ByeByeResponse\n");
            ret = handleByeByeResponse();
            break;
        }
        case MESSAGE_FORCE_LANDSCAPE_REQUEST: {
            LOG("[CONTROL] <- ForceLandscapeRequest\n");
            ForceLandscapeRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleForceLandscapeRequest(req.force());
            }
            break;
        }
        case MESSAGE_SCREEN_ORIENTATION_INQUIRE: {
            LOG("[CONTROL] <- ScreenOrientationInquire\n");
            ret = handleScreenOrientationInqure();
            break;
        }
        case MESSAGE_RUNNING_STATE_INQUIRE: {
            LOG("[CONTROL] <- RunningStateInquire\n");
            ret = handleRunningStateInquire();
            break;
        }
        case MESSAGE_AUTO_ROTATION_REQUEST: {
            LOG("[CONTROL] <- autoRotationRequest!\n");
            AutoRotationRequest req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleAutoRotationRequest(req.enable());
            }
            break;
        }
        case MESSAGE_READ_RESPONSE: {
            LOG("[CONTROL] <- readResponse!\n");
            ReadResponse req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleReadResponse(req);
            }
            break;
        }
        case MESSAGE_WRITE_RESPONSE: {
            LOG("[CONTROL] <- writeResponse!\n");
            WriteResponse req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleWriteResponse(req);
            }
            break;
        }
        case MESSAGE_SCREEN_RESOLUTION_INQUIRE: {
            LOG("[CONTROL] <- ScreenResolutionInquire!\n");
            ret = handleScreenResolutionInquire();
            break;
        }

        case MESSAGE_TIMEDATE_INQUIRE: {
            LOG("[CONTROL] <- TimeDateInquire!\n");
            ret = handleTimeDateInquire();
            break;
        }

        case MESSAGE_UNEXPECTED_MESSAGE: {
            LOG("[CONTROL] <- UnexpectedMessage!\n");
            ret = STATUS_SUCCESS;
            break;
        }

        case MWSSAGE_WLTOUCH_NOTIFICATION: {
            LOG("[CONTROL] <- WLTouchNotification!\n");
            WlTouchEvent req;
            if (PARSE_PROTO(req, ptr, len)) {
                ret = handleWLTouchNotification(req);
            }
            break;
        }
        default: {
            LOG("[CONTROL] <- [%d]Unknown Message!!!\n", type);
            break;
        }
    }
    return ret;
}

void Controller::start() {
    //do nothing here!
}

int Controller::handleWLTouchNotification(const WlTouchEvent &req) {
    mControllerCallbacks->wlTouchNotification(req.x(), req.y(), req.action(), req.timestamp());
    return STATUS_SUCCESS;
}

int Controller::handleAutoRotationRequest(bool autoed) {
    mControllerCallbacks->autoRotationRequest(autoed);
    return STATUS_SUCCESS;
}

int Controller::handleVersionRequest(void *msg, size_t len) {
    auto *ptr = (uint8_t *) msg;
    uint16_t major, minor, status;
    READ_BE16(ptr, major);
    READ_BE16(ptr + 2, minor);

    LOG("major=%hu, minor=%hu, ", major, minor);
    if (major == PROTOCOL_MAJOR_VERSION) {
        LOG("result:success\n");
        status = STATUS_SUCCESS;
        mControllerCallbacks->versionResponseCallback(major, minor);
    } else {
        LOG("result:fail\n");
        status = STATUS_NO_COMPATIBLE_VERSION;
    }

    mHealthy = (status == STATUS_SUCCESS);
    sendVersionResponse(status);
    return STATUS_SUCCESS;
}

int Controller::handleEncapsulatedSsl(void *msg, size_t len) {
    //TODO: check SSL certificate
    sendAuthResponse(STATUS_SUCCESS);
    return STATUS_SUCCESS;
}

int Controller::handleAuthComplete() {
    mControllerCallbacks->authCompleteCallback();
    sendServiceDiscoveryRequest();
    return STATUS_SUCCESS;
}

int Controller::handleServiceDiscoveryReponse(ServiceDiscoveryResponse &response) {
    string make = response.make();
    string model = response.model();
    string year = response.year();
    string id = response.vehicle_id();
    string HuIc = response.head_unit_ic();
    string HuMake = response.head_unit_make();
    string HuModel = response.head_unit_model();
    string HuSwBuild = response.head_unit_software_build();
    string HuSwVersion = response.head_unit_software_version();
    string HuSeries = response.head_unit_series();
    string HuMuVersion = response.head_unit_module_version();
    int HuScreenSize = response.head_unit_screen_size();
    int HuScreenType = response.head_unit_screen_touch_type();
    int HuScreenWidth = response.head_unit_screen_width();
    int HuScreenHeight = response.head_unit_screen_height();
    int HuCheckSum = response.checksum();
    mDriverPosition = 0;
    mSessionConfiguration = response.session_configuration();
    //LOG("make:%s\n", make.c_str());
    //LOG("model:%s\n", model.c_str());
    //LOG("year:%s\n", year.c_str());
    //LOG("id:%s\n", id.c_str());
    //LOG("hu ic:%s\n", HuIc.c_str());
    //LOG("hu series:%s\n", HuSeries.c_str());
    //LOG("hu make:%s\n", HuMake.c_str());
    //LOG("hu model:%s\n", HuModel.c_str());
    //LOG("hu swBuild:%s\n", HuSwBuild.c_str());
    //LOG("hu swVersion:%s\n", HuSwVersion.c_str());
    //LOG("hu checksum:%d\n", HuCheckSum);
    //LOG("hu muVersion:%s\n", HuMuVersion.c_str());
    mControllerCallbacks->serviceDiscoveryResponseCallback(id, make, model, year, HuIc, HuMake,
                                                           HuModel, HuSwBuild, HuSwVersion,
                                                           HuSeries, HuMuVersion, HuCheckSum);
    mRouter->discoverServices(response);
    return STATUS_SUCCESS;
}

int Controller::handlePingRequest(const PingRequest &req) {
    bool bugReport = req.has_bug_report() && req.bug_report();
    sendPingResponse(req.timestamp());
    mControllerCallbacks->pingRequestCallback(req.timestamp(), bugReport);
    return STATUS_SUCCESS;
}

int Controller::handlePingResponse(const PingResponse &resp) {
    mControllerCallbacks->pingResponseCallback(resp.timestamp());
    return STATUS_SUCCESS;
}

int Controller::handleByeByteRequest(ByeByeReason reason) {
    sendByeByeResponse();
    mControllerCallbacks->byeByeRequestCallback(reason);
    return STATUS_SUCCESS;
}

int Controller::handleByeByeResponse() {
    mControllerCallbacks->byeByeResponseCallback();
    return STATUS_SUCCESS;
}

int Controller::handleExitRequest() {
    mControllerCallbacks->exitRequestCallback();
    return STATUS_SUCCESS;
}

int Controller::handleReadResponse(const ReadResponse &resp) {
    string data = resp.data();
    mControllerCallbacks->readResponseCallback(data);
    return STATUS_SUCCESS;
}

int Controller::handleWriteResponse(const WriteResponse &resp) {
    mControllerCallbacks->writeResponseCallback(resp.ok());
    return STATUS_SUCCESS;
}

int Controller::handleForceLandscapeRequest(bool force) {
    mControllerCallbacks->forceLandscapeRequestCallback(force);
    return STATUS_SUCCESS;
}

int Controller::handleScreenOrientationInqure() {
    mControllerCallbacks->screenOrientationInquire();
    return STATUS_SUCCESS;
}

int Controller::handleScreenResolutionInquire() {
    mControllerCallbacks->screenResolutionInquire();
    return STATUS_SUCCESS;
}

int Controller::handleRunningStateInquire() {
    mControllerCallbacks->runningStateInquire();
    return STATUS_SUCCESS;
}

int Controller::handleTimeDateInquire() {
    mControllerCallbacks->timeDateInquire();
    return STATUS_SUCCESS;
}


void Controller::sendVersionResponse(uint16_t status) {
    LOG("[CONTROL] -> VersionResponse\n");
    size_t len = 4 * sizeof(uint16_t);
    auto *buf = new uint8_t[len];
    WRITE_BE16(buf, MESSAGE_VERSION_RESPONSE);
    WRITE_BE16(buf + 2, PROTOCOL_MAJOR_VERSION);
    WRITE_BE16(buf + 4, PROTOCOL_MINOR_VERSION);
    WRITE_BE16(buf + 6, status);
    queueOutgoingUnencrypted(buf, len);
    delete[] buf;
}

void Controller::sendAuthResponse(int status) {
    LOG("[CONTROL] -> AuthResponse\n");
    AuthResponse resp;
    resp.set_status(status);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_AUTH_COMPLETE, resp, &buf);
    queueOutgoingUnencrypted(buf.raw(), buf.size());
}

void Controller::sendServiceDiscoveryRequest() {
    LOG("[CONTROL] -> ServiceDiscoverRequest\n");
    ServiceDiscoveryRequest req;
    req.set_manufacturer(mManufacturer);
    req.set_model(mModel);
    req.set_version(mVersion);
    req.set_device_name(mPhoneName);
    req.set_screen_resolution_w(mScreenWidth);
    req.set_screen_resolution_h(mScreenHeight);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_SERVICE_DISCOVERY_REQUEST, req, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendPingRequest(int64_t timestamp, bool bugReport) {
    //LOG("[CONTROL] -> PingRequest\n");
    PingRequest req;
    req.set_timestamp(timestamp);
    req.set_bug_report(bugReport);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_PING_REQUEST, req, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendPingResponse(int64_t timestamp) {
    //LOG("[CONTROL] -> PingResponse\n");
    PingResponse resp;
    resp.set_timestamp(timestamp);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_PING_RESPONSE, resp, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendByeByeRequest(int32_t reason) {
    LOG("[CONTROL] -> ByebyeRequest\n");
    ByeByeRequest request;
    request.set_reason((ByeByeReason) reason);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_BYEBYE_REQUEST, request, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendByeByeResponse() {
    LOG("[CONTROL] -> ByebyeResponse\n");
    ByeByeResponse response;
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_BYEBYE_RESPONSE, response, &buf);
    queueOutgoing(buf.raw(), buf.size());
}


void Controller::sendExitResponse() {
    LOG("[CONTROL] ->ExitResponse\n");
    ExitResponse response;
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_EXIT_RESPONSE, response, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendNavFocusRequest(int32_t type) {
    LOG("[CONTROL] -> NavFocusRequest\n");
    /*NavFocusRequestNotification req;
    req.set_focus_type((NavFocusType)type);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_NAV_FOCUS_REQUEST, req, &buf);
    queueOutgoing(buf.raw(), buf.size());*/
}

void Controller::sendScreenOrientationNotifi(int orientation, int rotation) {
    LOG("[CONTROL] -> OrientationNotification\n");
    ScreenOrientationNotification ntf;
    ntf.set_orientation((ScreenOrientation) orientation);
    ntf.set_rotation((ScreenRotation) rotation);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_SCREEN_ORIENTATION_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendUpdateVehicleIdNotifi(string id) {
    LOG("[CONTROL] -> UpdateVehicleIdNotifi\n");
    UpdateVehicleIdNotification ntf;
    ntf.set_vehicle_id(id);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_UPDATE_VEHICLE_ID_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendRunningStateNotifi(int state) {
    RunningStateNotification ntf;
    ntf.set_state((RunningState) state);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_RUNNING_STATE_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendAutoRotationNotifi(bool isAutoed) {

}

void Controller::sendReadRequest(int address, int length) {
    ReadRequest rr;
    rr.set_address(address);
    rr.set_length(length);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_READ_REQUEST, rr, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendWriteRequest(string data, int address, int length) {
    WriteRequest wr;
    wr.set_data(data);
    wr.set_address(address);
    wr.set_length(length);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_WRITE_REQUEST, wr, &buf);
    queueOutgoing(buf.raw(), buf.size());

}

void Controller::sendScreenResolutionNotification(int width, int height, bool isRequired) {
    LOG("[CONTROL] -> sendScreenResolutionNotification\n");
    ScreenResolutionNotification ntf;
    ntf.set_width(width);
    ntf.set_height(height);
    ntf.set_unsolicited(isRequired);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_SCREEN_RESOLUTION_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendTimeDateNotification(int year, int month, int day, int hour, int minute,
                                          int second, int nanosecond, int week, int dayOfWeek) {
    LOG("[CONTROL] -> sendTimeDateNotification\n");
    TimedateNotification ntf;
    ntf.set_year(year);
    ntf.set_month(month);
    ntf.set_day(day);
    ntf.set_hour(hour);
    ntf.set_minute(minute);
    ntf.set_second(second);
    ntf.set_nsecond(nanosecond);
    ntf.set_week(week);
    ntf.set_day_of_week(dayOfWeek);
    IoBuffer buf;
    mRouter->marshallProto(MESSAGE_TIMEDATE_NOTIFICATION, ntf, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void Controller::sendForceLandscapeResponse(bool force) {

}


//chip check
const unsigned long address_8288[] = {0xbffe2f00, 0xbffe2f08, 0xbffe2f0c};
const unsigned long address_8268[] = {0x9c00af1c, 0x9c00af18, 0x00100C00};

list<const string> &get8288Chips() {
    static list<const string> chips_8288{"EW6800_AA", "EW6800_AB", "EW6800_I_AA",
                                         "8202H_DS_AA", "8202H_DS_AB", "8202H_DS_AC",
                                         "8202H_LT_AA", "8202H_LT_AB", "8268H_AA",
                                         "8268H_AB", "8268H_I_AA", "8288H_AA", "8288H_AB",
                                         "8288H_I_AA", "8288H_IL_AA"};
    return chips_8288;
}

list<const string> &get8268Chips() {
    static list<const string> chips_8268{"8368-U-X", "8368-U", "8368-UA", "8368-UC",
                                         "8368-UCL", "8368-UL", "DA-007", "8268K-X",
                                         "8268K", "8268K-A", "8268K-W", "8268K-AW",
                                         "8268K-CW", "8268K-S", "8268K-C", "8268-test"};
    return chips_8268;
}


const unsigned long *Controller::getReadAddresses(const string &chipName) {
    LOGE("->getReadAddresses\n");
    list<const string> chips_8268 = get8268Chips();
    list<const string> chips_8288 = get8288Chips();
    list<const string>::iterator iterator;
    iterator = find(chips_8288.begin(), chips_8288.end(), chipName);
    if (iterator != chips_8288.end()) {
        return chipCodes->at("8288");
    }

    iterator = find(chips_8268.begin(), chips_8268.end(), chipName);
    if (iterator != chips_8268.end()) {
        return chipCodes->at("8268");
    }

    return nullptr;
}

bool Controller::calculateSafety() {
    return false;
}


int Controller::checkName(const string &chipName) {

    LOGE("->checkName\n");
    list<const string> chips_8268 = get8268Chips();
    list<const string> chips_8288 = get8288Chips();
    list<const string>::iterator iterator;
    iterator = find(chips_8288.begin(), chips_8288.end(), chipName);
    if (iterator != chips_8288.end()) {
        return 1;
    }

    iterator = find(chips_8268.begin(), chips_8268.end(), chipName);
    if (iterator != chips_8268.end()) {
        return 2;
    }

    return 0;
}

void Controller::initChipList() {
    LOGE("->initChipList\n");
    chipCodes = new map<string, const unsigned long *>;
    chipCodes->insert(std::make_pair<string, const unsigned long *>("8288", address_8288));
    chipCodes->insert(std::make_pair<string, const unsigned long *>("8268", address_8268));
}

int Controller::checkProduct(int moduleId, int feature, int productId) {
    int mFeature = feature;
    int mProductId = productId;
    return module_product_remote_check(moduleId, &mFeature, &mProductId);
}

void Controller::checkProductResult(void *buf, int32_t size) {
    const auto *result_ = (const chk_result_t *) buf;
    CheckResult checkResult;
    for (int i = 0; i < MODULE_PRODUCT_ID_MAX; i++) {
        checkResult.moduleId = result_[i].module_id;
        checkResult.failCode = result_[i].fail_code;
        checkResult.value = result_[i].value;
        mControllerCallbacks->checkProductResultCallBack(checkResult);
    }
}

