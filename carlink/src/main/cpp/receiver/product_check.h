#ifndef __PRODUCT_CHECK_H__
#define __PRODUCT_CHECK_H__

#define CONFIG_CHECK_RESULT_CRYPTO 1
#define CONFIG_CHECK_RESULT_UPLOAD 0



/*
* product reg
*/
#define PRODUCT_CHECK_TEST
//#define PRODUCT_REG_USER_DEFINE
#define PRODUCT_CHECK_REMOTE_SUPPORT


#ifdef PRODUCT_CHECK_TEST
#ifndef PRODUCT_MEM_ANCHOR_ADDR
#define PRODUCT_MEM_ANCHOR_ADDR (0x00001000+0x20)
#endif
#ifndef FIRMWARE_ENTRY_ADDR
#define FIRMWARE_ENTRY_ADDR     (0x00001000+0x20)
#endif
#ifndef FIRMWARE_SIZE
#define FIRMWARE_SIZE           (0x200)
#endif

#endif


#ifndef PRODUCT_REG_USER_DEFINE

#define REG_BASE                         0x9c000000
#define RF_GRP(_grp, _reg)             ((((_grp) * 32 + (_reg)) * 4) + REG_BASE)

#define REG_VALUE(r)                    *((volatile unsigned int * )(r))
#define REG_ADDR(r)                     (volatile unsigned int * )(r)

#define PRODUCT_FEATURE_REG()           REG_ADDR(RF_GRP(350, 6))//0x9c00af18
#define PRODUCT_REG()                   REG_ADDR(RF_GRP(350, 7)) //0x9c00af1c

#define GET_PRODUCT_FEATURE_VALUE(r)    ((REG_VALUE(r) & 0xffff0000) >> 16)
#define PRODUCT_FEATURE_VALUE()         GET_PRODUCT_FEATURE_VALUE(PRODUCT_FEATURE_REG())

#define GET_PRODUCT_ID(r)               ((REG_VALUE(r) & 0x0000ff00) >> 8)
#define PRODUCT_ID()                    GET_PRODUCT_ID(PRODUCT_REG())

#else

#define REG_BASE                        0x00001000

#define REG_VALUE(r)                    *((volatile unsigned int * )(r))
#define REG_ADDR(r)                     (volatile unsigned int * )(r)

#define PRODUCT_FEATURE_REG()           REG_ADDR(REG_BASE)
#define PRODUCT_REG()                   REG_ADDR(REG_BASE+4)

#define GET_PRODUCT_FEATURE_VALUE(r)    ((REG_VALUE(r) & 0xffff0000) >> 16)
#define PRODUCT_FEATURE_VALUE()         GET_PRODUCT_FEATURE_VALUE(PRODUCT_FEATURE_REG())

#define GET_PRODUCT_ID(r)               ((REG_VALUE(r) & 0x0000ff00) >> 8)
#define PRODUCT_ID()                    GET_PRODUCT_ID(PRODUCT_REG())

#define SET_PRODUCT_ID(a)               *(PRODUCT_REG()) = ((a) << 8)
#define SET_PRODUCT_FEATURE(a)          *(PRODUCT_FEATURE_REG()) = ((a) << 16 )

#endif

/*
* module product id
*
**/
typedef enum {
    MODULE_PRODUCT_ID_SAMPLE = 0,
    MODULE_PRODUCT_ID_AUTOLINK = 1,
    MODULE_PRODUCT_ID_VAUTOLINK = 2,
    MODULE_PRODUCT_ID_CARPLAY = 3,
    MODULE_PRODUCT_ID_AIRPLAY = 4,
    MODULE_PRODUCT_ID_WFD = 5,
    MODULE_PRODUCT_ID_IAP2 = 6,
    MODULE_PRODUCT_ID_ANDROIDAUTO = 7,

    MODULE_PRODUCT_ID_CARLIFE = 10,

    MODULE_PRODUCT_ID_WIFI_AP = 20,
    MODULE_PRODUCT_ID_WIFI_P2P = 21,
    MODULE_PRODUCT_ID_WIFI_STA = 22,

    MODULE_PRODUCT_ID_MAX = 32,

} module_otp_id_t;


/*
*product check result ref define
*/

typedef enum {
    FAIL_CODE_SUCCESS = -1,
    FAIL_CODE_STAGE_1 = 1,
    FAIL_CODE_STAGE_2 = 2,
    FAIL_CODE_STAGE_3 = 3,

    FAIL_CODE_STAGE_4 = 4,
    FAIL_CODE_STAGE_5 = 5,
    FAIL_CODE_STAGE_6 = 6,
    FAIL_CODE_STAGE_7 = 7,
    FAIL_CODE_STAGE_8 = 8,
    FAIL_CODE_STAGE_9 = 9,
    FAIL_CODE_STAGE_10 = 10,
    FAIL_CODE_STAGE_11 = 11,
    FAIL_CODE_STAGE_12 = 12,
    FAIL_CODE_STAGE_13 = 13,
    FAIL_CODE_STAGE_14 = 14,
    FAIL_CODE_STAGE_15 = 15,
    FAIL_CODE_STAGE_16 = 16,
    FAIL_CODE_STAGE_17 = 17,

} fail_code_t;


/*
* module check result
*
**/

typedef struct {
    int module_id;

    fail_code_t fail_code;
    int value;
} chk_result_t;


/*
* product check apis
**/
typedef void (*module_chk_result_cb)(const void *result, int size);

typedef void (*module_get_chk_result)(module_chk_result_cb cb);


typedef module_get_chk_result (*module_product_check_entry)(int module_id, int feature_value,
                                                            int feature_reg, int product_id,
                                                            int product_reg, int mem_anchor);


module_get_chk_result
module_product_check(int module_id, int feature_value, int feature_reg, int product_id,
                     int product_reg, int mem_anchor);

int module_product_check_simple(int module_id);


#ifdef PRODUCT_CHECK_REMOTE_SUPPORT

int module_product_remote_check(int module_id, int *feature, int *product_id);

typedef void (*module_product_remote_check_result_cb)(const chk_result_t *result);

int module_product_remote_check_result(const void *result, int size,
                                       module_product_remote_check_result_cb cb);

#endif



/*
*  check param code for api call
**/
#define entry_check_param_pack(mem_achor) \
{\
        int arg_pid         = (int)PRODUCT_ID(); \
        int arg_pid_reg     = (int)PRODUCT_REG(); \
        int arg_mem         = (int)mem_achor; \
        int arg_feature     = (int)PRODUCT_FEATURE_VALUE(); \
        int arg_feature_reg = (int)PRODUCT_FEATURE_REG(); \
        argv[0] = (char*)arg_feature; \
        argv[1] = (char*)arg_feature_reg; \
        argv[2] = (char*)arg_pid; \
        argv[3] = (char*)arg_pid_reg; \
        argv[4] = (char*)arg_mem; \
        argc    = 5;\
}

/*
*  the check code for main entry
**/
#define entry_main_check(module_id, chk_cb) \
    { \
        int arg_feature     = ((int)argv[0]); \
        int arg_feature_reg = ((int)argv[1]); \
        int arg_pid         = ((int)argv[2]); \
        int arg_pid_reg     = ((int)argv[3]); \
        int arg_mem         = ((int)argv[4]); \
        chk_cb = module_product_check(module_id, \
            arg_feature,arg_feature_reg,arg_pid,arg_pid_reg,arg_mem); \
    }


#endif

