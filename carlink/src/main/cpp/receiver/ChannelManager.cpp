// Copyright 2014 Google Inc. All Rights Reserved.

#include "util/common.h"
#include "ChannelManager.h"
#include "MessageRouter.h"

bool ChannelManager::init(MessageRouter *messageRouter) {
    mMessageRouter = messageRouter;
    mHealthy = true;
#ifdef SUPPORT_SSL
    mSslWrapper = NULL;
#endif

    for (auto &mChannel : mChannels) {
        mChannel = nullptr;
    }
    mLastChannel = MAX_CHANNELS - 1;  // Start from zero the first time.
    return true;
}

void ChannelManager::prepareShutdown() {
    mHealthy = false;
    mDataAvailable.up();
}

void ChannelManager::shutdown() {
    mMessageRouter = nullptr;
    Autolock l(&mMutex);
    for (auto &mChannel : mChannels) {
        mChannel = nullptr;
    }
}

void ChannelManager::sendFramingError() {
    mHealthy = false;
    uint16_t buf;
    LOGE("Sending Framing Error notification!");
    WRITE_BE16(&buf, MESSAGE_FRAMING_ERROR);
    queueOutgoing(0, false, &buf, sizeof(uint16_t), false);
    mMessageRouter->unrecoverableError(STATUS_FRAMING_ERROR);
}

int ChannelManager::allocChannel(uint8_t serviceId, int8_t priority) {
    Autolock l(&mMutex);
    int i;
    for (i = 0; i < MAX_CHANNELS; i++) {
        if (mChannels[i] == nullptr) {
            break;
        } else if (mChannels[i].get()->id() == serviceId) {
            break;
        }
    }
    if (i < MAX_CHANNELS) {
        shared_ptr<Channel> channel(new Channel(i, priority));
        mChannels[i] = channel;
    }
    return i;
}

int ChannelManager::freeChannel(uint8_t channelId) {
    Autolock l(&mMutex);
    //LOG("free channel:%d\n", channelId);
    if (mChannels[channelId] == nullptr) {
        return STATUS_INVALID_CHANNEL;
    }
    // This might look weird but assigning NULL will cause the original reference to be dropped
    // potentially releasing the memory.
    mChannels[channelId] = nullptr;
    return STATUS_SUCCESS;
}

void ChannelManager::messageToFrames(int channelId, bool control, void *message,
                                     size_t len, const shared_ptr<Channel> &channel,
                                     bool encrypted) {
    size_t remaining = len;
    int fraglen;
    const size_t maxSize = getMaxFragmentPayloadSize();
    shared_ptr<IoBuffer> msg(new IoBuffer(len));
    char *payload = (char *) msg->raw();
    memcpy(msg->raw(), message, len);

    FragInfo fraginfo;
    if (len > maxSize) {
        fraginfo = FRAG_FIRST;
        fraglen = maxSize;
    } else {
        fraginfo = FRAG_UNFRAGMENTED;
        fraglen = len;
    }

    while (remaining > 0) {
        shared_ptr<Frame> frame(new Frame());
        SET_FRAGINFO(frame->bitField, fraginfo);
        SET_ENCRYPTED(frame->bitField, encrypted);
        frame->channelId = channelId;
        frame->payload = payload;
        frame->original = msg;
        frame->frameLength = fraglen;
        frame->messageLength = 0;
        if (control) {
            SET_CHANNEL_CONTROL(frame->bitField);
        }
        if (fraginfo == FRAG_FIRST) {
            frame->messageLength = len;
        }

        payload += fraglen;
        remaining -= fraglen;

        if (remaining > maxSize) {
            fraginfo = FRAG_CONTINUATION;
            fraglen = maxSize;
        } else {
            fraginfo = FRAG_LAST;
            fraglen = remaining;
        }

        channel->enqueueOutgoing(frame);
        mDataAvailable.up();
    }
}

shared_ptr<IoBuffer> ChannelManager::framesToMessage(const shared_ptr<Channel> &channel) {
    shared_ptr<Frame> frame = channel->dequeueIncoming();

    // Single fragment zero copy optimization. A large fraction of our data is
    // single fragment so we can optimize the common case by avoiding a copy.
    if (FRAG_BITS(frame->bitField) == FRAG_UNFRAGMENTED) {
        shared_ptr<IoBuffer> msg = frame->original;
        auto offset = (uintptr_t) frame->payload;
        offset -= (uintptr_t) frame->original->raw();
        // Raw is already offset, so also add in the inbuilt offset.
        offset += frame->original->startOffset();
        msg->setStartOffset(offset);
        msg->setEndOffset(offset + frame->frameLength);
        return msg;
    }

    // In the multi-fragment case, we can't trivially do zero copy. One option would
    // be to pre-allocate the whole message when we get the whole frame but it gets
    // messy. There's also the posssiblilty of mucking around with page tables to
    // remap pages into contiguous ranges but that might not port well.
    shared_ptr<IoBuffer> msg(new IoBuffer(frame->messageLength));
    char *msgptr = (char *) msg->raw();

    while (true) {
        memcpy(msgptr, frame->payload, frame->frameLength);
        msgptr += frame->frameLength;
        if (!channel->hasIncoming()) {
            break;
        }

        frame = channel->dequeueIncoming();
    }

    return msg;
}

int ChannelManager::queueOutgoing(int channelId, bool control, void *message,
                                  size_t len, bool encrypted) {
    shared_ptr<Channel> channel;
    {
        Autolock l(&mMutex);
        channel = mChannels[channelId];
        if (channel == nullptr) {
            return STATUS_INVALID_CHANNEL;
        }
    }
    messageToFrames(channelId, control, message, len, channel, encrypted);
    return STATUS_SUCCESS;
}

int ChannelManager::queueOutgoing(int channelId, bool control, void *message, size_t len) {
    return queueOutgoing(channelId, control, message, len, true); // encrypted.
}

int ChannelManager::queueOutgoingUnencrypted(int channelId, bool control,
                                             void *message, size_t len) {
    return queueOutgoing(channelId, control, message, len, false);
}

int ChannelManager::handleChannelControl(const shared_ptr<Frame> &frame,
                                         const shared_ptr<IoBuffer> &buf) {
    void *rmsg = (unsigned char *) buf->raw() + FRAME_HEADER_MIN_LENGTH;

    if (FRAG_BITS(frame->bitField) != FRAG_UNFRAGMENTED) {
        return STATUS_FRAMING_ERROR;
    }

    int ret = mMessageRouter->routeChannelControlMsg(frame, rmsg, frame->frameLength);
    if (ret == STATUS_FRAMING_ERROR) {
        sendFramingError();
    }
    return ret;
}

int ChannelManager::checkFragment(const shared_ptr<Frame> &frame, shared_ptr<Channel> *ch,
                                  bool *valid, bool *complete) {
    shared_ptr<Channel> channel;
    {
        Autolock l(&mMutex);
        channel = mChannels[frame->channelId];
        if (channel == nullptr) {
            *valid = false;
            return STATUS_FRAMING_ERROR;
        }
    }

    FragInfo curFragInfo = FRAG_BITS(frame->bitField);
    if (channel->hasIncoming()) {
        shared_ptr<Frame> lastFrame = channel->peekLastIncoming();
        FragInfo lastFragInfo = FRAG_BITS(lastFrame->bitField);
        *valid = (lastFragInfo == FRAG_FIRST || lastFragInfo == FRAG_CONTINUATION) &&
                 (curFragInfo == FRAG_CONTINUATION || curFragInfo == FRAG_LAST);
        *complete = (lastFragInfo == FRAG_FIRST || lastFragInfo == FRAG_CONTINUATION) &&
                    curFragInfo == FRAG_LAST;
    } else {
        *valid = (curFragInfo == FRAG_UNFRAGMENTED || curFragInfo == FRAG_FIRST);
        *complete = (curFragInfo == FRAG_UNFRAGMENTED);
    }

    if (!(IS_ENCRYPTED(frame->bitField) || frame->channelId == 0)) {
        *valid = false;
        return STATUS_AUTHENTICATION_FAILURE;
    }

    *ch = channel;
    return STATUS_SUCCESS;
}

int ChannelManager::queueIncoming(const shared_ptr<IoBuffer> &buf) {
    if (unlikely(!mHealthy)) {
        sendFramingError();
        return STATUS_FRAMING_ERROR;
    }

    shared_ptr<Frame> frame(new Frame());
    if (decodeFrame(frame, buf) == STATUS_FRAMING_ERROR) {
        sendFramingError();
        return STATUS_FRAMING_ERROR;
    }

    /* Handle channel control messages. */
    if (IS_CHANNEL_CONTROL(frame->bitField)) {
        return handleChannelControl(frame, buf);
    }

    bool complete = false;
    bool valid = false;
    shared_ptr<Channel> channel;
    checkFragment(frame, &channel, &valid, &complete);

    if (!valid) {
        //sendFramingError();
        return STATUS_FRAMING_ERROR;
    }
    channel->enqueueIncoming(frame);

    if (complete) {
        shared_ptr<IoBuffer> msg = framesToMessage(channel);
        mMessageRouter->routeMessage(channel->id(), msg);
    }
    return STATUS_SUCCESS;
}

bool ChannelManager::extractEncodedFrame(IoBuffer *encoded) {
    shared_ptr<Channel> channel;
    {
        Autolock l(&mMutex);
        for (int i = 0; i < MAX_CHANNELS; i++) {
            // Start looking from the next channel.
            int candidate = (mLastChannel + i + 1) % MAX_CHANNELS;
            if (mChannels[candidate] != nullptr && mChannels[candidate]->hasOutgoing()) {
                channel = mChannels[candidate];
                mLastChannel = candidate;
                if (mMessageRouter->isChannelClosed(candidate)) {
                    mChannels[candidate] = nullptr;
                }
                break;
            }
        }
    }

    if (channel == nullptr) {
        return false;
    }

    shared_ptr<Frame> frame = channel->dequeueOutgoing();
    encodeFrame(frame, encoded);
    return true;
}

bool ChannelManager::getEncodedFrame(IoBuffer *encoded) {
    if (unlikely(!mHealthy)) {
        return false;
    }
    if (!mDataAvailable.down()) {
        return false;
    }
    // prepareShutdown() may have been called when we were waiting on the semaphore so check again.
    if (unlikely(!mHealthy)) {
        return false;
    }
    return extractEncodedFrame(encoded);
}

void ChannelManager::encodeFrame(const shared_ptr<Frame> &frame, IoBuffer *encoded) {
    size_t len = FRAME_HEADER_MIN_LENGTH;
    if (frame->messageLength) {
        len += sizeof(uint32_t);
    }

    // The payload size might grow if encryption is enabled because of padding.
    int payloadLength = frame->frameLength;
#ifdef SUPPORT_SSL
    bool wasEncrypted = false;
    if (likely(mSslWrapper != NULL && IS_ENCRYPTED(frame->bitField)))  {
        payloadLength = mSslWrapper->encryptionPipelineEnqueue(frame->payload,
                frame->frameLength);
        wasEncrypted = true;
        if (payloadLength < 0) {
            mMessageRouter->unrecoverableError(STATUS_FRAMING_ERROR);
            return;
        }
    }
#endif

    len += payloadLength;

    encoded->resize(len);

    char *frameptr = (char *) encoded->raw();
    *frameptr++ = frame->channelId;
    *frameptr++ = frame->bitField;

    WRITE_BE16(frameptr, payloadLength);
    frameptr += sizeof(uint16_t);

    if (frame->messageLength) {
        WRITE_BE32(frameptr, frame->messageLength);
        frameptr += sizeof(uint32_t);
    }
#ifdef SUPPORT_SSL
    if (likely(wasEncrypted)) {
        int ret = mSslWrapper->encryptionPipelineDequeue(frameptr, payloadLength);
        if (ret < 0) {
            mMessageRouter->unrecoverableError(STATUS_FRAMING_ERROR);
            return;
        }
    } else {
        memcpy(frameptr, frame->payload, payloadLength);
    }
#else
    memcpy(frameptr, frame->payload, payloadLength);
#endif
}

size_t ChannelManager::getAdditionalBytesToRead(const uint8_t buf[FRAME_HEADER_MIN_LENGTH]) {
    size_t len = buf[FRAME_LEN_START_OFFSET] << 8;
    len |= buf[FRAME_LEN_START_OFFSET + 1];

    if (FRAG_BITS(buf[BIT_FIELD_OFFSET]) == FRAG_FIRST) {
        len += sizeof(uint32_t);
    }

    return len;
}

int ChannelManager::decodeFrame(const shared_ptr<Frame> &frame,
                                const shared_ptr<IoBuffer> &encoded) {
    unsigned char *frameptr = (unsigned char *) encoded->raw();
    size_t len = encoded->size();

    frame->channelId = *frameptr++;
    frame->bitField = *frameptr++;

    READ_BE16(frameptr, frame->frameLength);
    frameptr += sizeof(uint16_t);

    if (FRAG_BITS(frame->bitField) == FRAG_FIRST) {
        READ_BE32(frameptr, frame->messageLength);
        frameptr += sizeof(uint32_t);
    } else {
        frame->messageLength = 0;
    }

    /* Check for potential framing errors. */
    if (unlikely(((uint64_t) encoded->raw() + len) != ((uint64_t) frameptr + frame->frameLength))) {
        LOGE("Framing error: len=%lu framelen=%d", (unsigned long) len, frame->frameLength);
        return STATUS_FRAMING_ERROR;
    }

    frame->payload = frameptr;
    frame->original = encoded;

    // Decrypt in place if SSL is enabled. Since compression is disabled,
    // decryption will always shrink the payload if anything so its alright to
    // do it in place.
#ifdef SUPOORT_SSL
    if (mSslWrapper != NULL && IS_ENCRYPTED(frame->bitField)) {
        if (!mSslWrapper->decryptionPipelineEnqueue(frame->payload, frame->frameLength)) {
            // Failed to decrypt - Abort the connection.
            LOGE("Failed to enqueue into decryption pipeline!");
            return STATUS_FRAMING_ERROR;
        }

        frame->frameLength = 0;
        uint8_t* ptr = (uint8_t*)frame->payload;
        // |len| contains the length of the input frame and thus the amount of
        // data that we can safely write to |ptr|.
        while (len > 0) {
            int chunkLen = mSslWrapper->decryptionPipelineDequeue(ptr, len);
            if (chunkLen < 0) {
                LOGE("Failed to dequeue from decryption pipeline!");
                return STATUS_FRAMING_ERROR;
            } else if (chunkLen == 0) {
                break;
            }
            frame->frameLength += chunkLen;
            ptr += chunkLen;
            len -= chunkLen;
        }
        if (len == 0) {
            LOGE("Somehow, the decrypted data is larger than the ciphertext!");
            return STATUS_FRAMING_ERROR;
        }
    }
#endif

    return STATUS_SUCCESS;
}
