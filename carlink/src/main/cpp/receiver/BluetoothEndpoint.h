// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROJECTION_PROTOCOL_BLUETOOTH_ENDPOINT_H
#define AUTOLINK_PROJECTION_PROTOCOL_BLUETOOTH_ENDPOINT_H

#include "util/common.h"
#include "IBluetoothCallbacks.h"
#include "ProtocolEndpointBase.h"

#include <string>

class BluetoothEndpoint : public ProtocolEndpointBase {
public:
    BluetoothEndpoint(uint8_t id, MessageRouter *router)
            : ProtocolEndpointBase(id, router, false) {}

    virtual void onChannelOpened(uint8_t channelId, uint32_t extraMessage);

    void registerCallbacks(const shared_ptr<IBluetoothCallbacks> &callbacks);

    void sendPairingResquest(string address, uint32_t pairMethod);

    bool discoverService(const Service &srv);

    int routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg);

    void sendBluetoothStatus(int32_t status, bool unsolicited);

private:
    void handlePairingResponse(const BluetoothPairingResponse &response);

    void handleAuthenticationData(const BluetoothAuthenticationData &auth);

    shared_ptr<IBluetoothCallbacks> mCallbacks;

    void handleBluetoothStatusInquire();
};

#endif // AUTOLINK_PROJECTION_PROTOCOL_BLUETOOTH_ENDPOINT_H
