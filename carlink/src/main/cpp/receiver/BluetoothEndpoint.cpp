// Copyright 2014 Google Inc. All Rights Reserved.

#include "util/common.h"
#include "BluetoothEndpoint.h"


void BluetoothEndpoint::registerCallbacks(
        const shared_ptr<IBluetoothCallbacks> &callbacks) {
    mCallbacks = callbacks;
}

void BluetoothEndpoint::onChannelOpened(uint8_t channelId, uint32_t extraMessage) {
    ProtocolEndpointBase::onChannelOpened(channelId, extraMessage);
    mCallbacks->onChannelOpened();
}

bool BluetoothEndpoint::discoverService(const Service &srv) {
    bool ret = false;
    if (srv.has_bluetooth_service()) {
        const BluetoothService *msrv = &srv.bluetooth_service();
        const string &carAddress = msrv->car_address();
        uint32_t methodsBitmap = 0;
        for (int i = 0; i < msrv->supported_pairing_methods_size(); i++) {
            methodsBitmap |= (1u << (msrv->supported_pairing_methods(i)));
        }
        ret = mCallbacks->discoverBluetoothService(carAddress, methodsBitmap);
    }
    return ret;
}


int BluetoothEndpoint::routeMessage(
        uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg) {
    int ret = STATUS_UNEXPECTED_MESSAGE;
    uint8_t *ptr = (uint8_t *) msg->raw() + sizeof(uint16_t);
    size_t len = msg->size() - sizeof(uint16_t);

    switch (type) {
        case BLUETOOTH_MESSAGE_PAIRING_RESPONSE: {
            BluetoothPairingResponse respose;
            if (PARSE_PROTO(respose, ptr, len)) {
                handlePairingResponse(respose);
                return STATUS_SUCCESS;
            }
            break;
        }
        case BLUETOOTH_MESSAGE_AUTHENTICATION_DATA: {
            BluetoothAuthenticationData auth;
            if (PARSE_PROTO(auth, ptr, len)) {
                handleAuthenticationData(auth);
                return STATUS_SUCCESS;
            }
            break;
        }
        case BLUETOOTH_MESSAGE_PHONE_STATUS_INQUIRE: {
            handleBluetoothStatusInquire();
            break;
        }
        default:
            break;
    }
    return ret;
}

void BluetoothEndpoint::handlePairingResponse(
        const BluetoothPairingResponse &response) {
    LOG("[BluetoothEndpoint] <- PairingResponse\n");
    int32_t status = response.status();
    bool alreadyPaired = response.already_paired();
    mCallbacks->onPairingResponse(status, alreadyPaired);
}

void BluetoothEndpoint::handleAuthenticationData(
        const BluetoothAuthenticationData &auth) {
    const string &authData = auth.auth_data();
    mCallbacks->onAuthenticationData(authData);
}

void BluetoothEndpoint::sendPairingResquest(string address, uint32_t pairMethod) {
    LOG("[BluetoothEndpoint] -> PairingResquest\n");
    BluetoothPairingRequest req;
    req.set_phone_address(address);
    req.set_pairing_method(static_cast<BluetoothPairingMethod>(pairMethod));
    IoBuffer buf;
    mRouter->marshallProto(BLUETOOTH_MESSAGE_PAIRING_REQUEST, req, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void BluetoothEndpoint::sendBluetoothStatus(int32_t status, bool unsolicited) {
    BluetoothPhoneStatusNotification req;
    req.set_status(status);
    req.set_unsolicited(unsolicited);
    IoBuffer buf;
    mRouter->marshallProto(BLUETOOTH_MESSAGE_PHONE_STATUS_NOTIFICATION, req, &buf);
    queueOutgoing(buf.raw(), buf.size());
}

void BluetoothEndpoint::handleBluetoothStatusInquire() {
    mCallbacks->onPhoneBluetoothStatusInquire();
}


