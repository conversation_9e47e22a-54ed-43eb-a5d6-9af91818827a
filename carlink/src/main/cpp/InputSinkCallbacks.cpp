// Copyright 2014 Google Inc. All Rights Reserved.

#include "InputSinkCallbacks.h"

int InputSinkCallbacks::onChannelOpened() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(mJthis, mChannelOpenCallbackId);
}

bool InputSinkCallbacks::discoverInputService(bool hasTouchscreen,
                                              bool hasTouchpad, bool hasKey) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallBooleanMethod(mJthis, mDiscoverInputSinkCallbackId, hasTouchscreen,
                           hasTouchpad, hasKey);
    return true;
}

void InputSinkCallbacks::onTouchEvent(uint64_t timestamp, int32_t numPointers,
                                      const uint32_t *pointerIds, const uint32_t *x,
                                      const uint32_t *y,
                                      int action, int actionIndex) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    jintArray arrayIds = env->NewIntArray(numPointers);
    env->SetIntArrayRegion(arrayIds, 0, numPointers, (jint *) pointerIds);
    jintArray arrayX = env->NewIntArray(numPointers);
    env->SetIntArrayRegion(arrayX, 0, numPointers, (jint *) x);
    jintArray arrayY = env->NewIntArray(numPointers);
    env->SetIntArrayRegion(arrayY, 0, numPointers, (jint *) y);
    env->CallVoidMethod(mJthis, mOnTouchEventId, timestamp, numPointers,
                        arrayIds, arrayX, arrayY, action, actionIndex);
    env->DeleteLocalRef(arrayIds);
    env->DeleteLocalRef(arrayX);
    env->DeleteLocalRef(arrayY);

}

void InputSinkCallbacks::onTouchPadEvent(uint64_t timestamp,
                                         int32_t numPointers, const uint32_t *pointerIds,
                                         const uint32_t *x,
                                         const uint32_t *y, int action, int actionIndex) {

    JNIEnv *env = JniUtil::getEnv(mVm);
    jintArray arrayIds = env->NewIntArray(numPointers);
    env->SetIntArrayRegion(arrayIds, 0, numPointers, (jint *) pointerIds);
    jintArray arrayX = env->NewIntArray(numPointers);
    env->SetIntArrayRegion(arrayX, 0, numPointers, (jint *) x);
    jintArray arrayY = env->NewIntArray(numPointers);
    env->SetIntArrayRegion(arrayY, 0, numPointers, (jint *) y);
    env->CallVoidMethod(mJthis, mOnTouchPadEventId, timestamp, numPointers,
                        arrayIds, arrayX, arrayY, action, actionIndex);
    env->DeleteLocalRef(arrayIds);
    env->DeleteLocalRef(arrayX);
    env->DeleteLocalRef(arrayY);

}

void InputSinkCallbacks::onKeyEvent(uint64_t timestamp, int32_t keycode,
                                    bool isDown, bool longPress, uint32_t metaState) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallBooleanMethod(mJthis, mOnKeyEventId, timestamp, keycode, isDown,
                           longPress, metaState);

}

void InputSinkCallbacks::onAbsoluteEvent(uint64_t timestamp, int32_t keycode,
                                         int32_t value) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallBooleanMethod(mJthis, mOnAbsoluteEventId, timestamp, keycode,
                           value);
}

void InputSinkCallbacks::onRelativeEvent(uint64_t timestamp, int32_t keycode,
                                         int32_t delta) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallBooleanMethod(mJthis, mOnRelativeEventId, timestamp, keycode,
                           delta);
}
