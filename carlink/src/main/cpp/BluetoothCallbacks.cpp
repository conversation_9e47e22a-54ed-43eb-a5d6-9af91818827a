// Copyright 2014 Google Inc. All Rights Reserved.

#include "BluetoothCallbacks.h"


int BluetoothCallbacks::onChannelOpened() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    return env->CallIntMethod(mJthis, mChannelOpenCallbackId);
}

bool BluetoothCallbacks::discoverBluetoothService(string carAddress, uint32_t methodsBitmap) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    jstring javaPhoneAddress = env->NewStringUTF(carAddress.c_str());
    env->CallBooleanMethod(mJthis, mDiscoverBtCallbackId, javaPhoneAddress, methodsBitmap);
    env->DeleteLocalRef(javaPhoneAddress);
    return true;
}

void BluetoothCallbacks::onPairingResponse(int32_t status, bool alreadyPaired) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mPairingResponseCallbackId, status, alreadyPaired);
}

void BluetoothCallbacks::onAuthenticationData(string authData) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    jstring javaAuthData = env->NewStringUTF(authData.c_str());
    env->CallVoidMethod(mJthis, mAuthenticationCallbackId, javaAuthData);
    env->DeleteLocalRef(javaAuthData);
}

void BluetoothCallbacks::onPhoneBluetoothStatusInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mPhoneBluetoothStatusInquireId);
}
