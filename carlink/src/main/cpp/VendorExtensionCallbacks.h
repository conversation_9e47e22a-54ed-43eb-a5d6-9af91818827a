#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_VENDOR_CALLBACKS_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_VENDOR_CALLBACKS_H

#include "IVendorExtensionCallbacks.h"
#include "NativeCallbackWrapper.h"
#include "util/common.h"

class VendorExtensionCallbacks : public NativeCallbackWrapper, public IVendorExtensionCallbacks {
public:
    VendorExtensionCallbacks(JNIEnv *env, jobject jthis) : NativeCallbackWrapper(env, jthis) {
        mDataAvailableId = env->GetMethodID(
                mThisClass, "dataAvailable", "([BI)I");

    }

    virtual ~VendorExtensionCallbacks() {}

    int dataAvailable(uint8_t *data, uint32_t len);

private:
    jmethodID mDataAvailableId;
};

#endif // ANDROID_AUTO_PROJECTION_PROTOCOL_VIDEO_SINK_CALLBACKS_H
