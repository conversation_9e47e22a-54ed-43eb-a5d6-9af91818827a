// Copyright 2014 Google Inc. All Rights Reserved.

#include <jni.h>
#include "GalReceiver.h"
#include "InputSink.h"
#include "util/shared_ptr.h"
#include "InputSinkCallbacks.h"
#include <android/log.h>

static jclass inputSinkClass;
static jfieldID fidNativeInputSink;
static jfieldID callbackID;
static jobject inpuSinkCallbacks;

InputSink *getInputSink(JNIEnv *env, jobject jthis) {
    jlong mNativeInputSink = env->GetLongField(jthis, fidNativeInputSink);
    return (InputSink *) mNativeInputSink;
}

void setInputSink(JNIEnv *env, jobject jthis, InputSink *inputSink) {
    env->SetLongField(jthis, fidNativeInputSink, (jlong) inputSink);
}

extern "C" JNIEXPORT jint JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_InputSink_nativeInit(
        JNIEnv *env, jobject jthis, jint id, jlong nativeGalReceiver) {
    inputSinkClass = env->GetObjectClass(jthis);
    fidNativeInputSink = env->GetFieldID(inputSinkClass, "mNativeInputSink", "J");
    callbackID = env->GetFieldID(inputSinkClass, "mInputListener",
                                 "Lcom/car/autolink/module/protocal/eightthree/project/InputSinkCallbacks;");
    inpuSinkCallbacks = env->GetObjectField(jthis, callbackID);
    InputSink *inputSink = getInputSink(env, jthis);
    if (inputSink != nullptr) {
        jclass eclass = env->FindClass("java/lang/IllegalStateException");
        env->ThrowNew(eclass, "Already initialized.");
        return -1;
    }

    auto *galReceiver = (GalReceiver *) nativeGalReceiver;
    inputSink = new InputSink(id, galReceiver->messageRouter());
    shared_ptr<IInputSinkCallbacks> callbacks(new InputSinkCallbacks(env, inpuSinkCallbacks));
    inputSink->registerCallbacks(callbacks);
    inputSink->setTouchScreenSupport(true);
    setInputSink(env, jthis, inputSink);
    return STATUS_SUCCESS;
}

extern "C" JNIEXPORT void JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_InputSink_nativeShutdown(
        JNIEnv *env, jobject jthis) {
    InputSink *inputSink = getInputSink(env, jthis);
    if (inputSink != nullptr) {
        inputSink->forceCloseChannel();
        delete inputSink;
        setInputSink(env, jthis, nullptr);
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_InputSink_sendStart(
        JNIEnv *env, jobject jthis) {

}
