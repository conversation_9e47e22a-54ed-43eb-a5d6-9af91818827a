// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_VIDEO_SINK_CALLBACKS_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_VIDEO_SINK_CALLBACKS_H

#include "IVideoSourceCallbacks.h"
#include "NativeCallbackWrapper.h"
#include "util/common.h"

class VideoSourceCallbacks : public NativeCallbackWrapper, public IVideoSourceCallbacks {
public:
    VideoSourceCallbacks(JNIEnv *env, jobject jthis) : NativeCallbackWrapper(env, jthis) {
        mChannelOpendCallbackId = env->GetMethodID(
                mThisClass, "onChannelOpened", "()I");
        mConfigCallbackId = env->GetMethodID(mThisClass, "configCallback", "(II[II)I");
        mAckallbackId = env->GetMethodID(mThisClass, "ackCallback", "(II)I");
        mVideoFoucsNotifCallbackId = env->GetMethodID(mThisClass, "videoFocusNotifCallback",
                                                      "(IZ)I");
        mDisplayChangeCallbackId = env->GetMethodID(mThisClass, "displayChangeCallback", "(IIZI)I");
        mDiscoverVideoConfigCallbackId = env->GetMethodID(mThisClass, "discoverVideoConfigCallback",
                                                          "(IIII)Z");
        mStartResponseCallbackCallbackId = env->GetMethodID(mThisClass, "startResponseCallback",
                                                            "(Z)I");
        mStopResponseCallbackCallbackId = env->GetMethodID(mThisClass, "stopResponseCallback",
                                                           "()I");
    }

    virtual ~VideoSourceCallbacks() {}

    int onChannelOpened();

    int configCallback(int32_t status, uint32_t maxUnack, uint32_t *prefer, uint32_t size);

    int ackCallback(int32_t sessionId, uint32_t numFrames);

    int videoFocusNotifCallback(int32_t focus, bool unsolicited);

    int displayChangeCallback(int32_t width, int32_t height, bool isLandscape, int32_t density);

    bool discoverVideoConfigCallback(int32_t codec, int32_t fps, int32_t w, int32_t h);

    int startResponseCallback(bool isOk);

    int stopResponseCallback();

private:
    jmethodID mChannelOpendCallbackId;
    jmethodID mConfigCallbackId;
    jmethodID mAckallbackId;
    jmethodID mVideoFoucsNotifCallbackId;
    jmethodID mDisplayChangeCallbackId;
    jmethodID mDiscoverVideoConfigCallbackId;
    jmethodID mStartResponseCallbackCallbackId;
    jmethodID mStopResponseCallbackCallbackId;
};

#endif

