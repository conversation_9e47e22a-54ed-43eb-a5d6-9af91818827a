//#include "com_car_autolink_module_protocal_eightthree_project_BluetoothEndpoint.h"
#include <jni.h>
#include "GalReceiver.h"
#include "BluetoothEndpoint.h"
#include "util/shared_ptr.h"
#include "BluetoothCallbacks.h"

static jclass bluetoothClass;
static jfieldID fidNativeBluetoothEndpoint;
static jfieldID callbackID;
static jobject bluetoothcallbacks;

BluetoothEndpoint *getBluetoothEndpoint(JNIEnv *env, jobject jthis) {
    jlong mNativeBluetoothEndpoint = env->GetLongField(jthis, fidNativeBluetoothEndpoint);
    return (BluetoothEndpoint *) mNativeBluetoothEndpoint;
}

void setBluetoothEndpoint(JNIEnv *env, jobject jthis, BluetoothEndpoint *receiver) {
    env->SetLongField(jthis, fidNativeBluetoothEndpoint, (jlong) receiver);
}

extern "C" JNIEXPORT jint JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_BluetoothEndpoint_nativeInit(JNIEnv *env,
                                                                                      jobject jthis,
                                                                                      jint id,
                                                                                      jlong nativeGalReceiver) {
    bluetoothClass = env->GetObjectClass(jthis);
    fidNativeBluetoothEndpoint =
            env->GetFieldID(bluetoothClass, "mNativeBluetoothEndpoint", "J");
    callbackID = env->GetFieldID(bluetoothClass, "mListener",
                                 "Lcom/car/autolink/module/protocal/eightthree/project/BluetoothCallbacks;");
    bluetoothcallbacks = env->GetObjectField(jthis, callbackID);
    BluetoothEndpoint *bluetooth = getBluetoothEndpoint(env, jthis);
    if (bluetooth != nullptr) {
        jclass eclass = env->FindClass("java/lang/IllegalStateException");
        env->ThrowNew(eclass, "Already initialized.");
        return -1;  // This return value is ignored.
    }

    auto *galReceiver = (GalReceiver *) nativeGalReceiver;
    bluetooth = new BluetoothEndpoint(static_cast<uint8_t>(id), galReceiver->messageRouter());
    setBluetoothEndpoint(env, jthis, bluetooth);

    shared_ptr<IBluetoothCallbacks> callbacks(new BluetoothCallbacks(env, bluetoothcallbacks));
    bluetooth->registerCallbacks(callbacks);

    return STATUS_SUCCESS;
}

extern "C" JNIEXPORT void JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_BluetoothEndpoint_nativeShutdown
        (JNIEnv *env, jobject jthis) {
    BluetoothEndpoint *bluetooth = getBluetoothEndpoint(env, jthis);
    if (bluetooth != nullptr) {
        bluetooth->forceCloseChannel();
        delete bluetooth;
        setBluetoothEndpoint(env, jthis, nullptr);
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_BluetoothEndpoint_nativeSendPairingRequest
        (JNIEnv *env, jobject jthis, jstring address, jint method) {
    BluetoothEndpoint *bluetooth = getBluetoothEndpoint(env, jthis);
    if (bluetooth != nullptr && address != nullptr) {
        const char *caddress = env->GetStringUTFChars(address, nullptr);
        string saddress(caddress);
        bluetooth->sendPairingResquest(saddress, static_cast<uint32_t>(method));
        env->ReleaseStringUTFChars(address, caddress);
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_car_autolink_module_protocal_eightthree_project_BluetoothEndpoint_nativeSendBluetoothStatus
        (JNIEnv *env, jobject jthis, jint status, jboolean unsolicited) {
    BluetoothEndpoint *bluetooth = getBluetoothEndpoint(env, jthis);
    if (bluetooth != nullptr) {
        bluetooth->sendBluetoothStatus(status, unsolicited);
    }

}
