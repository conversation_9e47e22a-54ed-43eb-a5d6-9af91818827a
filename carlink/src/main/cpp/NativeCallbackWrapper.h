// Copyright 2014 Google Inc. All Rights Reserved.
#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_AUDIO_SINK_NATIVECALLBACKWRAPPER_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_AUDIO_SINK_NATIVECALLBACKWRAPPER_H

#include <jni.h>
#include "JniUtil.h"
#include "JniUtil.h"

class NativeCallbackWrapper {
public:
    NativeCallbackWrapper(JNIEnv *env, jobject jthis) {
        env->GetJavaVM(&mVm);
        jclass thisClass = env->GetObjectClass(jthis);
        mThisClass = (jclass) env->NewGlobalRef(thisClass);
        mJthis = env->NewGlobalRef(jthis);
    }

    virtual ~NativeCallbackWrapper() {
        JniUtil::getEnv(mVm)->DeleteGlobalRef(mJthis);
        JniUtil::getEnv(mVm)->DeleteGlobalRef(mThisClass);
    }

protected:
    JavaVM *mVm;
    jobject mJthis;
    jclass mThisClass; // A global ref on this keeps the method ids valid too.
};

#endif // ANDROID_AUTO_PROJECTION_PROTOCOL_AUDIO_SINK_NATIVECALLBACKWRAPPER_H
