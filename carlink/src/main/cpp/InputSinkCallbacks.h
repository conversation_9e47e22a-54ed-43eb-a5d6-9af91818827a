// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef ANDROID_AUTO_PROJECTION_PROTOCOL_INPUTSINK_CALLBACKS_H
#define ANDROID_AUTO_PROJECTION_PROTOCOL_INPUTSINK_CALLBACKS_H

#include "IInputSinkCallbacks.h"
#include "NativeCallbackWrapper.h"
#include "util/common.h"

class InputSinkCallbacks : public NativeCallbackWrapper, public IInputSinkCallbacks {
public:
    InputSinkCallbacks(JNIEnv *env, jobject jthis) : NativeCallbackWrapper(env, jthis) {
        mChannelOpenCallbackId = env->GetMethodID(
                mThisClass, "onChannelOpened", "()I");
        mDiscoverInputSinkCallbackId = env->GetMethodID(
                mThisClass, "discoverInputService", "(ZZZ)Z");
        mOnTouchEventId = env->GetMethodID(
                mThisClass, "onTouchEvent", "(JI[I[I[III)V");
        mOnKeyEventId = env->GetMethodID(
                mThisClass, "onKeyEvent", "(JIZZI)V");
        mOnAbsoluteEventId = env->GetMethodID(
                mThisClass, "onAbsoluteEvent", "(JII)V");
        mOnRelativeEventId = env->GetMethodID(
                mThisClass, "onRelativeEvent", "(JII)V");
        mOnTouchPadEventId = env->GetMethodID(
                mThisClass, "onTouchPadEvent", "(JI[I[I[III)V");

    }

    virtual ~InputSinkCallbacks() {}

    int onChannelOpened();

    bool discoverInputService(bool hasTouchscreen, bool hasTouchpad,
                              bool hasKey);

    void onTouchEvent(uint64_t timestamp, int32_t numPointers,
                      const uint32_t *pointerIds, const uint32_t *x, const uint32_t *y,
                      int action, int actionIndex);

    void onTouchPadEvent(uint64_t timestamp, int32_t numPointers,
                         const uint32_t *pointerIds, const uint32_t *x, const uint32_t *y,
                         int action, int actionIndex);

    void onKeyEvent(uint64_t timestamp, int32_t keycode, bool isDown,
                    bool longPress, uint32_t metaState);

    void onAbsoluteEvent(uint64_t timestamp, int32_t keycode, int32_t value);

    void onRelativeEvent(uint64_t timestamp, int32_t keycode, int32_t delta);

private:
    jmethodID mChannelOpenCallbackId;
    jmethodID mDiscoverInputSinkCallbackId;
    jmethodID mOnTouchEventId;
    jmethodID mOnTouchPadEventId;
    jmethodID mOnKeyEventId;
    jmethodID mOnAbsoluteEventId;
    jmethodID mOnRelativeEventId;
};

#endif // ANDROID_AUTO_PROJECTION_PROTOCOL_BLUETOOTH_CALLBACKS_H
