[versions]
agp = "8.6.1"
androidDriver = "2.1.0"
coreKtx = "1.15.0"
eventbus = "3.3.1"
glide = "5.0.5"
kotlin = "1.9.24"
appcompat = "1.7.0"
localbroadcastmanager = "1.1.0"
constraintlayout = "2.2.0"
material = "1.13.0"
recyclerview = "1.3.2"
roomRuntime = "2.6.1"
viewpager2 = "1.1.0"
xlog = "1.11.1"
xxpermissions = "18.3"
compileSdk = "35"
minSdk = "24"
targetSdk = "35"
autolinkVersionCode = "57"
autolinkVersionName = "2.0.57"
autolinkProVersionCode = "33"
autolinkProVersionName = "1.0.33"
activity = "1.10.0"
protoc = "2.6.1"
protobuf-java = "2.6.1"
protobuf = "0.9.3"

[libraries]
android-driver = { module = "app.cash.sqldelight:android-driver", version.ref = "androidDriver" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-localbroadcastmanager = { module = "androidx.localbroadcastmanager:localbroadcastmanager", version.ref = "localbroadcastmanager" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "roomRuntime" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "roomRuntime" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "roomRuntime" }
androidx-viewpager2 = { module = "androidx.viewpager2:viewpager2", version.ref = "viewpager2" }
coroutines-extensions = { module = "app.cash.sqldelight:coroutines-extensions", version.ref = "androidDriver" }
eventbus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
material = { module = "com.google.android.material:material", version.ref = "material" }
xlog = { module = "com.elvishew:xlog", version.ref = "xlog" }
xxpermissions = { module = "com.github.getActivity:XXPermissions", version.ref = "xxpermissions" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
protoc = { module = "com.google.protobuf:protoc", version.ref = "protoc" }
protobuf-java = { module = "com.google.protobuf:protobuf-java", version.ref = "protobuf-java" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
android-library = { id = "com.android.library", version.ref = "agp" }
google-protobuf = { id = "com.google.protobuf", version.ref = "protobuf" }

