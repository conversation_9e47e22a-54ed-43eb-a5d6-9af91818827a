package com.link.autolink.events;

import android.net.wifi.p2p.WifiP2pDevice;

import java.util.List;

public class WifiP2pListEvent {
    private final List<WifiP2pDevice> scannedDevices;
    private final List<WifiP2pDevice> connectedDevices;

    public WifiP2pListEvent(List<WifiP2pDevice> scannedDevices, List<WifiP2pDevice> connectedDevices) {
            this.scannedDevices = scannedDevices;
            this.connectedDevices = connectedDevices;
    }

    public List<WifiP2pDevice> getScannedDevices() {
        return scannedDevices;
    }

    public List<WifiP2pDevice> getConnectedDevices() {
        return connectedDevices;
    }

}