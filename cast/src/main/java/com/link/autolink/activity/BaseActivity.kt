package com.link.autolink.activity

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.PersistableBundle
import android.util.Log
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat.startForegroundService
import com.car.autolink.events.MediaProjectionEvent
import com.car.autolink.events.ProjectionPermissionEvent
import com.elvishew.xlog.XLog
import com.link.autolink.ProjectionControl
import com.link.autolink.service.AutolinkService
import com.link.autolink.utils.WebViewInputListener
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


abstract class BaseActivity : AppCompatActivity(){
    private var isSecondary = false
    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val data = result.data
            val intent = Intent(this, AutolinkService::class.java)
            intent.putExtra("resultCode", result.resultCode)
            intent.putExtra("data", data)
            intent.putExtra("isSecondary", isSecondary)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
        }
    }

    fun handleProjectionPermission(event: ProjectionPermissionEvent) {
        if (event.intent != null) {
            isSecondary = event.isSecondary
            launcher.launch(event.intent)
        }
    }

    override fun onResume() {
        super.onResume()
        GlobalEventSubscriber.setCurrentActivity(this)
    }

    override fun onPause() {
        super.onPause()
        GlobalEventSubscriber.setCurrentActivity(null)
    }

    companion object {
        private const val TAG = "BaseActivity"
    }
}