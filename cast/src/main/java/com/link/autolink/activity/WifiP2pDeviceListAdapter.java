package com.link.autolink.activity;

import android.graphics.Color;
import android.net.wifi.p2p.WifiP2pDevice;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.link.autolink.R;
import com.link.autolink.fragment.ItemClickListener;

public class WifiP2pDeviceListAdapter extends
        ListAdapter<WifiP2pDevice, WifiP2pDeviceListAdapter.DeviceViewHolder> {

    private static final String TAG = "WifiP2pDeviceListAdapter";
    private final ItemClickListener mItemClickListener;

    WifiP2pDeviceListAdapter(ItemClickListener itemClickListener) {
        super(DIFF_CALLBACK);
        mItemClickListener = itemClickListener;
    }

    @NonNull
    @Override
    public DeviceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View contentView =
                LayoutInflater.from(parent.getContext()).inflate(R.layout.row_devices, parent,
                        false);

        return new DeviceViewHolder(contentView);
    }

    @Override
    public void onBindViewHolder(@NonNull DeviceViewHolder holder, int position) {
        holder.bind(getItem(position));
    }

    class DeviceViewHolder extends RecyclerView.ViewHolder {
        private final View mItemLayoutView;
        private final TextView mDeviceName;
        private final TextView mDeviceStatus;
        private final ImageView mWifiSignal;

        DeviceViewHolder(@NonNull View itemView) {
            super(itemView);
            mItemLayoutView = itemView;
            mDeviceName = itemView.findViewById(R.id.device_name);
            mDeviceStatus = itemView.findViewById(R.id.device_details);
            mWifiSignal = itemView.findViewById(R.id.wifi_signal);
        }

        void bind(WifiP2pDevice device) {
            mDeviceName.setText(device.deviceName);
            mDeviceName.setTextColor(
                    device.status == WifiP2pDevice.CONNECTED ? Color.parseColor("#00a0e9") :
                            mItemClickListener.getThemeColor());
            mDeviceStatus.setText(mItemClickListener.getDeviceStatus(device.status));
            mWifiSignal.setImageResource(getDrawable(device));
            mItemLayoutView.setOnClickListener(v -> mItemClickListener.onClick(device));
        }
    }

    private int getDrawable(WifiP2pDevice wifiP2pDevice) {
        String[] split = wifiP2pDevice.primaryDeviceType.split("-");
        if (split[0].length() == wifiP2pDevice.primaryDeviceType.length()) {
            split[0] =
                    Integer.valueOf(wifiP2pDevice.primaryDeviceType.substring(0, 4), 16).toString();
        }
        switch (split[0]) {
            case "1":
                return R.drawable.ic_list_computer;
            case "2":
                return R.drawable.ic_list_input;
            case "3":
                return R.drawable.ic_list_printer;
            case "4":
                return R.drawable.ic_list_camera;
            case "5":
                return R.drawable.ic_list_storage;
            case "6":
                return R.drawable.ic_list_network;
            case "7":
                return R.drawable.ic_list_display;
            case "8":
                return R.drawable.ic_list_multimedia;
            case "9":
                return R.drawable.ic_list_psp;
            case "10":
                return R.drawable.ic_list_mobile;
            case "11":
                return R.drawable.ic_list_audio;
        }

        return R.drawable.ic_list_unknown;
    }

    private static final DiffUtil.ItemCallback<WifiP2pDevice> DIFF_CALLBACK =
            new DiffUtil.ItemCallback<WifiP2pDevice>() {
                @Override
                public boolean areItemsTheSame(@NonNull WifiP2pDevice oldItem,
                                               @NonNull WifiP2pDevice newItem) {
                    return oldItem.deviceAddress.equals(newItem.deviceAddress);
                }

                @Override
                public boolean areContentsTheSame(@NonNull WifiP2pDevice oldItem,
                                                  @NonNull WifiP2pDevice newItem) {
                    return oldItem.deviceAddress.equals(newItem.deviceAddress);
                }
            };

}
