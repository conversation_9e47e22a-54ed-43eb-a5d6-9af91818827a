package com.link.autolink.activity

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.webkit.PermissionRequest
import android.webkit.WebIconDatabase
import android.webkit.WebView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.PopupMenu
import androidx.lifecycle.lifecycleScope
import com.elvishew.xlog.XLog
import com.link.autolink.ProjectionControl
import com.link.autolink.R
import com.link.autolink.data.local.AppDatabase
import com.link.autolink.databinding.ActivityBrowserBinding
import com.link.autolink.presentation.ProjectionController
import com.link.autolink.presentation.WebViewCallback
import com.link.autolink.presentation.browser.model.BrowserInfo
import com.link.autolink.presentation.widget.BrowserLayout
import com.link.autolink.presentation.widget.CustomWebView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class BrowserActivity : BaseActivity() {
    private lateinit var binding: ActivityBrowserBinding
    private lateinit var projectionController: ProjectionController
    private var mCurrentPermissionRequest: PermissionRequest? = null

    private val webViewCallback = object : WebViewCallback {
        override fun getCallbackType(): String {
            return "activity"
        }

        override fun onStarStatusChanged(isStarred: Boolean) {
            super.onStarStatusChanged(isStarred)
            binding.browserLayout.setSelectStar(isStarred)
        }

        override fun onPageFinished(webView: WebView?) {
            super.onPageFinished(webView)
        }

        override fun onPageLoadStatus(
            isRemote: Boolean,
            webView: WebView?,
            showProgress: Boolean,
            progress: Int
        ) {
            super.onPageLoadStatus(isRemote, webView, showProgress, progress)
            Log.d(TAG, "onPageLoadStatus $showProgress $progress")
            if (isRemote) {
                binding.browserLayout.setLoadProgress(false, 0)
                return
            }
            binding.browserLayout.setLoadProgress(showProgress, progress)
            binding.browserLayout.setEnableReward(webView?.canGoBack() == true)
            binding.browserLayout.setEnableForward(webView?.canGoForward() == true)
            binding.browserLayout.setEnableRefresh(true)
            binding.browserLayout.setEnableStar(true)
        }

        override fun onPageStarted(isRemote: Boolean, url: String?) {
            super.onPageStarted(isRemote, url)
            Log.d(TAG, "onPageStarted $isRemote $url")
            if (isRemote) {
                binding.browserLayout.switchView(false)
            } else {
                binding.browserLayout.switchView(true)
            }
            if (binding.searchEdittext.etSearch.hasFocus()) {
                return
            }
            binding.searchEdittext.etSearch.setText(url)
        }

        override fun onLocalBrowserStatus(enabled: Boolean) {
            super.onLocalBrowserStatus(enabled)
            Log.d(TAG, "BrowserActivity onLocalBrowserStatus: $enabled")
            // 只有在本地模式且当前 WebView 不是活跃的时候才设置
            if (!enabled && projectionController.webView != binding.browserLayout.webView) {
                Log.d(TAG, "BrowserActivity 设置 WebView 为活跃")
                projectionController.setupWebView(binding.browserLayout.webView)
            }
        }

        override fun onPermissionRequest(request: PermissionRequest) {
            super.onPermissionRequest(request)
            XLog.d("onPermissionRequest for ${request.resources.joinToString()}")

            // 1. 关键检查：如果当前已经有一个正在处理的请求，则立即拒绝新的请求。
            //    这可以防止弹出重叠的对话框。
            if (mCurrentPermissionRequest != null) {
                request.deny()
                XLog.w("Another permission request came in while one is active. Denying the new one.")
                return
            }

            // 2. 将当前请求存入成员变量，表示“处理中”状态
            mCurrentPermissionRequest = request

            // 3. 创建并显示对话框
            AlertDialog.Builder(this@BrowserActivity)
                .setTitle("权限请求")
                .setMessage("此页面需要获取以下权限: ${request.resources.joinToString()}")
                .setPositiveButton("允许") { _, _ ->
                    // 使用 mCurrentPermissionRequest 来确保我们处理的是正确的请求
                    mCurrentPermissionRequest?.grant(request.resources)
                    XLog.d("Permission granted by user.")
                    // 4. 重要：处理完毕后，必须清空成员变量，以允许处理下一个请求
                    mCurrentPermissionRequest = null
                }
                .setNegativeButton("拒绝") { _, _ ->
                    mCurrentPermissionRequest?.deny()
                    XLog.d("Permission denied by user.")
                    // 4. 重要：处理完毕后，必须清空成员变量
                    mCurrentPermissionRequest = null
                }
                .setOnDismissListener {
                    // 如果用户关闭了对话框，也视为拒绝，并清空状态
                    if (mCurrentPermissionRequest != null) {
                        mCurrentPermissionRequest?.deny()
                        XLog.d("Permission denied by dismiss.")
                        mCurrentPermissionRequest = null
                    }
                }
                // 建议设置为 false，防止用户通过点击外部区域轻易关闭对话框，导致状态不明确
                .setCancelable(false)
                .show()
        }

        override fun onWebViewReady(webView: WebView?) {
            super.onWebViewReady(webView)
        }

        override fun onBookmarksUpdated(bookmarks: List<BrowserInfo?>?) {
            super.onBookmarksUpdated(bookmarks)
            binding.browserLayout.setBrowserInfoList(bookmarks ?: emptyList())
            val selectedCount = bookmarks?.filter { it?.selected == true }?.size ?: 0
            val isAllSelected = selectedCount == bookmarks?.size
            binding.browserLayout.setTextAllSelect(if (isAllSelected) getString(R.string.online_cancel_all_select) else getString(R.string.online_all_select))
        }

        override fun onNavigationVisibilityChanged(
            visible: Boolean,
            url: String?
        ) {
            super.onNavigationVisibilityChanged(visible, url)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBrowserBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setSupportActionBar(binding.toolbarLocalBrowser)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        projectionController = ProjectionController.getInstance()

        initializeWebIconDatabase()

        val navBarHeight = resources.getDimensionPixelSize(R.dimen.browser_naiv_def_siez)
        binding.browserLayout.setNaviLayoutParams(2, -1, navBarHeight) // Position: 2=bottom
        binding.browserLayout.browserNaviLayout.sendToRemoteButton.visibility = View.VISIBLE
        projectionController.addCallback(webViewCallback)
        binding.searchEdittext.etSearch.setOnEditorActionListener { textView, _, _ ->
            loadUrl(textView, textView.text.toString())
            true
        }

        binding.browserLayout.setOnClickBrowserListener(object :
            BrowserLayout.OnBrowserClickListener {

            override fun onBrowserButtonClick(view: View?) {
                super.onBrowserButtonClick(view)
                when (view?.id) {
                    R.id.forward -> {
                        projectionController.webView.goForward()
                    }

                    R.id.refresh -> {
                        projectionController.webView.reload()
                    }

                    R.id.reward -> {
                        projectionController.webView.goBack()
                    }

                    R.id.send_to_remote -> {
                        projectionController.enableRemoteBrowser(true)
                    }

                    R.id.star -> {
                        projectionController.toggleStarStatus()
                    }

                    R.id.tv_all_select -> {
                        lifecycleScope.launch {
                            val appDatabase = AppDatabase.getInstance(this@BrowserActivity)
                            withContext(Dispatchers.IO) {
                                val browserInfoEntities = appDatabase.browserInfoDao().all
                                if (browserInfoEntities.isEmpty()) {
                                    return@withContext
                                }
                                val targetSelectedState = !browserInfoEntities.all { it.isSelected }
                                browserInfoEntities.forEach { it.isSelected = targetSelectedState }
                                appDatabase.browserInfoDao().updateAll(browserInfoEntities)
                                projectionController.updateBookmarks()
                            }
                        }
                    }

                    R.id.tv_delete -> {
                        lifecycleScope.launch {
                            val browserInfoEntities = withContext(Dispatchers.IO) {
                                val appDatabase = AppDatabase.getInstance(this@BrowserActivity)
                                appDatabase.browserInfoDao().all
                            }
                            browserInfoEntities.forEach {
                                Log.d(TAG, "tv_delete: ${it.isSelected}")
                            }
                            if (browserInfoEntities.isNotEmpty()) {
                                AlertDialog.Builder(this@BrowserActivity)
                                    .setTitle(R.string.online_delete_bookmark)
                                    .setMessage(
                                        String.format(
                                            getString(R.string.online_delete_bookmark_message),
                                            browserInfoEntities.filter { it.isSelected }.size
                                        )
                                    )
                                    .setPositiveButton(R.string.devices_dialog_confirm) { dialog, which ->
                                        lifecycleScope.launch {
                                            withContext(Dispatchers.IO) {
                                                browserInfoEntities.filter { it.isSelected }
                                                    .forEach {
                                                        AppDatabase.getInstance(
                                                            this@BrowserActivity
                                                        ).browserInfoDao().deleteByUrl(it.url)
                                                    }
                                            }
                                            projectionController.updateBookmarks()
                                        }
                                        dialog.dismiss()
                                    }
                                    .setNegativeButton(R.string.devices_dialog_cancel) { dialog, which ->
                                        dialog.dismiss()
                                    }
                                    .create()
                                    .show()
                            }
                        }
                    }
                }
            }
        })
        binding.browserLayout.setOnItemClickListener(object : BrowserLayout.OnItemClickListener {
            override fun onItemClick(
                view: View,
                isFavorite: Boolean,
                browserInfo: BrowserInfo?
            ) {
                Log.d(TAG, "onItemClick: $browserInfo")
                if (!isFavorite) {
                    loadUrl(view, browserInfo?.url ?: "")
                    return
                }
                lifecycleScope.launch {
                    withContext(Dispatchers.IO) {
                        val appDatabase = AppDatabase.getInstance(this@BrowserActivity)
                        appDatabase.browserInfoDao().all.forEach {
                            if (it.url == browserInfo?.url) {
                                it.isSelected = !it.isSelected
                                appDatabase.browserInfoDao().update(it)
                            }
                        }
                    }
                    projectionController.updateBookmarks()
                }
            }

            override fun onLongClick(isFavorite: Boolean) {
                super.onLongClick(isFavorite)
                if (isFavorite) {
                    return
                }
                updateFavoriteStatus(false)
            }
        })

        MainScope().launch {
            val list = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(this@BrowserActivity).browserInfoDao().getAll()
            }
            val browserInfoList =
                list.map { BrowserInfo(it.title, it.url, it.isSelected, it.favicon) }
            binding.browserLayout.setBrowserInfoList(browserInfoList)
        }
    }

    private fun updateFavoriteStatus(showSearch: Boolean) {
        val isFavoriteVisible = binding.browserLayout.isInEditMode
        binding.searchEdittext.visibility = if (showSearch) View.VISIBLE else View.GONE
        binding.browserLayout.bottomLayout.visibility = if (showSearch) View.GONE else View.VISIBLE
        if (binding.browserLayout.bookmarkRecyclerView != null) {
            binding.browserLayout.notifyDataSetChanged()
        }
        if (!showSearch) {
            //todo 管理收藏
        } else if (isFavoriteVisible) {
            lifecycleScope.launch {
                withContext(Dispatchers.IO) {
                    val browserInfoDao =
                        AppDatabase.getInstance(this@BrowserActivity).browserInfoDao()
                    browserInfoDao.all.forEach {
                        it.isSelected = false
                        browserInfoDao.update(it)
                    }
                }
                projectionController.updateBookmarks()
            }
        }

    }

    private fun initializeWebIconDatabase() {
        val path = cacheDir.absolutePath + "/icons"
        val file = File(path)
        if (!file.exists()) {
            file.mkdirs()
        }
        WebIconDatabase.getInstance().open(path)
    }


    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_browser_more, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                when {
                    binding.browserLayout.isInEditMode -> updateFavoriteStatus(true)
                    binding.browserLayout.isFavoriteVisible -> onBackPressedDispatcher.onBackPressed()
                    else -> showWebView(false)
                }
                return true
            }

            R.id.menu_more -> {
                showPopupMenu(findViewById(R.id.menu_more))
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun showPopupMenu(anchor: View) {
        val popup = PopupMenu(this, anchor)
        popup.menuInflater.inflate(R.menu.menu_browser, popup.menu)
        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.item_clear_cache -> {
                    AlertDialog.Builder(this)
                        .setTitle(R.string.online_item_clear_cache)
                        .setMessage(R.string.online_clear_cache_message)
                        .setPositiveButton(R.string.devices_dialog_confirm) { dialog, which ->
                            projectionController.webView.let {
                                it.clearCache(true)
                                it.clearHistory()
                                it.clearFormData()
                            }
                            updateFavoriteStatus(true)
                            dialog.dismiss()
                        }
                        .setNegativeButton(R.string.devices_dialog_cancel) { dialog, which ->
                            dialog.dismiss()
                        }
                        .create()
                        .show()
                }

                R.id.item_managing_collections -> {
                    updateFavoriteStatus(false)
                    binding.browserLayout.switchView(false)
                }

                R.id.item_switch_engines -> {
                    val searchEngines = resources.getStringArray(R.array.default_browser)
                    AlertDialog.Builder(this)
                        .setTitle(R.string.online_item_switch_engines)
                        .setItems(searchEngines) { dialog, which ->
                            // projectionController.searchEngineIndex = which
                            dialog.dismiss()
                        }
                        .create()
                        .show()
                }

                R.id.item_switch_mute -> {

                }

                R.id.item_switch_phone -> {
                    projectionController.enableRemoteBrowser(false)
                }

                R.id.item_video_size -> {
                    val videoSizes = resources.getStringArray(R.array.video_size)
                    AlertDialog.Builder(this)
                        .setTitle(R.string.online_item_video_size)
                        .setItems(videoSizes) { dialog, which ->
                            projectionController.updateVideoSize(which)
                            dialog.dismiss()
                        }
                        .create()
                        .show()
                }
            }
            true
        }
        popup.show()
    }

    override fun onResume() {
        super.onResume()
    }

    private fun loadUrl(view: View, url: String) {
        val formattedUrl = projectionController.processAndLoadUrl(url)
        Log.d(TAG, "loadUrl: $formattedUrl")

        val urlEditText = binding.searchEdittext.etSearch
        urlEditText.clearFocus()
        urlEditText.setText(formattedUrl)
        val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun updateToolbarTitle() {
        // 5. 使用字符串模板
        // val title = "Managing Collections (${browserPresenter.getSelectedBookmarkCount()}/${browserPresenter.getTotalBookmarkCount()})"
        supportActionBar?.title = "title"
    }

    private fun setBookmarkEditMode(enableEditMode: Boolean) {
        val isCurrentlyEditing = binding.browserLayout.isInEditMode
        Log.d(TAG, "showSearch show: ${!enableEditMode}, managingCollections: $isCurrentlyEditing")

        binding.searchEdittext.visibility = if (enableEditMode) View.GONE else View.VISIBLE
//        binding.browserLayout.showBottomActionLayout(enableEditMode)
//
//        binding.browserLayout.bookmarkAdapter?.notifyDataSetChanged()

        if (enableEditMode) {
            updateToolbarTitle()
        } else if (isCurrentlyEditing) {
            //browserPresenter.clearAllSelections()
        }
    }

    private fun showWebView(show: Boolean) {
        binding.browserLayout.switchView(show)
    }

    override fun onDestroy() {
        projectionController.removeCallback(webViewCallback)
        val webView: CustomWebView = binding.browserLayout.webView as CustomWebView
        webView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null)
        webView.clearHistory()

        val parent = webView.parent
        if (parent is ViewGroup) {
            parent.removeView(webView)
        }

        webView.destroy()
        binding.browserLayout.setWebView(null)
        super.onDestroy()
    }


    companion object {
        private const val TAG = "BrowserActivity"
    }

}