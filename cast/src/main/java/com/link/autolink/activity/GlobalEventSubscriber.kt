package com.link.autolink.activity

import android.annotation.SuppressLint
import android.content.Context
import android.os.SystemClock
import android.util.Log
import android.view.Display
import android.view.MotionEvent
import android.view.WindowManager
import com.car.autolink.events.DisconnectedEvent
import com.car.autolink.events.MediaProjectionEvent
import com.car.autolink.events.PresentationDisplayCreatedEvent
import com.car.autolink.events.ProjectionPermissionEvent
import com.car.autolink.events.WlTouchEvent
import com.elvishew.xlog.XLog
import com.link.autolink.ProjectionControl
import com.link.autolink.R
import com.link.autolink.presentation.CastMirrorPresentation
import com.link.autolink.presentation.MirrorBasePresentation
import com.link.autolink.utils.WebViewInputListener
import com.link.autolink.widget.KeyboardInputDialog
import com.link.autolink.widget.InputDialogHelper
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.lang.ref.WeakReference

object GlobalEventSubscriber {
    // Hold a weak reference to the current foreground activity
    private var currentActivity: WeakReference<BaseActivity>? = null
    private var mPresentation: MirrorBasePresentation? = null

    @SuppressLint("StaticFieldLeak")
    private var keyboardInputDialog: KeyboardInputDialog? = null

    fun initialize() {
        // Register itself with EventBus only once
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    fun setCurrentActivity(activity: BaseActivity?) {
        currentActivity = if (activity != null) WeakReference(activity) else null
    }

    // Move the @Subscribe methods here from BaseActivity
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onProjectionPermissionEvent(event: ProjectionPermissionEvent) {
        XLog.tag("GlobalEventSubscriber").d("onProjectionPermissionEvent received once")
        // Use the current activity to launch the intent
        currentActivity?.get()?.handleProjectionPermission(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMediaProjectionEvent(event: MediaProjectionEvent) {
        XLog.tag("GlobalEventSubscriber").d("onMediaProjectionEvent received once")
        if (event.intent != null) {
            ProjectionControl.onActivityResult(
                event.resultCode,
                event.intent,
                event.isSecondary
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPresentationDisplayCreatedEvent(event: PresentationDisplayCreatedEvent) {
        XLog.tag("GlobalEventSubscriber").d("onPresentationDisplayCreatedEvent received once")
        if (event.display == null) {
            releaseCurrentPresentation()
        } else {
            showAMapPresentation(event.display)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onWlTouchEvent(event: WlTouchEvent) {
        XLog.tag("GlobalEventSubscriber")
            .d("onWlTouchEvent received ${event.x} ${event.y} ${event.action} ${event.timestamp}")
        val event = MotionEvent.obtain(
            SystemClock.uptimeMillis(), SystemClock.uptimeMillis(),
            event.action, event.x.toFloat(), event.y.toFloat(), 0
        )
        mPresentation?.dispatchTouchEvent(event)
    }

    @Subscribe
    fun onDisconnectedEvent(event: DisconnectedEvent) {
        XLog.tag("GlobalEventSubscriber").d("onDisconnectedEvent received")
        ProjectionControl.release()
    }


    @Synchronized
    fun showAMapPresentation(display: Display?, isSupportCircularScreen: Boolean = false) {
        // 如果当前已有投屏且显示设备不同，则需要先释放当前投屏
        if (shouldReleaseCurrentPresentation(display)) {
            releaseCurrentPresentation()
        }

        // 只有当没有活跃的投屏且提供了有效的显示设备时才创建新的投屏
        if (mPresentation == null && display != null) {
            createAndShowPresentation(display, isSupportCircularScreen)
        }
    }


    private fun createAndShowPresentation(display: Display, isSupportCircularScreen: Boolean) {
        try {
            val presentation = createMapPresentation(
                currentActivity!!.get()!!,
                display,
                R.style.PresentationDialog,
                isSupportCircularScreen
            )

            mPresentation = presentation
            presentation.show()
            Log.i(TAG, "成功创建并显示投屏")
        } catch (e: WindowManager.InvalidDisplayException) {
            Log.e(TAG, "无法显示投屏！显示设备可能已被移除", e)
            mPresentation = null
        } catch (e: Exception) {
            Log.e(TAG, "创建或显示投屏时发生未知错误", e)
            mPresentation = null
        }
    }

    // 新的贴键盘输入框监听器
    private val keyboardInputDialogListener = object : KeyboardInputDialog.InputDialogListener {
        override fun onTextChanged(text: String) {
            updateWebViewInput(text)
        }

        override fun onInputDone() {
            //submitWebViewInput()
            keyboardInputDialog = null
        }
    }

    private val inputListener = object : WebViewInputListener {
        override fun onInputFocused(type: String, currentValue: String) {
            Log.d(TAG, "onInputFocused")
            currentActivity?.get()?.runOnUiThread {
                // 使用新的贴键盘输入框
                if (keyboardInputDialog == null) {
                    keyboardInputDialog = KeyboardInputDialog.newInstance(currentValue)
                    keyboardInputDialog?.listener = keyboardInputDialogListener
                    keyboardInputDialog?.show(
                        currentActivity?.get()?.supportFragmentManager!!,
                        "keyboard_input_dialog"
                    )
                } else {
                    keyboardInputDialog?.updateText(currentValue)
                }
            }
        }

        override fun onInputBlurred() {
            currentActivity?.get()?.runOnUiThread {
                keyboardInputDialog?.dismiss()
                keyboardInputDialog = null
            }
        }
    }


    private fun createMapPresentation(
        context: Context, display: Display, theme: Int, isSupportCircularScreen: Boolean
    ): MirrorBasePresentation {
        return CastMirrorPresentation(
            context = context,
            display = display,
            theme = theme,
            inputListener = inputListener
        )
    }


    private fun shouldReleaseCurrentPresentation(newDisplay: Display?): Boolean {
        return mPresentation != null && mPresentation?.display !== newDisplay
    }

    private fun releaseCurrentPresentation() {
        try {
            Log.i(TAG, "释放当前投屏，因为显示设备已更改")
            if (mPresentation?.isShowing == true) {
                mPresentation?.dismiss()
            }
            mPresentation = null
        } catch (th: Throwable) {
            Log.e(TAG, "释放当前投屏失败", th)
        }
    }

    fun updateWebViewInput(text: String) {
        (mPresentation as? CastMirrorPresentation)?.updateWebViewInput(text)
    }

    fun submitWebViewInput() {
        (mPresentation as? CastMirrorPresentation)?.submitWebViewInput()
    }

    fun blurWebViewInput() {
        (mPresentation as? CastMirrorPresentation)?.blurWebViewInput()
    }

    private const val TAG = "GlobalEventSubscriber"
}