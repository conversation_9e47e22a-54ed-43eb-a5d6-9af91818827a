package com.link.autolink.activity

import android.net.wifi.p2p.WifiP2pDevice
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.util.TypedValue
import android.view.MenuItem
import android.view.View
import android.view.View.VISIBLE
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.core.content.res.use
import androidx.recyclerview.widget.LinearLayoutManager
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.link.autolink.CastWifiP2pManager
import com.link.autolink.ProjectionControl
import com.link.autolink.R
import com.link.autolink.databinding.ActivityWifiP2pBinding
import com.link.autolink.events.WifiP2pListEvent
import com.link.autolink.events.WifiP2pSearchEvent
import com.link.autolink.fragment.ItemClickListener
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

class WifiP2pActivity : BaseActivity() {

    private lateinit var binding: ActivityWifiP2pBinding


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWifiP2pBinding.inflate(layoutInflater)
        setContentView(binding.root)
        EventBus.getDefault().register(this)
        setSupportActionBar(binding.toolbarWifiP2p)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.devices_title)
        setupListeners()
        requestPermissions()
    }

    private fun setupListeners() {
        binding.btnPairRefresh.setOnClickListener {
            if (CastWifiP2pManager.isSearching()) {
                CastWifiP2pManager.stopSearch()
            } else {
                search()
            }
        }
    }

    private fun requestPermissions() {
        if (XXPermissions.isGranted(
                this,
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) PERMISSIONS else ADVANCE_PERMISSIONS
            )
        ) {
            search()
        }
        XXPermissions.with(this)
            .permission(if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) PERMISSIONS else ADVANCE_PERMISSIONS)
            .request { permissions, allGranted ->
                {
                    if (allGranted) {
                        search()
                    }
                }
            }
    }

    private fun search() {
        CastWifiP2pManager.search()
    }


    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId != android.R.id.home) {
            return super.onOptionsItemSelected(item)
        }
        onBackPressed()
        return true
    }

    companion object {
        private val PERMISSIONS: Array<String?> = arrayOf(
            Permission.ACCESS_COARSE_LOCATION,
            Permission.ACCESS_FINE_LOCATION,
        )

        @RequiresApi(api = Build.VERSION_CODES.S)
        private val ADVANCE_PERMISSIONS: Array<String?> = arrayOf(
            Permission.ACCESS_COARSE_LOCATION,
            Permission.ACCESS_FINE_LOCATION,
            Permission.NEARBY_WIFI_DEVICES,
            Permission.BLUETOOTH_SCAN,
            Permission.BLUETOOTH_CONNECT,
            Permission.BLUETOOTH_ADVERTISE,
            Permission.POST_NOTIFICATIONS
        )
        private const val TAG = "WifiP2pActivity"
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }


    @Subscribe
    fun onWifiP2pSearchEvent(event: WifiP2pSearchEvent) {
        if (event.isScanning) {
            binding.btnPairRefresh.text = getString(R.string.devices_discover_stop)
            binding.tvNoAvailable.text = getString(R.string.devices_searching)
            binding.pbLoading.visibility = VISIBLE
        } else {
            binding.btnPairRefresh.text = getString(R.string.devices_discover_start)
            binding.tvNoAvailable.text = getString(R.string.devices_click_searching)
            binding.pbLoading.visibility = View.GONE
        }
    }

    @Subscribe
    fun onWifiP2pListEvent(event: WifiP2pListEvent) {
        Log.d(
            TAG,
            "onWifiP2pListEvent ${event.scannedDevices.size} ${event.connectedDevices.size} "
        )
        if (event.scannedDevices.isNotEmpty()) {
            binding.tvNoAvailable.visibility = View.GONE
            binding.rvAvailable.visibility = VISIBLE
        } else {
            binding.tvNoAvailable.visibility = View.VISIBLE
            binding.rvAvailable.visibility = View.GONE
        }
        binding.rvAvailable.layoutManager = LinearLayoutManager(this)
        binding.rvAvailable.adapter = WifiP2pDeviceListAdapter(object : ItemClickListener {
            override fun onClick(wifiP2pDevice: WifiP2pDevice?) {
                handleClick(wifiP2pDevice)
            }

            override fun getThemeColor(): Int {
                val typedValue = TypedValue()
                theme.resolveAttribute(android.R.attr.textColorPrimary, typedValue, true)
                val attrs = intArrayOf(android.R.attr.textColorPrimary)
                val arr = obtainStyledAttributes(typedValue.data, attrs)
                val primaryColor = arr.use {
                    it.getColor(0, -1)
                }
                return primaryColor
            }

            override fun getDeviceStatus(deviceStatus: Int): String? {
                return when (deviceStatus) {
                    WifiP2pDevice.AVAILABLE -> getString(R.string.status_tap)
                    WifiP2pDevice.INVITED -> getString(R.string.status_invited)
                    WifiP2pDevice.CONNECTED -> getString(R.string.status_preparing)
                    WifiP2pDevice.FAILED -> getString(R.string.status_failed)
                    WifiP2pDevice.UNAVAILABLE -> getString(R.string.status_unavailable)
                    else -> getString(R.string.status_unknown)
                }
            }

        })
        binding.rvAvailable.adapter?.let {
            it as WifiP2pDeviceListAdapter
            it.submitList(event.scannedDevices)
        }

        if (event.connectedDevices.isNotEmpty()) {
            binding.rvPaired.visibility = VISIBLE
            binding.tvPaired.visibility = VISIBLE
            CastWifiP2pManager.stopSearch()

            // 只有在没有 AutolinkControl 实例时才启动连接
            if (!ProjectionControl.isConnected()) {
                Log.d(TAG, "P2P 连接成功，启动 ProjectionControl")
                ProjectionControl.startConnect()
            }
        } else {
            binding.rvPaired.visibility = View.GONE
            binding.tvPaired.visibility = View.GONE

            // P2P 断开时释放连接
            ProjectionControl.release()
        }

        binding.rvPaired.layoutManager = LinearLayoutManager(this)
        binding.rvPaired.adapter = WifiP2pDeviceListAdapter(object : ItemClickListener {
            override fun onClick(wifiP2pDevice: WifiP2pDevice?) {
                handleClick(wifiP2pDevice)
            }

            override fun getThemeColor(): Int {
                val typedValue = TypedValue()
                theme.resolveAttribute(android.R.attr.textColorPrimary, typedValue, true)
                val attrs = intArrayOf(android.R.attr.textColorPrimary)
                val arr = obtainStyledAttributes(typedValue.data, attrs)
                val primaryColor = arr.use {
                    it.getColor(0, -1)
                }
                return primaryColor
            }

            override fun getDeviceStatus(deviceStatus: Int): String? {
                return when (deviceStatus) {
                    WifiP2pDevice.AVAILABLE -> getString(R.string.status_tap)
                    WifiP2pDevice.INVITED -> getString(R.string.status_invited)
                    WifiP2pDevice.CONNECTED -> getString(R.string.status_preparing)
                    WifiP2pDevice.FAILED -> getString(R.string.status_failed)
                    WifiP2pDevice.UNAVAILABLE -> getString(R.string.status_unavailable)
                    else -> getString(R.string.status_unknown)
                }
            }

        })
        binding.rvPaired.adapter?.let {
            it as WifiP2pDeviceListAdapter
            it.submitList(event.connectedDevices)
        }

    }

    private fun handleClick(wifiP2pDevice: WifiP2pDevice?) {
        if (wifiP2pDevice?.status != WifiP2pDevice.CONNECTED) {
            if (wifiP2pDevice?.status != WifiP2pDevice.INVITED) {
                CastWifiP2pManager.connect(wifiP2pDevice)
            } else {
                AlertDialog.Builder(this)
                    .setTitle(R.string.devices_cancel_connect_title)
                    .setMessage(
                        String.format(
                            getString(R.string.devices_cancel_connect_message),
                            wifiP2pDevice.deviceName
                        )
                    )
                    .setPositiveButton(R.string.devices_dialog_confirm) { dialog, which ->
                        CastWifiP2pManager.cancelConnect(wifiP2pDevice)
                        dialog.dismiss()
                    }
                    .setNegativeButton(R.string.devices_dialog_cancel) { dialog, which ->
                        dialog.dismiss()
                    }
                    .create()
                    .show()
            }
        } else {
            AlertDialog.Builder(this)
                .setTitle(R.string.devices_disconnect_title)
                .setMessage(
                    String.format(
                        getString(R.string.devices_disconnect_message),
                        wifiP2pDevice.deviceName
                    )
                )
                .setPositiveButton(R.string.devices_dialog_confirm) { dialog, which ->
                    CastWifiP2pManager.disconnect()
                }
                .setNegativeButton(R.string.devices_dialog_cancel) { dialog, which ->
                    dialog.dismiss()
                }
                .create()
                .show()
        }

    }
}