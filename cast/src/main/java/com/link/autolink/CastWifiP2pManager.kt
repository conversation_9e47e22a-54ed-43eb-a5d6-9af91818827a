package com.link.autolink

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.NetworkInfo
import android.net.wifi.p2p.WifiP2pConfig
import android.net.wifi.p2p.WifiP2pDevice
import android.net.wifi.p2p.WifiP2pDeviceList
import android.net.wifi.p2p.WifiP2pInfo
import android.net.wifi.p2p.WifiP2pManager
import android.os.Build
import android.text.TextUtils
import android.util.Log
import com.link.autolink.events.P2pConnectionEvent
import com.link.autolink.events.WifiP2pListEvent
import com.link.autolink.events.WifiP2pSearchEvent
import org.greenrobot.eventbus.EventBus
import java.net.NetworkInterface
import java.net.SocketException

@SuppressLint("StaticFieldLeak")
object CastWifiP2pManager {
    private const val TAG = "CastWifiP2pManager"
    private var isSearching = false
    private var wifiManager: WifiP2pManager? = null
    private var channel: WifiP2pManager.Channel? = null
    private var context: Context? = null
    var isConnected = false
    private var channelListener: WifiP2pManager.ChannelListener? = null
    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION -> {

                }

                WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION -> {
                    Log.d(TAG, "WIFI_P2P_PEERS_CHANGED_ACTION")
                    val deviceList = intent.getParcelableExtra<WifiP2pDeviceList>(
                        WifiP2pManager.EXTRA_P2P_DEVICE_LIST
                    )?.deviceList
                    val connectedDevices =
                        deviceList?.filter { it.status == WifiP2pDevice.CONNECTED }
                    val scannedDevices = deviceList?.filter { it.status != WifiP2pDevice.CONNECTED }
                    EventBus.getDefault().post(
                        WifiP2pListEvent(
                            scannedDevices ?: emptyList(),
                            connectedDevices ?: emptyList()
                        )
                    )
                }

                WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION -> {
                    val networkInfo = intent.getParcelableExtra<NetworkInfo>(
                        WifiP2pManager.EXTRA_NETWORK_INFO
                    )
                    isConnected = networkInfo?.isConnected == true
                    Log.d(TAG, "P2P connection changed. Connected: $isConnected")
                    if (isConnected) {
                        wifiManager?.requestConnectionInfo(
                            channel,
                            this@CastWifiP2pManager::processConnectionInfo
                        )
                    }
                }

                WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION -> {
                    val thisDevice: WifiP2pDevice? =
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            intent.getParcelableExtra(
                                WifiP2pManager.EXTRA_WIFI_P2P_DEVICE,
                                WifiP2pDevice::class.java
                            )
                        } else {
                            @Suppress("DEPRECATION")
                            intent.getParcelableExtra(WifiP2pManager.EXTRA_WIFI_P2P_DEVICE)
                        }
                    Log.d(TAG, "This device changed. Name: ${thisDevice?.deviceName}")
                }

                WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION -> {

                }
            }
        }

    }

    @SuppressLint("MissingPermission")
    private fun processConnectionInfo(info: WifiP2pInfo?) {
        Log.d(TAG, "Processing connection info: $info")

        wifiManager?.requestGroupInfo(channel) { group ->
            if (group == null || info == null || info.groupOwnerAddress == null) {
                Log.w(TAG, "Connection info is incomplete. Cannot proceed.")
                return@requestGroupInfo
            }

            val ownerDeviceName = group.owner?.deviceName ?: ""
            val (localIp, broadcastAddr) = getLocalIpAndBroadcastAddress(group.`interface`)

            Log.d(
                TAG,
                "Connection details: broadcast=$broadcastAddr, owner=$ownerDeviceName, ownerAddress=${info.groupOwnerAddress}"
            )

            if (TextUtils.isEmpty(broadcastAddr) || TextUtils.isEmpty(ownerDeviceName)) {
                return@requestGroupInfo
            }
            isConnected = true
            EventBus.getDefault().post(P2pConnectionEvent(4, ownerDeviceName))
        }
    }

    private fun getLocalIpAndBroadcastAddress(interfaceName: String?): Pair<String?, String?> {
        interfaceName ?: return Pair(null, null)
        try {
            return NetworkInterface.getNetworkInterfaces().asSequence()
                .filter { !it.isLoopback && it.name == interfaceName }
                .flatMap { it.interfaceAddresses.asSequence() }
                .mapNotNull { addr ->
                    addr.broadcast?.let { broadcast ->
                        Pair(addr.address.hostAddress, broadcast.toString().substring(1))
                    }
                }
                .firstOrNull() ?: Pair(null, null)
        } catch (e: SocketException) {
            Log.e(TAG, "Could not get network interface info", e)
            return Pair(null, null)
        }
    }

    fun init(context: Context) {
        this.context = context.applicationContext
        wifiManager = context.getSystemService(Context.WIFI_P2P_SERVICE) as WifiP2pManager
        initializeChannel()
        context.registerReceiver(
            receiver,
            IntentFilter(WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION).apply {
                addAction(WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION)
                addAction(WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION)
                addAction(WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION)
                addAction(WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION)
            }
        )
    }

    /**
     * 初始化或重新初始化 WiFi P2P Channel
     */
    private fun initializeChannel() {
        channelListener = WifiP2pManager.ChannelListener {
            Log.w(TAG, "WiFi P2P Channel 断开，尝试重新初始化")
            // Channel 失效时重新初始化
            context?.let { ctx ->
                channel = wifiManager?.initialize(ctx, ctx.mainLooper, channelListener)
                Log.d(TAG, "WiFi P2P Channel 重新初始化完成")
            }
        }

        context?.let { ctx ->
            channel = wifiManager?.initialize(ctx, ctx.mainLooper, channelListener)
            Log.d(TAG, "WiFi P2P Channel 初始化完成")
        }
    }

    /**
     * 确保 Channel 有效，如果无效则重新初始化
     */
    private fun ensureChannelValid(): Boolean {
        if (channel == null) {
            Log.w(TAG, "Channel 为空，重新初始化")
            initializeChannel()
        }
        return channel != null
    }

    fun destroy() {
        wifiManager = null
        channel = null
    }

    @SuppressLint("MissingPermission")
    fun search() {
        if (!ensureChannelValid()) {
            Log.e(TAG, "无法初始化 WiFi P2P Channel，搜索失败")
            isSearching = false
            EventBus.getDefault().post(WifiP2pSearchEvent(false))
            return
        }

        channel?.let {
            wifiManager?.discoverPeers(it, object : WifiP2pManager.ActionListener {
                override fun onSuccess() {
                    Log.d(TAG, "discoverPeers onSuccess")
                    isSearching = true
                    EventBus.getDefault().post(WifiP2pSearchEvent(true))
                }

                override fun onFailure(reasonCode: Int) {
                    Log.d(TAG, "discoverPeers onFailure $reasonCode")
                    isSearching = false
                    EventBus.getDefault().post(WifiP2pSearchEvent(false))
                }
            })
        }
    }

    fun stopSearch() {
        if (!ensureChannelValid()) {
            Log.w(TAG, "Channel 无效，直接设置搜索状态为 false")
            isSearching = false
            EventBus.getDefault().post(WifiP2pSearchEvent(false))
            return
        }

        channel?.let {
            wifiManager?.stopPeerDiscovery(it, object : WifiP2pManager.ActionListener {
                override fun onSuccess() {
                    Log.d(TAG, "stopPeerDiscovery onSuccess")
                    isSearching = false
                    EventBus.getDefault().post(WifiP2pSearchEvent(false))
                }

                override fun onFailure(reason: Int) {
                    Log.d(TAG, "stopPeerDiscovery onFailure $reason")
                    isSearching = false
                    EventBus.getDefault().post(WifiP2pSearchEvent(false))
                }

            })
        }
    }

    fun isSearching(): Boolean {
        return isSearching
    }

    @SuppressLint("MissingPermission")
    fun connect(wifiP2pDevice: WifiP2pDevice?) {
        if (!ensureChannelValid()) {
            Log.e(TAG, "Channel 无效，连接失败")
            EventBus.getDefault().post(P2pConnectionEvent(3, ""))
            return
        }

        wifiP2pDevice?.let {
            wifiManager?.connect(channel, WifiP2pConfig().apply {
                deviceAddress = wifiP2pDevice.deviceAddress
                groupOwnerIntent = 0
            }, object : WifiP2pManager.ActionListener {
                override fun onSuccess() {
                    Log.d(TAG, "connect onSuccess: ")
                    EventBus.getDefault().post(P2pConnectionEvent(2, ""))
                }

                override fun onFailure(reason: Int) {
                    Log.d(TAG, "connect onFailure: $reason")
                    EventBus.getDefault().post(P2pConnectionEvent(3, ""))
                }

            })
        }
    }

    fun cancelConnect(wifiP2pDevice: WifiP2pDevice?) {
        if (!ensureChannelValid()) {
            Log.w(TAG, "Channel 无效，无法取消连接")
            return
        }

        wifiP2pDevice?.let {
            wifiManager?.removeGroup(channel, object : WifiP2pManager.ActionListener {
                override fun onSuccess() {
                    Log.d(TAG, "cancelConnect onSuccess: ")
                }

                override fun onFailure(reason: Int) {
                    Log.d(TAG, "cancelConnect onFailure: $reason")
                }
            })
        }
    }

    fun disconnect() {
        isConnected = false
        EventBus.getDefault().post(P2pConnectionEvent(5, ""))

        if (!ensureChannelValid()) {
            Log.w(TAG, "Channel 无效，无法断开连接")
            return
        }

        wifiManager?.removeGroup(channel, object : WifiP2pManager.ActionListener {
            override fun onSuccess() {
                Log.d(TAG, "disconnect onSuccess: ")
            }

            override fun onFailure(reason: Int) {
                Log.d(TAG, "disconnect onFailure: $reason")
            }
        })
    }

    fun requestConnectionInfo() {
        if (!ensureChannelValid()) {
            Log.w(TAG, "Channel 无效，无法请求连接信息")
            return
        }

        channel?.let {
            wifiManager?.requestConnectionInfo(it) { wifiP2pInfo ->
                processConnectionInfo(wifiP2pInfo)
            }
        }
    }

}