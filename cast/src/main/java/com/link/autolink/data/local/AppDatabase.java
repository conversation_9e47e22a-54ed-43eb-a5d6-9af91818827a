package com.link.autolink.data.local;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;

@Database(entities = {BrowserInfoEntity.class}, version = 1)
@TypeConverters({BitmapConverter.class})
public abstract class AppDatabase extends RoomDatabase {

    public abstract BrowserInfoDao browserInfoDao();

    private static volatile AppDatabase INSTANCE;

    public static AppDatabase getInstance(final Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(context.getApplicationContext(),
                                    AppDatabase.class, "browser_history.db")
                            .build();
                }
            }
        }
        return INSTANCE;
    }
}
