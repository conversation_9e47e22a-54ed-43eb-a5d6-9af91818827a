package com.link.autolink.data.local;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

@Dao
public interface BrowserInfoDao {

    // OnConflictStrategy.REPLACE 表示如果插入的数据 url 已存在，则替换旧数据
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(BrowserInfoEntity browserInfo);

    @Update
    void update(BrowserInfoEntity browserInfo);

    @Update
    void updateAll(List<BrowserInfoEntity> entities);

    @Query("SELECT * FROM browser_history ORDER BY id DESC")
    List<BrowserInfoEntity> getAll();

    @Query("SELECT * FROM browser_history WHERE url = :url LIMIT 1")
    BrowserInfoEntity findByUrl(String url);

    @Query("DELETE FROM browser_history WHERE url = :url")
    void deleteByUrl(String url);

    @Query("DELETE FROM browser_history")
    void clearAll();
}