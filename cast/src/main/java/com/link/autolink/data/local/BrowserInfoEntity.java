package com.link.autolink.data.local;


import android.graphics.Bitmap;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

@Entity(tableName = "browser_history", indices = {@Index(value = "url", unique = true)})
public class BrowserInfoEntity {

    @PrimaryKey(autoGenerate = true)
    public int id;

    @NonNull
    @ColumnInfo(name = "title")
    public String title;

    @NonNull
    @ColumnInfo(name = "url")
    public String url;

    @ColumnInfo(name = "favicon")
    public Bitmap favicon;

    @ColumnInfo(name = "is_selected")
    public boolean isSelected;

    // Room 需要一个无参构造函数
    public BrowserInfoEntity() {}

    // 你可以创建一个方便的构造函数来从你的 Model 类进行转换
    public BrowserInfoEntity(@NonNull String title, @NonNull String url, Bitmap favicon, boolean isSelected) {
        this.title = title;
        this.url = url;
        this.favicon = favicon;
        this.isSelected = isSelected;
    }
}