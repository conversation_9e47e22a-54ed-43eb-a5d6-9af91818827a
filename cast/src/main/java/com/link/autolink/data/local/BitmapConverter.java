package com.link.autolink.data.local;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import androidx.room.TypeConverter;

import java.io.ByteArrayOutputStream;

public class BitmapConverter {
    /**
     * 将 Bitmap 转换为 byte[] 以便存入数据库
     */
    @TypeConverter
    public byte[] fromBitmap(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        // 将 Bitmap 压缩为 PNG 格式的字节流
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, stream);
        return stream.toByteArray();
    }

    /**
     * 将 byte[] 转换回 Bitmap 以便在代码中使用
     */
    @TypeConverter
    public Bitmap toBitmap(byte[] byteArray) {
        if (byteArray == null) {
            return null;
        }
        return BitmapFactory.decodeByteArray(byteArray, 0, byteArray.length);
    }
}
