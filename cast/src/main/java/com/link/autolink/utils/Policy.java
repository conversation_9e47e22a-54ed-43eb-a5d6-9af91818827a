package com.link.autolink.utils;

import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import com.link.autolink.R;

/**
 * <AUTHOR>
 * @date 2020/8/11
 */
public class Policy {
    private static volatile Policy instance = null;

    private Policy() {

    }

    public static Policy getInstance() {
        if (instance == null) {
            synchronized (Policy.class) {
                if (instance == null) {
                    instance = new Policy();
                }
            }
        }
        return instance;
    }

    public Dialog showRuleDialog(final Context context, String title, int tagColor,
                                 final RuleListener ruleListener) {
        if (hasShowRule(context)) {
            if (ruleListener != null) {
                ruleListener.rule(true);
            }
            return null;
        }
        final Dialog dialog = new Dialog(context, R.style.POLICY_DIALOG);
        String policyText = context.getString(R.string.policy_text);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        dialog.setContentView(R.layout.layout_rule);
        dialog.show();
        TextView tvOk = dialog.findViewById(R.id.tv_ok);
        TextView tvCancel = dialog.findViewById(R.id.tv_cancel);
        TextView tvTitle = dialog.findViewById(R.id.tv_title);
        TextView tvText = dialog.findViewById(R.id.tv_text);
        tvTitle.setText(title);
        String tag1 = "《";
        String tag2 = "》";
        int firstIndex = policyText.indexOf(tag1);
        int secondIndex = policyText.indexOf(tag2) + 1;

        SpannableStringBuilder style = new SpannableStringBuilder();
        style.append(policyText);
        ClickableSpan clickableSpanOne = new ClickableSpan() {
            @Override
            public void onClick(View v) {
                if (ruleListener != null) {
                    ruleListener.policyClick();
                }
            }
        };

        style.setSpan(clickableSpanOne, firstIndex, secondIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvText.setText(style);

        ForegroundColorSpan foregroundColorSpanOne =
                new ForegroundColorSpan(context.getResources().getColor(tagColor));
        style.setSpan(foregroundColorSpanOne, firstIndex, secondIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvText.setMovementMethod(LinkMovementMethod.getInstance());
        tvText.setText(style);

        tvOk.setOnClickListener(v -> {
            dialog.dismiss();
            if (ruleListener != null) {
                ruleListener.rule(true);
                putShowRule(context);
            }
        });
        tvCancel.setOnClickListener(v -> {
            dialog.dismiss();
            if (ruleListener != null) {
                ruleListener.rule(false);
            }
        });
        return dialog;
    }

    public boolean hasShowRule(Context context) {
        SharedPreferences sharedPreferences =
                context.getSharedPreferences("rule", Context.MODE_PRIVATE);
        return sharedPreferences.getBoolean("rule", false);
    }

    public void putShowRule(Context context) {
        SharedPreferences sharedPreferences =
                context.getSharedPreferences("rule", Context.MODE_PRIVATE);
        sharedPreferences.edit().putBoolean("rule", true).apply();
    }

    public void clearShowRule(Context context) {
        SharedPreferences sharedPreferences =
                context.getSharedPreferences("rule", Context.MODE_PRIVATE);
        sharedPreferences.edit().putBoolean("rule", false).apply();
    }

    public interface RuleListener {
        void rule(boolean agree);

        void policyClick();
    }
}
