package com.link.autolink.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

public final class SearchEngineRepository {

    private static final String TAG = "SearchEngineRepository";
    private static final String PREFS_NAME = "browserEngines";
    private static final String KEY_DOMAIN_INDEX = "domain_name_index";

    private final SharedPreferences sharedPreferences;

    public SearchEngineRepository(Context context) {
        // Use application context to prevent memory leaks
        this.sharedPreferences = context.getApplicationContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Retrieves the saved index for the user's preferred search engine.
     * Defaults to 0 if no preference is found.
     *
     * @return The integer index of the search engine.
     * Original name: m3233a()
     */
    public int getSearchEngineIndex() {
        int index = sharedPreferences.getInt(KEY_DOMAIN_INDEX, 0);
        Log.d(TAG, "getSearchEngineIndex() returned index: " + index);
        return index;
    }
}