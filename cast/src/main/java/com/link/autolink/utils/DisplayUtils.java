package com.link.autolink.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

public final class DisplayUtils {

    private SharedPreferences preferences;

    public DisplayUtils(Context context) {
        this.preferences = context.getSharedPreferences("adjustVideoSize", 0);
    }

    public int getVideoSizeIndex() {
        int mode = this.preferences.getInt("adjust_video_size_index", 0);
        Log.d("DisplayUtils", "getAspectRatioMode: "+mode);
        return mode;
    }

    public void setVideoSizeIndex(int which) {
        this.preferences.edit().putInt("adjust_video_size_index", which).apply();
    }
}