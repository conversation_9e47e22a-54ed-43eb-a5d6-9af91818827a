package com.link.autolink.utils

import android.webkit.JavascriptInterface

// 定义一个监听器接口，用于将事件从 Bridge 传递到我们的控制器
interface WebViewInputListener {
    fun onInputFocused(type: String, currentValue: String)
    fun onInputBlurred()
}

class WebAppInterface(private val listener: WebViewInputListener) {

    @JavascriptInterface
    fun onInputFocused(type: String, currentValue: String) {
        // 当 JS 调用时，通过 listener 通知我们的控制器
        listener.onInputFocused(type, currentValue)
    }

    @JavascriptInterface
    fun onInputBlurred() {
        listener.onInputBlurred()
    }
}