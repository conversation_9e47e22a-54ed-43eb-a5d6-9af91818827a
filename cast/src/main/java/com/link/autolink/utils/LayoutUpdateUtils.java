package com.link.autolink.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.core.content.ContextCompat;

/**
 * 一个工具类，用于根据XML布局文件的定义来动态更新现有View的UI属性。
 */
public final class LayoutUpdateUtils {

    private static final String TAG = "LayoutUpdateUtils";
    private static final String ANDROID_SCHEMA = "http://schemas.android.com/apk/res/android";

    /**
     * 检查一个字符串是否是有效的Android资源引用（例如 "@2131230877"）。
     *
     * @param str 待检查的字符串
     * @return 如果是有效引用则返回 true
     */
    private static boolean isResourceReference(String str) {
        // 原始逻辑是：不为空、不为"null"字符串、且包含"@"符号
        return !TextUtils.isEmpty(str) && !str.equals("null") && str.contains("@");
    }

    /**
     * 读取一个XML布局文件，并将其中的UI属性（背景、图片、文本颜色等）应用到已有的rootView及其子View上。
     *
     * @param context     上下文
     * @param layoutResId 要应用的皮肤布局文件ID，例如 R.layout.main_activity_dark_theme
     * @param rootView    当前界面上已经存在的根视图
     */
    @SuppressLint({"UseCompatLoadingForDrawables"})
    public static void updateUIFromLayoutFile(Context context, int layoutResId, View rootView) {
        // 获取布局文件的XML解析器
        XmlResourceParser parser = context.getResources().getLayout(layoutResId);
        try {
            int eventType = parser.getEventType();
            while (eventType != XmlResourceParser.END_DOCUMENT) {
                // 只处理开始标签事件
                if (eventType == XmlResourceParser.START_TAG) {
                    // 获取 android:id 属性
                    String idAttribute = parser.getAttributeValue(ANDROID_SCHEMA, "id");

                    // 如果ID是有效的资源引用
                    if (isResourceReference(idAttribute)) {
                        // 从字符串中解析出ID的整数值
                        int viewId = Integer.parseInt(idAttribute.replace("@", ""));
                        View targetView = rootView.findViewById(viewId);

                        if (targetView == null) {
                            Log.w(TAG, "Could not find view with ID: " + viewId);
                            // 必须进入下一次循环，否则会处理一个null view导致崩溃
                            eventType = parser.next();
                            continue;
                        }

                        // 获取其他可能的UI属性
                        String backgroundAttribute = parser.getAttributeValue(ANDROID_SCHEMA, "background");
                        String srcAttribute = parser.getAttributeValue(ANDROID_SCHEMA, "src");
                        String textColorAttribute = parser.getAttributeValue(ANDROID_SCHEMA, "textColor");

                        // 1. 更新背景 (通用)
                        if (isResourceReference(backgroundAttribute)) {
                            int backgroundResId = Integer.parseInt(backgroundAttribute.replace("@", ""));
                            if (backgroundResId != 0) {
                                targetView.setBackground(ContextCompat.getDrawable(context, backgroundResId));
                            }
                        }

                        // 2. 更新图片资源 (针对 ImageView 和 AppCompatImageView)
                        if ((targetView instanceof ImageView) && isResourceReference(srcAttribute)) {
                            int srcResId = Integer.parseInt(srcAttribute.replace("@", ""));
                            if (srcResId != 0) {
                                ((ImageView) targetView).setImageDrawable(ContextCompat.getDrawable(context, srcResId));
                            }
                        }

                        // 3. 更新文本颜色 (针对 TextView)
                        if ((targetView instanceof TextView) && isResourceReference(textColorAttribute)) {
                            int colorResId = Integer.parseInt(textColorAttribute.replace("@", ""));
                            // 注意：getColor(id) 已弃用，应使用 ContextCompat.getColor()
                            ((TextView) targetView).setTextColor(ContextCompat.getColor(context, colorResId));
                        }
                    }
                }
                eventType = parser.next();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error parsing layout for UI update", e);
            e.printStackTrace();
        }
    }
}