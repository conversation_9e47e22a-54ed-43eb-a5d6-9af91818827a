package com.link.autolink.presentation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.link.autolink.R;

public class TipLocalBrowserLayout extends RelativeLayout {

    // 本地浏览提示文本视图
    private TextView localBrowsingTextView;

    // 提示布局容器
    private LinearLayout tipLayoutContainer;

    public TipLocalBrowserLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initializeViews(context);
    }

    private void initializeViews(Context context) {
        // 加载布局文件
        View.inflate(context, R.layout.layout_browser_local_tip, this);

        // 初始化视图组件
        tipLayoutContainer = findViewById(R.id.tip_layout_browser_local);
        localBrowsingTextView = findViewById(R.id.tv_tip_local_browsing);
    }

    public void setOnConfirmClickListener(View.OnClickListener onClickListener) {
        localBrowsingTextView.setOnClickListener(onClickListener);
    }

    public void setLocalBrowsingText(String text) {
        localBrowsingTextView.setText(text);
    }

    public void setLocalBrowsingText(int resId) {
        localBrowsingTextView.setText(resId);
    }

    public void showLocalBrowsingTip() {
        tipLayoutContainer.setVisibility(VISIBLE);
    }

    public void hideLocalBrowsingTip() {
        tipLayoutContainer.setVisibility(View.GONE);
    }

    public void setTipLayoutVisible(boolean visible) {
        tipLayoutContainer.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setLocalBrowsingClickable(boolean clickable) {
        localBrowsingTextView.setClickable(clickable);
    }

    public TextView getLocalBrowsingTextView() {
        return localBrowsingTextView;
    }

    public LinearLayout getTipLayoutContainer() {
        return tipLayoutContainer;
    }


}