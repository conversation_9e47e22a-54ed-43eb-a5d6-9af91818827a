package com.link.autolink.presentation

import android.content.Context
import android.os.Bundle

import android.util.Log
import android.view.Display
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.PermissionRequest
import android.webkit.WebView
import android.widget.FrameLayout
import android.widget.RelativeLayout
import com.link.autolink.R
import com.link.autolink.data.local.AppDatabase
import com.link.autolink.presentation.browser.model.BrowserInfo
import com.link.autolink.presentation.widget.BrowserLayout
import com.link.autolink.presentation.widget.CustomWebView
import com.link.autolink.presentation.widget.DisconnectLayout
import com.link.autolink.presentation.widget.TipBrowserLayout
import com.link.autolink.presentation.widget.TipLocalBrowserLayout
import com.link.autolink.presentation.widget.ToastBrowserLayout
import com.link.autolink.utils.DisplayUtils
import com.link.autolink.utils.LayoutUpdateUtils
import com.link.autolink.utils.WebAppInterface
import com.link.autolink.utils.WebViewInputListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CastMirrorPresentation @JvmOverloads constructor(
    context: Context,
    display: Display,
    theme: Int = 0,
    private val inputListener: WebViewInputListener,
    private val isHorizontal: Boolean = true,
    private val displayUtils: DisplayUtils = DisplayUtils(context)
) : MirrorBasePresentation(context, display, theme) {

    lateinit var projectionController: ProjectionController
    lateinit var controlPointView: View
    lateinit var browserLayout: BrowserLayout
    lateinit var tipLocalBrowserLayout: TipLocalBrowserLayout
    lateinit var toastBrowserLayout: ToastBrowserLayout
    lateinit var tipBrowserLayout: TipBrowserLayout
    lateinit var disconnectLayout: DisconnectLayout
    private val displayWidth: Int = display.width
    private val displayHeight: Int = display.height

    // 全屏视频相关
    private var fullscreenContainer: FrameLayout? = null
    private var isVideoFullscreen = false


    private val webViewCallback = object : WebViewCallback {
        override fun getCallbackType(): String {
            return "presentation"
        }

        override fun onStarStatusChanged(isStarred: Boolean) {
            super.onStarStatusChanged(isStarred)
            browserLayout.setSelectStar(isStarred)
        }

        override fun onPageFinished(webView: WebView?) {
            super.onPageFinished(webView)
            browserLayout.setLoadProgress(false, 0)
            browserLayout.setEnableReward(webView?.canGoBack() == true)
            browserLayout.setEnableForward(webView?.canGoForward() == true)
            browserLayout.setEnableHome(true)
            browserLayout.setEnableRefresh(true)
            browserLayout.setEnableStar(true)
            injectInputHandlerScript(webView)
        }

        override fun onBookmarksUpdated(bookmarks: List<BrowserInfo?>?) {
            super.onBookmarksUpdated(bookmarks)
            browserLayout.setBrowserInfoList(bookmarks ?: emptyList())
        }

        override fun onLocalBrowserStatus(enabled: Boolean) {
            super.onLocalBrowserStatus(enabled)
            Log.d(TAG, "CastMirrorPresentation onLocalBrowserStatus:$enabled")
            if (!enabled) {
                tipLocalBrowserLayout.showLocalBrowsingTip()
                return
            }

            // 只有在远程模式且当前 WebView 不是活跃的时候才设置
            if (enabled && projectionController.webView != browserLayout.webView) {
                Log.d(TAG, "CastMirrorPresentation 设置 WebView 为活跃")
                projectionController.setupWebView(browserLayout.webView)
            }
            tipLocalBrowserLayout.hideLocalBrowsingTip()
        }

        override fun onPermissionRequest(request: PermissionRequest) {
            super.onPermissionRequest(request)
            toastBrowserLayout.showToast(R.string.virtual_toast_phone_permissions, 3000L)
            LayoutUpdateUtils.updateUIFromLayoutFile(
                <EMAIL>,
                R.layout.layout_browser_toast,
                tipBrowserLayout.rootView
            )
        }


        override fun onPageLoadStatus(
            isRemote: Boolean,
            webView: WebView?,
            showProgress: Boolean,
            progress: Int
        ) {
            super.onPageLoadStatus(isRemote, webView, showProgress, progress)
            browserLayout.setLoadProgress(showProgress, progress)
            browserLayout.setEnableReward(webView?.canGoBack() == true)
            browserLayout.setEnableForward(webView?.canGoForward() == true)
            browserLayout.setEnableHome(true)
            browserLayout.setEnableRefresh(true)
            browserLayout.setEnableStar(true)
        }

        override fun onPageStarted(isRemote: Boolean, url: String?) {
            super.onPageStarted(isRemote, url)
            Log.d(TAG, "onPageStarted: $isRemote $url")
            if (isRemote) {
                browserLayout.switchView(true)
            } else {
                browserLayout.switchView(false)
            }
        }

        override fun onWebViewReady(webView: WebView?) {
            super.onWebViewReady(webView)
        }

        override fun onNavigationVisibilityChanged(visible: Boolean, url: String?) {
            super.onNavigationVisibilityChanged(visible, url)
        }

        override fun onVideoSizeChanged(which: Int) {
            super.onVideoSizeChanged(which)
            displayUtils.videoSizeIndex = which
            calculateWebViewDimensions()
        }

        override fun onVideoFullscreenEnter(customView: View?) {
            super.onVideoFullscreenEnter(customView)
            Log.d(TAG, "onVideoFullscreenEnter")
            customView?.let { enterFullscreen(it) }
        }

        override fun onVideoFullscreenExit() {
            super.onVideoFullscreenExit()
            Log.d(TAG, "onVideoFullscreenExit")
            exitFullscreen()
        }
    }

    private fun injectInputHandlerScript(webView: WebView?) {
        try {
            // 从 assets 读取 JS 文件内容
            val inputStream = context.assets.open("input_handler.js")
            val script = inputStream.bufferedReader().use { it.readText() }
            // 注入并执行 JS
            webView?.evaluateJavascript(script) { result ->
                Log.d(TAG, "Input handler script injected, result: $result")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to inject optimized JS script", e)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.cast_presentation)
        Log.d(
            "CustomPresentation",
            "Creating presentation - horizontal:$isHorizontal,width:$displayWidth,height:$displayHeight,displayId:${context.resources.displayMetrics}"
        )
        projectionController = ProjectionController.getInstance()
        controlPointView = findViewById(R.id.view_point)
        tipLocalBrowserLayout = findViewById(R.id.tip_local_browser_layout)
        toastBrowserLayout = findViewById(R.id.toast_browser_layout)
        tipBrowserLayout = findViewById(R.id.tip_input_browser_layout)
        disconnectLayout = findViewById(R.id.tip_disconnect_layout)
        browserLayout = findViewById(R.id.bl_remote)
        browserLayout.setAutoHideBrowserNavi(true)
        browserLayout.browserNaviLayout.homeButton.visibility = View.VISIBLE
        browserLayout.browserNaviLayout.sendToPhoneButton.visibility = View.VISIBLE
        browserLayout.showBrowserNavi()
        browserLayout.setShowDivisionLine(true)
        val dimension = context.resources.getDimensionPixelSize(R.dimen.browser_naiv_def_siez)
        if (isHorizontal) {
            val params =
                browserLayout.bookmarkRecyclerView.layoutParams as RelativeLayout.LayoutParams
            params.setMargins(dimension, 0, 0, 0)
            browserLayout.bookmarkRecyclerView.layoutParams = params
            browserLayout.setNaviLayoutParams(0, dimension, -1)
        } else {
            val params =
                browserLayout.bookmarkRecyclerView.layoutParams as RelativeLayout.LayoutParams
            params.setMargins(0, 0, 0, dimension)
            browserLayout.bookmarkRecyclerView.layoutParams = params
            browserLayout.setNaviLayoutParams(2, -1, dimension)
        }
        browserLayout.webView.addJavascriptInterface(
            WebAppInterface(inputListener),
            "AndroidBridge"
        )
        projectionController.setupWebView(browserLayout.webView)
        projectionController.addCallback(webViewCallback)
        browserLayout.setOnClickBrowserListener(object : BrowserLayout.OnBrowserClickListener {
            override fun onBrowserEditTypeClick() {
                super.onBrowserEditTypeClick()
                Log.d(TAG, "onBrowserEditTypeClick")
                tipBrowserLayout.showTipWithAutoHide(3000L)
            }

            override fun onBrowserButtonClick(view: View?) {
                super.onBrowserButtonClick(view)
                when (view?.id) {
                    R.id.forward -> {
                        projectionController.webView.goForward()
                    }

                    R.id.home -> {
                        if (browserLayout.webContainer.visibility != View.VISIBLE) {
                            browserLayout.switchView(true)
                        } else {
                            browserLayout.switchView(false)
                        }
                    }

                    R.id.refresh -> {
                        projectionController.webView.reload()
                    }

                    R.id.reward -> {
                        projectionController.webView.goBack()
                    }

                    R.id.send_to_phone -> {
                        disconnectLayout.showDisconnectTip()
                    }

                    R.id.star -> {
                        projectionController.toggleStarStatus()
                    }
                }
            }
        })
        browserLayout.setOnItemClickListener(object : BrowserLayout.OnItemClickListener {
            override fun onItemClick(
                view: View?,
                isFavorite: Boolean,
                browserInfo: BrowserInfo?
            ) {
                if (isFavorite) {
                    return
                }
                browserInfo?.let {
                    projectionController.processAndLoadUrl(it.url)
                }
            }

        })
        tipLocalBrowserLayout.setOnConfirmClickListener {
            projectionController.enableRemoteBrowser(true)
        }
        tipBrowserLayout.setOnConfirmClickListener {
            projectionController.enableRemoteBrowser(false)
        }
        disconnectLayout.setOnConfirmClickListener {
            disconnectLayout.hideDisconnectTip()
            projectionController.disconnect()
        }
        calculateWebViewDimensions()
        MainScope().launch {
            val list = withContext(Dispatchers.IO) {
                AppDatabase.getInstance(context).browserInfoDao().getAll()
            }
            val browserInfoList =
                list.map { BrowserInfo(it.title, it.url, it.isSelected, it.favicon) }
            browserLayout.setBrowserInfoList(browserInfoList)
        }


    }

    fun calculateWebViewDimensions() {
        // 如果处于全屏模式，不调整 WebView 尺寸
        if (isVideoFullscreen) {
            return
        }

        var currentWidth = displayWidth
        val currentHeight = displayHeight
        val aspectRatioMode = displayUtils.videoSizeIndex

        if (currentWidth > currentHeight) {
            val targetWidth = when (aspectRatioMode) {
                1 -> (currentHeight * 1.7777778f).toInt() // 16:9
                2 -> (currentHeight * 1.3333334f).toInt() // 4:3
                3 -> (currentHeight * 2.0f).toInt()       // 2:1
                4 -> (currentHeight * 1.0f).toInt()       // 1:1
                else -> currentWidth                       // 0: Original or default
            }
            if (targetWidth <= currentWidth) {
                currentWidth = targetWidth
            }
        }

        // Apply the final dimensions
        val finalWidth = currentWidth
        val finalHeight = currentHeight
        (browserLayout.webView.layoutParams as FrameLayout.LayoutParams).also {
            it.width = finalWidth
            it.height = finalHeight
        }.let {
            browserLayout.webView.layoutParams = it
        }
    }

    /**
     * 进入全屏模式
     */
    private fun enterFullscreen(customView: View) {
        Log.d(TAG, "enterFullscreen")
        isVideoFullscreen = true

        // 创建全屏容器
        if (fullscreenContainer == null) {
            fullscreenContainer = FrameLayout(context).apply {
                setBackgroundColor(android.graphics.Color.BLACK)
                layoutParams = FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                )
            }
        }

        // 添加自定义视频视图到全屏容器
        fullscreenContainer?.addView(
            customView, FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        )

        // 将全屏容器添加到根视图
        val rootView = findViewById<ViewGroup>(android.R.id.content)
        rootView.addView(fullscreenContainer)

        // 只隐藏主要的浏览器界面，其他提示界面保持原状态
        browserLayout.visibility = View.GONE

        // 注意：不强制隐藏提示界面，让它们保持当前状态
        // 这样在退出全屏时也不会意外显示不该显示的界面
    }

    /**
     * 退出全屏模式
     */
    private fun exitFullscreen() {
        Log.d(TAG, "exitFullscreen")
        isVideoFullscreen = false

        // 移除全屏容器
        fullscreenContainer?.let { container ->
            val rootView = findViewById<ViewGroup>(android.R.id.content)
            rootView.removeView(container)
            container.removeAllViews()
        }

        // 恢复浏览器主界面
        browserLayout.visibility = View.VISIBLE

        // 检查当前模式，如果是本地模式则显示提示
        if (!projectionController.isRemote()) {
            Log.d(TAG, "exitFullscreen: 当前为本地模式，显示 TipLocalBrowserLayout")
            tipLocalBrowserLayout.showLocalBrowsingTip()
        }

        // 重新计算 WebView 尺寸
        calculateWebViewDimensions()
    }


    override fun dismiss() {
        super.dismiss()
        projectionController.removeCallback(webViewCallback)
    }


    fun updateWebViewInput(text: String) {
        val escapedText = text.replace("'", "\\'") // 简单转义
        val js = """
        (function() {
            let element = document.activeElement;
            if (element && (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.isContentEditable)) {
                // 步骤 1: 设置输入框的值，和以前一样
                element.value = '$escapedText';

                // 步骤 2: 创建一个 'input' 事件
                // 'bubbles: true' 很重要，它让事件可以像真实事件一样在DOM中冒泡
                const inputEvent = new Event('input', {
                    bubbles: true,
                    cancelable: true
                });

                // 步骤 3: 将创建的事件派发给输入框
                // 这是“通知”网页值已更改的关键一步
                element.dispatchEvent(inputEvent);
            }
        })();
    """.trimIndent()
        browserLayout.webView.post { // 确保在UI线程执行
            browserLayout.webView.evaluateJavascript(js, null)
        }
    }

    fun blurWebViewInput() {
        val js = "if(document.activeElement) { document.activeElement.blur(); }"
        browserLayout.webView.post {
            browserLayout.webView.evaluateJavascript(js, null)
        }
    }

    fun submitWebViewInput() {
        val js = """
            (function() {
                let element = document.activeElement;
                if (element && (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA')) {
                    // 创建一个键盘事件
                    const enterEvent = new KeyboardEvent('keydown', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    });
                    // 将事件派发给当前元素
                    element.dispatchEvent(enterEvent);
                    // 派发后可以选择性地让其失焦
                    element.blur(); 
                }
            })();
        """.trimIndent()

        browserLayout.webView.post {
            browserLayout.webView.evaluateJavascript(js, null)
        }
    }


    companion object {
        private const val TAG = "CastMirrorPresentation"
    }

    init {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        window?.addFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        window?.setType(WindowManager.LayoutParams.TYPE_PRIVATE_PRESENTATION)
        window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
//        window?.addFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
//        window?.addFlags(WindowManager.LayoutParams.FLAG_LOCAL_FOCUS_MODE)
    }
}