package com.link.autolink.presentation.widget;


import android.util.SparseArray;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class BaseViewHolder extends RecyclerView.ViewHolder {
    // 视图缓存，使用SparseArray提高查找效率
    private SparseArray<View> viewCache;

    // 根视图
    public View itemView;

    public BaseViewHolder(View itemView) {
        super(itemView);
        this.itemView = itemView;
        this.viewCache = new SparseArray<>();
    }

    @SuppressWarnings("unchecked")
    public <T extends View> T findViewById(int viewId) {
        // 先从缓存中查找
        T cachedView = (T) viewCache.get(viewId);
        if (cachedView != null) {
            return cachedView;
        }

        // 缓存中没有，从根视图中查找
        T foundView = (T) itemView.findViewById(viewId);
        if (foundView != null) {
            // 将找到的视图放入缓存
            viewCache.put(viewId, foundView);
        }

        return foundView;
    }


    public void setOnClickListener(View.OnClickListener listener) {
        itemView.setOnClickListener(listener);
    }

    public void setOnLongClickListener(View.OnLongClickListener listener) {
        itemView.setOnLongClickListener(listener);
    }

    public void setOnLongClickListener(int viewId, View.OnLongClickListener listener) {
        View view = findViewById(viewId);
        if (view != null) {
            view.setOnLongClickListener(listener);
        }
    }

    public void setVisibility(int viewId, int visibility) {
        View view = findViewById(viewId);
        if (view != null) {
            view.setVisibility(visibility);
        }
    }

    public void setEnabled(int viewId, boolean enabled) {
        View view = findViewById(viewId);
        if (view != null) {
            view.setEnabled(enabled);
        }
    }

    public void clearViewCache() {
        if (viewCache != null) {
            viewCache.clear();
        }
    }

    /**
     * 获取缓存的视图数量
     * @return 缓存的视图数量
     */
    public int getCachedViewCount() {
        return viewCache != null ? viewCache.size() : 0;
    }

    // Getter 方法

    public View getItemView() {
        return itemView;
    }

    public SparseArray<View> getViewCache() {
        return viewCache;
    }
}
