package com.link.autolink.presentation;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.webkit.PermissionRequest;
import android.webkit.WebChromeClient;
import android.webkit.WebHistoryItem;
import android.webkit.WebResourceRequest;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.elvishew.xlog.XLog;
import com.link.autolink.CastWifiP2pManager;
import com.link.autolink.R;
import com.link.autolink.data.local.AppDatabase;
import com.link.autolink.data.local.BrowserInfoEntity;
import com.link.autolink.presentation.browser.model.BrowserInfo;
import com.link.autolink.utils.SearchEngineRepository;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

public final class ProjectionController extends AbstractWebViewCallbackManager<WebViewCallback> {

    private static ProjectionController instance;
    private Context context;
    private WebView webView;
    private boolean isConnected = false;
    private boolean isRemote = false;
    private String currentUrl;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private static final String URL_REGEX = "^(http(s)?://)?([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?$";
    private static final String TAG = "ProjectionController";
    private SearchEngineRepository searchEngineRepository;

    // 全屏视频相关
    private View customView;
    private WebChromeClient.CustomViewCallback customViewCallback;
    private FrameLayout fullscreenContainer;
    private ViewGroup originalParent;

    // 防止重复调用的标识
    private String lastPageStartedUrl = "";
    private long lastPageStartedTime = 0;

    private ProjectionController() {
    }

    public static ProjectionController getInstance() {
        if (instance == null) {
            instance = new ProjectionController();
        }
        return instance;
    }

    public void initialize(Context context) {
        this.context = context;
        searchEngineRepository = new SearchEngineRepository(context);
    }


    @SuppressLint("SetJavaScriptEnabled")
    public void setupWebView(WebView webView) {
        XLog.tag(TAG).enableStackTrace(5).d("setupWebView: " + webView.hashCode() + " (current: " +
            (this.webView != null ? this.webView.hashCode() : "null") + ")");

        // 如果是同一个 WebView，不需要重复设置
        if (this.webView == webView) {
            Log.d(TAG, "setupWebView: 相同的 WebView，跳过设置");
            return;
        }

        // 如果已经有 WebView，先清理旧的
        if (this.webView != null) {
            Log.w(TAG, "setupWebView: 替换现有 WebView " + this.webView.hashCode() + " -> " + webView.hashCode());
            // 清理旧 WebView 的客户端设置
            this.webView.setWebViewClient(null);
            this.webView.setWebChromeClient(null);
        }

        this.webView = webView;
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d("ProjectionController", "onPageStarted: " + url);

                // 防止短时间内重复调用相同URL的回调
                long currentTime = System.currentTimeMillis();
                if (url != null && url.equals(lastPageStartedUrl) &&
                    (currentTime - lastPageStartedTime) < 500) { // 500ms内的重复调用忽略
                    Log.d(TAG, "onPageStarted: 忽略重复调用 " + url);
                    return;
                }
                lastPageStartedUrl = url;
                lastPageStartedTime = currentTime;

                WebHistoryItem currentItem = webView.copyBackForwardList().getCurrentItem();
                if (currentItem != null) {
                    Executors.newSingleThreadExecutor().execute(() -> {
                        AppDatabase database = AppDatabase.getInstance(context);
                        BrowserInfoEntity browserInfoEntity = database.browserInfoDao().findByUrl(currentItem.getOriginalUrl());
                        Log.d(TAG, "onPageStarted:" + browserInfoEntity + "--" + currentItem.getOriginalUrl());
                        handler.post(() -> {
                            synchronized (lock) {
                                for (WebViewCallback callback : callbacks) {
                                    callback.onStarStatusChanged(browserInfoEntity != null);
                                }
                            }
                        });
                    });
                }
                synchronized (lock) {
                    for (WebViewCallback callback : callbacks) {
                        callback.onPageLoadStatus(isRemote, view, true, 0);
                    }
                }
                handler.post(() -> {
                    synchronized (lock) {
                        for (WebViewCallback callback : callbacks) {
                            callback.onPageStarted(isRemote, url);
                        }
                    }
                });

            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                synchronized (lock) {
                    for (WebViewCallback callback : callbacks) {
                        callback.onPageLoadStatus(isRemote, view, false, 100);
                    }
                }
                handler.post(() -> {
                    for (WebViewCallback callback : callbacks) {
                        callback.onPageFinished(view);
                    }
                });
                WebHistoryItem currentItem = webView.copyBackForwardList().getCurrentItem();
                if (currentItem == null || currentItem.getFavicon() != null) {
                    return;
                }
                webView.evaluateJavascript("(function() { return $('link[rel~=\"icon\"]').attr('href'); })();", value -> {
                    Log.d(TAG, "Favicon URL from JS: " + value);
                    // 可以在这里处理获取到的 favicon链接
                });
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                String uri = request.getUrl().toString();
                return !(uri.startsWith("https://") || uri.startsWith("http://"));
            }
        });
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                Log.d(TAG, "WebChromeClient onProgressChanged:" + newProgress);
                if (newProgress == 100) {
                    synchronized (lock) {
                        for (WebViewCallback callback : callbacks) {
                            callback.onPageLoadStatus(isRemote, view, false, 100);
                        }
                    }
                } else {
                    synchronized (lock) {
                        for (WebViewCallback callback : callbacks) {
                            callback.onPageLoadStatus(isRemote, view, true, newProgress);
                        }
                    }
                }
            }

            @Override
            public void onPermissionRequest(PermissionRequest request) {
                request.grant(request.getResources());
            }

            @Override
            public void onShowCustomView(View view, CustomViewCallback callback) {
                super.onShowCustomView(view, callback);
                Log.d(TAG, "onShowCustomView - 进入全屏模式");

                if (customView != null) {
                    // 如果已经有全屏视图，先隐藏
                    onHideCustomView();
                }

                customView = view;
                customViewCallback = callback;

                // 通知回调进入全屏模式
                synchronized (lock) {
                    for (WebViewCallback webViewCallback : callbacks) {
                        webViewCallback.onVideoFullscreenEnter(view);
                    }
                }
            }

            @Override
            public void onHideCustomView() {
                super.onHideCustomView();
                Log.d(TAG, "onHideCustomView - 退出全屏模式");

                if (customView == null) {
                    return;
                }

                // 通知回调退出全屏模式
                synchronized (lock) {
                    for (WebViewCallback webViewCallback : callbacks) {
                        webViewCallback.onVideoFullscreenExit();
                    }
                }

                // 清理全屏相关变量
                if (customViewCallback != null) {
                    customViewCallback.onCustomViewHidden();
                    customViewCallback = null;
                }
                customView = null;
            }
        });
    }

    public WebView getWebView() {
        return webView;
    }



    public void setConnected(boolean connected) {
        isConnected = connected;
    }

    public boolean isConnected() {
        return isConnected;
    }

    public boolean isRemote() {
        return isRemote;
    }

    public void enableRemoteBrowser(boolean enable) {
        if (this.isRemote != enable) {
            this.isRemote = enable;

            if (enable) {
                Log.d("ProjectionController", "Enabling remote browser mode");
                // 启用远程浏览器模式 - 开始录音等操作
                // startAudioRecording();
            } else {
                Log.d("ProjectionController", "Disabling remote browser mode");
                // 关闭远程浏览器模式 - 停止录音等操作
                // stopAudioRecording();

                // 如果当前有全屏视频，强制退出全屏
                if (customView != null) {
                    Log.d(TAG, "enableRemoteBrowser: 强制退出全屏模式");
                    // 通知所有回调退出全屏
                    synchronized (lock) {
                        for (WebViewCallback callback : callbacks) {
                            callback.onVideoFullscreenExit();
                        }
                    }

                    // 清理全屏状态
                    if (customViewCallback != null) {
                        customViewCallback.onCustomViewHidden();
                        customViewCallback = null;
                    }
                    customView = null;
                }
            }
            if (webView != null) {
                WebHistoryItem currentItem = webView.copyBackForwardList().getCurrentItem();
                String url = currentItem != null ? currentItem.getUrl() : currentUrl;
                if (TextUtils.isEmpty(url)) {
                    return;
                }
                processAndLoadUrl(url);
            }
        }
    }

    public String processAndLoadUrl(String inputUrl) {
        // 实现m5311k的完整URL处理逻辑

        // 1. 权限和状态检查（占位符实现）
        boolean isProjecting = isProjecting(); // 需要实现isProjecting方法
        setMediaMute(isProjecting && isRemote);

        // 2. 录音权限检查（占位符实现）
//        if (!hasRecordAudioPermission()) {
//            notifyPermissionRequired(10);
//        }

        // 3. URL验证和搜索引擎转换
        if (TextUtils.isEmpty(inputUrl) || !isValidUrl(inputUrl)) {
            // 不是有效的URL，转换为搜索引擎查询
            int searchEngineIndex = getSearchEngineIndex(); // 需要实现搜索引擎选择

            switch (searchEngineIndex) {
                case 0: // Google (默认)
                    currentUrl = buildSearchUrl("https://www.google.com", "search", "q", inputUrl);
                    break;
                case 1: // Bing
                    currentUrl = buildSearchUrl("https://www.bing.com", "search", "q", inputUrl);
                    break;
                case 2: // Yahoo
                    currentUrl = buildSearchUrl("https://search.yahoo.com", "search", "p", inputUrl);
                    break;
                case 3: // 百度
                    currentUrl = buildSearchUrl("https://www.baidu.com", "s", "wd", inputUrl);
                    break;
                case 4: // Yandex
                    currentUrl = buildSearchUrl("https://yandex.com", "search", "text", inputUrl);
                    break;
                case 5: // DuckDuckGo
                    currentUrl = buildSearchUrl("https://duckduckgo.com", "", "q", inputUrl);
                    break;
                default:
                    currentUrl = buildSearchUrl("https://www.google.com", "search", "q", inputUrl);
            }
        } else {
            currentUrl = inputUrl;
        }

        // 5. 异步加载URL（模拟RunnableC2119f的功能）
        handler.post(() -> {
            // 通知搜索状态变化
            synchronized (lock) {
                for (WebViewCallback callback : callbacks) {
                    callback.onLocalBrowserStatus(isRemote);
                }
            }

            // 加载URL
            if (webView != null) {
                webView.loadUrl(currentUrl);
            }
        });
        return currentUrl;
    }

    // 辅助方法 - 需要实现
    private boolean isProjecting() {
        // 实现投影状态检查逻辑
        return true;
    }

    private void setMediaMute(boolean mute) {
        // 实现媒体静音设置逻辑
    }

    private boolean hasRecordAudioPermission() {
        // 实现录音权限检查逻辑
        return false;
    }

    private void notifyPermissionRequired(int permissionType) {
        // 实现权限通知逻辑
    }

    private boolean isValidUrl(String url) {
        // 实现URL验证逻辑
        return android.util.Patterns.WEB_URL.matcher(url).matches();
    }

    private int getSearchEngineIndex() {
        // 实现搜索引擎选择逻辑（默认返回Google）
        return searchEngineRepository.getSearchEngineIndex();
    }

    private String buildSearchUrl(String baseUrl, String path, String queryParam, String query) {
        android.net.Uri.Builder builder = android.net.Uri.parse(baseUrl).buildUpon();
        if (!TextUtils.isEmpty(path)) {
            builder.appendPath(path);
        }
        builder.appendQueryParameter(queryParam, query);
        return builder.build().toString();
    }

    public void disconnect() {
        isConnected = false;
        CastWifiP2pManager.INSTANCE.disconnect();
    }

    public void reconnect() {
        // 重新连接的具体实现
        //todo
        isConnected = true;
    }

    public void toggleStarStatus() {
        WebHistoryItem currentItem = webView.copyBackForwardList().getCurrentItem();
        if (currentItem != null) {
            Executors.newSingleThreadExecutor().execute(() -> {
                AppDatabase database = AppDatabase.getInstance(context);
                BrowserInfoEntity browserInfoEntity = database.browserInfoDao().findByUrl(currentItem.getOriginalUrl());
                if (browserInfoEntity != null) {
                    Log.d(TAG, "toggleStarStatus: delete bookmark");
                    database.browserInfoDao().deleteByUrl(browserInfoEntity.url);
                } else {
                    Log.d(TAG, "toggleStarStatus: add bookmark");
                    Bitmap favicon = currentItem.getFavicon();
                    if (favicon == null) {
                        favicon = BitmapFactory.decodeResource(context.getResources(), R.drawable.ic_item_def_favicon);
                    }
                    database.browserInfoDao().insert(new BrowserInfoEntity(currentItem.getTitle(), currentItem.getUrl(), favicon, false));
                }
                handler.post(() -> {
                    synchronized (lock) {
                        for (WebViewCallback callback : callbacks) {
                            callback.onStarStatusChanged(browserInfoEntity == null);
                        }
                    }
                });
                updateBookmarks();
            });

        }
    }

    public void updateBookmarks() {
        Executors.newSingleThreadExecutor().execute(() -> {
            AppDatabase database = AppDatabase.getInstance(context);
            List<BrowserInfoEntity> list = database.browserInfoDao().getAll();
            List<BrowserInfo> browserInfoList = list.stream()
                    .map(entity ->
                            new BrowserInfo(entity.title, entity.url, entity.isSelected,entity.favicon)
                    )
                    .collect(Collectors.toList());
            browserInfoList.forEach(browserInfo -> {
                Log.d(TAG, "updateBookmarks:" + browserInfo.selected);
            });
            handler.post(() -> {
                synchronized (lock) {
                    for (WebViewCallback callback : callbacks) {
                        callback.onBookmarksUpdated(browserInfoList);
                    }
                }
            });
        });
    }

    public void updateVideoSize(int which) {
        synchronized (lock) {
            for (WebViewCallback callback : callbacks) {
                callback.onVideoSizeChanged(which);
            }
        }
    }
}