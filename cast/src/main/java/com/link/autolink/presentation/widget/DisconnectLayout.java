package com.link.autolink.presentation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.link.autolink.R;

public class DisconnectLayout extends LinearLayout {

    // 确认按钮
    private TextView confirmButton;

    // 提示布局容器
    private LinearLayout tipLayoutContainer;

    // 取消按钮
    private TextView cancelButton;

    public DisconnectLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initializeViews(context);
    }

    private void initializeViews(Context context) {
        // 加载布局文件
        View.inflate(context, R.layout.layout_disconnect_tip, this);

        // 初始化视图组件
        tipLayoutContainer = findViewById(R.id.tip_layout_disconnect);
        confirmButton = findViewById(R.id.tv_disconnect_confirm);
        cancelButton = findViewById(R.id.tv_disconnect_cancel);
        hideDisconnectTip();

        // 设置取消按钮点击监听器
        setupCancelButton();
    }

    private void setupCancelButton() {
        cancelButton.setOnClickListener(v -> hideDisconnectTip());
    }

    public void setOnConfirmClickListener(View.OnClickListener onClickListener) {
        confirmButton.setOnClickListener(onClickListener);
    }

    public void setOnCancelClickListener(View.OnClickListener onClickListener) {
        cancelButton.setOnClickListener(onClickListener);
    }

    public void showDisconnectTip() {
        tipLayoutContainer.setVisibility(View.VISIBLE);
    }

    public void hideDisconnectTip() {
        tipLayoutContainer.setVisibility(View.GONE);
    }

    public void setConfirmButtonText(String text) {
        confirmButton.setText(text);
    }

    public void setCancelButtonText(String text) {
        cancelButton.setText(text);
    }

    public TextView getConfirmButton() {
        return confirmButton;
    }

    public TextView getCancelButton() {
        return cancelButton;
    }

    public LinearLayout getTipLayoutContainer() {
        return tipLayoutContainer;
    }

}