package com.link.autolink.presentation;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public abstract class AbstractWebViewCallbackManager<V> {

    protected final Object lock = new Object();
    protected final List<V> callbacks = new ArrayList<>();
    protected final Handler handler = new Handler(Looper.getMainLooper());

    public final void addCallback(V callback) {
        Log.d("w.feng", "addCallback: ");
        synchronized (lock) {
            if (callback != null && !callbacks.contains(callback)) {
                callbacks.add(callback);
            }
        }
    }

    public final void addCallbackOnMainThread(V callback) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            addCallback(callback);
        } else {
            handler.post(() -> addCallback(callback));
        }
    }

    public final void removeCallback(V callback) {
        synchronized (lock) {
            if (callback != null && callbacks.contains(callback)) {
                callbacks.remove(callback);
            }
        }
    }
}

