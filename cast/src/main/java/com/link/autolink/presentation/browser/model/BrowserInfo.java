package com.link.autolink.presentation.browser.model;

import android.graphics.Bitmap;

import java.io.ByteArrayOutputStream;

public class BrowserInfo {
    public byte[] favicon;
    public boolean selected;
    public String title;
    public String url;
    
    public BrowserInfo(String title, String url,boolean selected, Bitmap bitmap) {
        this.title = title;
        this.url = url;
        this.selected = selected;
        this.favicon = img(bitmap);
    }

    private static byte[] img(Bitmap bitmap){
        if (bitmap != null) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } else {
            return null;
        }
    }

    @Override
    public String toString() {
        return "BrowserInfo{" +
                ", selected=" + selected +
                ", title='" + title + '\'' +
                ", url='" + url + '\'' +
                '}';
    }
}