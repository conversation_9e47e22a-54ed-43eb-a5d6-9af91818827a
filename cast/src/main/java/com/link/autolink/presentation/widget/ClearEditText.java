package com.link.autolink.presentation.widget;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.RelativeLayout;

import com.link.autolink.R;

/**
 * 带清除按钮的搜索输入框组件
 * 支持自动显示/隐藏清除按钮，以及软键盘管理
 */
public class ClearEditText extends RelativeLayout {

    // 搜索输入框
    private EditText searchEditText;

    // 清除按钮
    private ImageButton clearButton;

    // 输入法管理器
    private InputMethodManager inputMethodManager;

    public ClearEditText(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        initializeComponents(context);
    }

    /**
     * 初始化组件
     */
    private void initializeComponents(Context context) {
        // 获取输入法管理器
        inputMethodManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);

        // 加载布局
        View.inflate(context, R.layout.edittext_search, this);

        // 初始化视图
        initializeViews();

        // 设置监听器
        setupListeners();
    }

    /**
     * 初始化视图组件
     */
    private void initializeViews() {
        searchEditText = findViewById(R.id.et_search);
        clearButton = findViewById(R.id.ivBtn_delete);
    }

    /**
     * 设置监听器
     */
    private void setupListeners() {
        // 设置清除按钮点击监听器
        clearButton.setOnClickListener(v -> clearText());

        // 设置焦点变化监听器
        searchEditText.setOnFocusChangeListener((view, hasFocus) -> {
            EditText editText = (EditText) view;
            updateClearButtonVisibility(editText.getText());

            if (hasFocus) {
                showSoftKeyboard(editText);
            }
        });

        // 设置文本变化监听器
        searchEditText.addTextChangedListener(new TextChangeWatcher());
    }

    /**
     * 显示软键盘
     */
    private void showSoftKeyboard(EditText editText) {
        if (inputMethodManager != null) {
            inputMethodManager.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
        }
    }

    /**
     * 隐藏软键盘
     */
    public void hideSoftKeyboard() {
        if (inputMethodManager != null && searchEditText != null) {
            inputMethodManager.hideSoftInputFromWindow(searchEditText.getWindowToken(), 0);
        }
    }

    /**
     * 文本变化监听器
     */
    private class TextChangeWatcher implements TextWatcher {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // 文本变化前
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            updateClearButtonVisibility(s);
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 文本变化后
        }
    }

    /**
     * 更新清除按钮的可见性
     * @param text 当前文本内容
     */
    public void updateClearButtonVisibility(CharSequence text) {
        boolean hasFocus = searchEditText.hasFocus();
        int padding = (int) getResources().getDimension(R.dimen.et_search_padding);
        boolean shouldShowClear = hasFocus && text != null && text.length() > 0;

        // 设置清除按钮可见性
        clearButton.setVisibility(shouldShowClear ? View.VISIBLE : View.GONE);

        // 调整输入框内边距
        searchEditText.setPadding(padding, 0, shouldShowClear ? 0 : padding, 0);
    }

    /**
     * 清除输入框内容
     */
    public void clearText() {
        searchEditText.setText("");
    }

    /**
     * 设置输入框文本
     * @param text 文本内容
     */
    public void setText(String text) {
        searchEditText.setText(text);
    }

    /**
     * 获取输入框文本
     * @return 文本内容
     */
    public String getText() {
        return searchEditText.getText().toString();
    }

    /**
     * 设置提示文本
     * @param hint 提示文本
     */
    public void setHint(String hint) {
        searchEditText.setHint(hint);
    }

    /**
     * 设置提示文本资源
     * @param resId 字符串资源ID
     */
    public void setHint(int resId) {
        searchEditText.setHint(resId);
    }

    /**
     * 请求焦点并显示键盘
     */
    public void requestFocusAndShowKeyboard() {
        searchEditText.requestFocus();
        showSoftKeyboard(searchEditText);
    }

    /**
     * 清除焦点并隐藏键盘
     */
    public void clearFocusAndHideKeyboard() {
        searchEditText.clearFocus();
        hideSoftKeyboard();
    }

    // Getter 方法

    /**
     * 获取搜索输入框
     * @return EditText实例
     */
    public EditText getSearchEditText() {
        return searchEditText;
    }

    /**
     * 获取搜索输入框（兼容原方法名）
     * @return EditText实例
     */
    public EditText getEtSearch() {
        return searchEditText;
    }

    /**
     * 获取清除按钮
     * @return ImageButton实例
     */
    public ImageButton getClearButton() {
        return clearButton;
    }
}
