package com.link.autolink.presentation.widget;


import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public abstract class BaseRecyclerViewAdapter<T> extends RecyclerView.Adapter<BaseViewHolder> {
    // 上下文对象
    protected Context context;

    // 布局资源ID
    protected int layoutResourceId;

    // 数据列表
    public List<T> dataList;

    public BaseRecyclerViewAdapter(Context context, int layoutResourceId, List<T> dataList) {
        this.context = context;
        this.layoutResourceId = layoutResourceId;
        this.dataList = dataList;
    }

    @Override
    public int getItemCount() {
        return dataList != null ? dataList.size() : 0;
    }

    @Override
    public void onBindViewHolder(BaseViewHolder holder, int position) {
        if (dataList != null && position >= 0 && position < dataList.size()) {
            bindViewHolder(holder, dataList.get(position), position);
        }
    }

    @Override
    public BaseViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new BaseViewHolder(LayoutInflater.from(context).inflate(layoutResourceId, parent, false));
    }

    public abstract void bindViewHolder(BaseViewHolder holder, T item, int position);

    public T getItem(int position) {
        if (dataList != null && position >= 0 && position < dataList.size()) {
            return dataList.get(position);
        }
        return null;
    }

    public void setDataList(List<T> newDataList) {
        this.dataList = newDataList;
        notifyDataSetChanged();
    }

    public void addItem(T item) {
        if (dataList != null) {
            dataList.add(item);
            notifyItemInserted(dataList.size() - 1);
        }
    }

    public void addItem(int position, T item) {
        if (dataList != null && position >= 0 && position <= dataList.size()) {
            dataList.add(position, item);
            notifyItemInserted(position);
        }
    }

    public void removeItem(int position) {
        if (dataList != null && position >= 0 && position < dataList.size()) {
            dataList.remove(position);
            notifyItemRemoved(position);
        }
    }

    public void removeItem(T item) {
        if (dataList != null) {
            int position = dataList.indexOf(item);
            if (position >= 0) {
                removeItem(position);
            }
        }
    }

    public void clearData() {
        if (dataList != null) {
            int size = dataList.size();
            dataList.clear();
            notifyItemRangeRemoved(0, size);
        }
    }

    public void updateItem(int position, T item) {
        if (dataList != null && position >= 0 && position < dataList.size()) {
            dataList.set(position, item);
            notifyItemChanged(position);
        }
    }

    public boolean isEmpty() {
        return dataList == null || dataList.isEmpty();
    }

    public Context getContext() {
        return context;
    }

    public int getLayoutResourceId() {
        return layoutResourceId;
    }

    public List<T> getDataList() {
        return dataList;
    }


}
