package com.link.autolink.presentation;

import android.content.Context;
import android.content.res.Configuration;
import android.util.Log;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public class AutoFitGridLayoutManager extends GridLayoutManager {

    private final int portraitColumnWidth;
    private final int landscapeColumnWidth;

    /**
     * @param context                  Context
     * @param portraitColumnWidthInDp  竖屏时期望的列宽 (单位: dp)
     * @param landscapeColumnWidthInDp 横屏时期望的列宽 (单位: dp)
     */
    public AutoFitGridLayoutManager(Context context, int portraitColumnWidthInDp, int landscapeColumnWidthInDp) {
        super(context, 1);
        this.portraitColumnWidth = dpToPx(context, portraitColumnWidthInDp);
        this.landscapeColumnWidth = dpToPx(context, landscapeColumnWidthInDp);
    }

    private int dpToPx(Context context, int dp) {
        return Math.round(dp * context.getResources().getDisplayMetrics().density);
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        // 1. 直接从 LayoutManager 获取 RecyclerView 的当前尺寸
        int width = getWidth();
        int height = getHeight();

        // 在布局尚未完成时，宽高可能为0，直接跳过
        if (width <= 0 || height <= 0) {
            super.onLayoutChildren(recycler, state);
            return;
        }

        // 2. 根据 RecyclerView 的实际宽高比，判断其有效方向
        //   (不再需要 getContext())
        boolean isEffectivelyPortrait = height > width;
        Log.d("w.feng", "onLayoutChildren: "+isEffectivelyPortrait);

        // 3. 根据有效方向选择合适的列宽
        int currentColumnWidth = isEffectivelyPortrait
                ? portraitColumnWidth
                : landscapeColumnWidth;

        // 4. 计算逻辑保持不变
        //   对于垂直滚动的网格，我们总是基于宽度来计算列数
        if (currentColumnWidth > 0 && getOrientation() == VERTICAL) {
            int totalSpace = width - getPaddingRight() - getPaddingLeft();
            int newSpanCount = Math.max(1, totalSpace / currentColumnWidth);
            if (newSpanCount != getSpanCount()) {
                setSpanCount(newSpanCount);
            }
        }

        super.onLayoutChildren(recycler, state);
    }
}