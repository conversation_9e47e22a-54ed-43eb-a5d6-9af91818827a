package com.link.autolink.presentation.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.webkit.WebView;
import android.widget.CheckBox;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestBuilder;
import com.bumptech.glide.RequestManager;
import com.elvishew.xlog.XLog;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.link.autolink.R;
import com.link.autolink.presentation.AutoFitGridLayoutManager;
import com.link.autolink.presentation.browser.model.BrowserInfo;

import java.util.List;

public class BrowserLayout extends FrameLayout implements View.OnClickListener {
    private static final String TAG = "BookmarkAdapter";
    // 收藏夹布局容器
    private RelativeLayout favoriteLayout;

    // 收藏夹列表
    private RecyclerView bookmarkRecyclerView;

    // 空收藏夹提示文本
    private TextView emptyFavoriteTextView;

    // 底部操作栏
    private LinearLayout bottomLayout;

    // 删除按钮
    private TextView deleteTextView;

    // 全选按钮
    private TextView allSelectTextView;

    // 是否自动隐藏导航栏
    private boolean autoHideBrowserNavi;

    // 是否显示导航栏
    private boolean showBrowserNavi;

    // 是否显示分割线
    private boolean showDivisionLine;

    // 收藏夹数据列表
    private List<BrowserInfo> browserInfoList;

    // 自动隐藏任务
    private AutoHideTask autoHideTask;

    // 手势检测器
    private GestureDetector gestureDetector;

    // 主Web容器
    private RelativeLayout webContainer;

    // 手机Web容器
    private RelativeLayout phoneWebContainer;

    // WebView容器
    private FrameLayout webViewContainer;

    // WebView实例
    private CustomWebView webView;

    // 加载进度条
    private LinearProgressIndicator loadProgressIndicator;

    // 浏览器导航布局
    private BrowserNavLayout browserNaviLayout;

    // 分割线视图
    private View divisionLineView;

    // 首页按钮
    private AppCompatImageView homeButton;

    // 后退按钮
    private AppCompatImageView rewardButton;

    // 前进按钮
    private AppCompatImageView forwardButton;

    // 发送到远程设备按钮
    private AppCompatImageView sendToRemoteButton;

    // 发送到手机按钮
    private AppCompatImageView sendToPhoneButton;

    // 刷新按钮
    private AppCompatImageView refreshButton;

    // 收藏按钮
    private AppCompatImageView starButton;

    // 浏览器点击监听器
    private OnBrowserClickListener browserClickListener;

    // 项目点击监听器
    private OnItemClickListener itemClickListener;

    // 收藏夹适配器
    private BookmarkAdapter bookmarkAdapter;

    // 手势优化相关变量
    private static final float MIN_FLING_VELOCITY_DP = 150f; // 最小快速滑动速度 (dp/s)
    private static final float MIN_SCROLL_DISTANCE_DP = 15f; // 最小滑动距离 (dp)
    private static final float DIRECTION_THRESHOLD = 0.75f;  // 方向判断阈值 (cos 41°) - 适中的水平判断
    private static final float MIN_HORIZONTAL_DISTANCE_DP = 25f; // 最小水平滑动距离 (dp)
    private static final float EDGE_ZONE_WIDTH_DP = 45f;     // 左侧边缘触发区域宽度 (dp)
    private static final float MAX_VERTICAL_DEVIATION_DP = 15f; // 最大垂直偏移 (dp)
    private static final long DEBOUNCE_DELAY_MS = 150L;      // 防抖延迟时间
    private static final long ANIMATION_DURATION_MS = 250L;  // 动画持续时间

    // 手势状态
    private boolean isAnimating = false;
    private boolean pendingShow = false;
    private boolean pendingHide = false;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private Runnable debounceRunnable;

    // 屏幕密度相关
    private float density;
    private float minScrollDistance;
    private float minFlingVelocity;
    private float minHorizontalDistance;
    private float edgeZoneWidth;
    private float maxVerticalDeviation;

    public void setWebView(@Nullable CustomWebView webView) {
        this.webView = webView;
    }

    private class AutoHideTask implements Runnable {

        @Override
        public void run() {
            setShowBrowserNavi(false);
        }
    }


    private class BrowserGestureListener implements GestureDetector.OnGestureListener {

        @Override
        public boolean onDown(MotionEvent motionEvent) {
            // 取消待处理的防抖操作
            mainHandler.removeCallbacks(debounceRunnable);
            return false;
        }

        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            if (e1 == null || e2 == null) {
                resetAutoHideTimer();
                return false;
            }

            float deltaX = e2.getX() - e1.getX();
            float deltaY = e2.getY() - e1.getY();
            float absVelocityX = Math.abs(velocityX);
            float absVelocityY = Math.abs(velocityY);

            Log.d(TAG, String.format("快速滑动 - deltaX: %.2f, deltaY: %.2f, velocityX: %.2f, velocityY: %.2f",
                    deltaX, deltaY, velocityX, velocityY));

            // 检查速度是否足够
            if (absVelocityX > minFlingVelocity && absVelocityX > absVelocityY * 2.0f) {
                float totalDistance = (float) Math.sqrt(deltaX * deltaX + deltaY * deltaY);

                if (deltaX > 0) {
                    // 快速右滑：需要从左侧边缘开始且为严格水平滑动
                    if (isFromLeftEdge(e1) && isStrictHorizontalSwipe(deltaX, deltaY, totalDistance)) {
                        Log.d(TAG, "检测到有效的左侧边缘快速右滑 - 显示导航栏");
                        requestShowNavigation();
                        return true;
                    } else {
                        Log.d(TAG, "快速右滑被忽略 - 不满足边缘或水平条件");
                    }
                } else {
                    // 快速左滑：只需要严格水平滑动
                    if (isStrictHorizontalSwipe(deltaX, deltaY, totalDistance)) {
                        Log.d(TAG, "检测到有效的快速左滑 - 隐藏导航栏");
                        requestHideNavigation();
                        return true;
                    } else {
                        Log.d(TAG, "快速左滑被忽略 - 不满足水平条件");
                    }
                }
            } else {
                Log.d(TAG, String.format("快速滑动速度不足 - 速度X: %.2f < %.2f 或 速度比例: %.2f < 2.0",
                        absVelocityX, minFlingVelocity, absVelocityX / Math.max(absVelocityY, 1f)));
            }

            resetAutoHideTimer();
            return false;
        }

        @Override
        public void onLongPress(MotionEvent e) {
            Log.d(TAG, "长按检测");
            resetAutoHideTimer();
        }

        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            if (e1 == null || e2 == null) return false;

            float deltaX = e2.getX() - e1.getX();
            float deltaY = e2.getY() - e1.getY();
            float totalDistance = (float) Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            Log.d(TAG, String.format("滑动检测开始 - 起始位置: (%.2f, %.2f), 当前位置: (%.2f, %.2f), deltaX: %.2f, deltaY: %.2f",
                    e1.getX(), e1.getY(), e2.getX(), e2.getY(), deltaX, deltaY));

            // 检查总滑动距离是否足够
            if (totalDistance < minScrollDistance) {
                Log.d(TAG, String.format("总距离不足 - 实际: %.2f, 需要: %.2f", totalDistance, minScrollDistance));
                return false;
            }

            // 只处理右滑（显示导航栏）的情况
            if (deltaX > 0) {
                // 检查是否从左侧边缘开始
                if (!isFromLeftEdge(e1)) {
                    Log.d(TAG, "不是从左侧边缘开始的滑动，忽略");
                    return false;
                }

                // 检查是否为严格的水平滑动
                if (isStrictHorizontalSwipe(deltaX, deltaY, totalDistance)) {
                    Log.d(TAG, "检测到有效的左侧边缘右滑 - 请求显示导航栏");
                    requestShowNavigation();
                    return true;
                }
            } else {
                // 左滑隐藏导航栏（不需要边缘检测，但需要严格的水平滑动）
                if (isStrictHorizontalSwipe(deltaX, deltaY, totalDistance)) {
                    Log.d(TAG, "检测到有效的左滑 - 请求隐藏导航栏");
                    requestHideNavigation();
                    return true;
                }
            }

            Log.d(TAG, "滑动不满足触发条件，忽略");
            return false;
        }

        @Override
        public void onShowPress(MotionEvent e) {
            resetAutoHideTimer();
        }

        @Override
        public boolean onSingleTapUp(MotionEvent e) {
            Log.d(TAG, "单击检测");
            resetAutoHideTimer();
            return false;
        }
    }

    /**
     * 检查是否从左侧边缘开始滑动
     */
    private boolean isFromLeftEdge(MotionEvent startEvent) {
        if (startEvent == null) return false;

        float startX = startEvent.getX();
        boolean fromEdge = startX <= edgeZoneWidth;

        Log.d(TAG, String.format("边缘检测 - 起始X: %.2f, 边缘宽度: %.2f, 从边缘开始: %s",
                startX, edgeZoneWidth, fromEdge));

        return fromEdge;
    }

    /**
     * 检查是否为严格的水平滑动
     */
    private boolean isStrictHorizontalSwipe(float deltaX, float deltaY, float totalDistance) {
        float absDeltaX = Math.abs(deltaX);
        float absDeltaY = Math.abs(deltaY);

        // 1. 检查水平距离是否足够
        if (absDeltaX < minHorizontalDistance) {
            Log.d(TAG, String.format("水平距离不足 - 实际: %.2f, 需要: %.2f", absDeltaX, minHorizontalDistance));
            return false;
        }

        // 2. 检查垂直偏移是否在允许范围内
        if (absDeltaY > maxVerticalDeviation) {
            Log.d(TAG, String.format("垂直偏移过大 - 实际: %.2f, 最大允许: %.2f", absDeltaY, maxVerticalDeviation));
            return false;
        }

        // 3. 检查方向比例
        float horizontalRatio = absDeltaX / totalDistance;
        if (horizontalRatio < DIRECTION_THRESHOLD) {
            Log.d(TAG, String.format("水平比例不足 - 实际: %.2f, 需要: %.2f", horizontalRatio, DIRECTION_THRESHOLD));
            return false;
        }

        // 4. 检查水平距离是否大于垂直距离
        if (absDeltaX <= absDeltaY * 2.0f) {
            Log.d(TAG, String.format("水平优势不足 - 水平: %.2f, 垂直*2: %.2f", absDeltaX, absDeltaY * 2.0f));
            return false;
        }

        Log.d(TAG, "严格水平滑动检测通过");
        return true;
    }

    /**
     * 请求显示导航栏（带防抖）
     */
    private void requestShowNavigation() {
        if (isAnimating) return;

        pendingShow = true;
        pendingHide = false;
        mainHandler.removeCallbacks(debounceRunnable);
        mainHandler.postDelayed(debounceRunnable, DEBOUNCE_DELAY_MS);
    }

    /**
     * 请求隐藏导航栏（带防抖）
     */
    private void requestHideNavigation() {
        if (isAnimating) return;

        pendingHide = true;
        pendingShow = false;
        mainHandler.removeCallbacks(debounceRunnable);
        mainHandler.postDelayed(debounceRunnable, DEBOUNCE_DELAY_MS);
    }

    /**
     * 带动画显示导航栏
     */
    private void showNavigationWithAnimation() {
        XLog.tag(TAG).enableStackTrace(10).d("showNavigationWithAnimation");
        if (browserNaviLayout.getVisibility() == View.VISIBLE) {
            resetAutoHideTimer();
            return;
        }

        Log.d(TAG, "开始显示导航栏动画");
        isAnimating = true;

        browserNaviLayout.setVisibility(View.VISIBLE);
        browserNaviLayout.setAlpha(0f);
        browserNaviLayout.setTranslationY(browserNaviLayout.getHeight());

        ValueAnimator animator = ValueAnimator.ofFloat(0f, 1f);
        animator.setDuration(ANIMATION_DURATION_MS);
        animator.setInterpolator(new DecelerateInterpolator());

        animator.addUpdateListener(animation -> {
            float progress = (float) animation.getAnimatedValue();
            browserNaviLayout.setAlpha(progress);
            browserNaviLayout.setTranslationY(browserNaviLayout.getHeight() * (1f - progress));
        });

        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                isAnimating = false;
                showBrowserNavi = true;
                resetAutoHideTimer();
                Log.d(TAG, "显示导航栏动画完成");
            }
        });

        animator.start();
    }

    /**
     * 带动画隐藏导航栏
     */
    private void hideNavigationWithAnimation() {
        if (browserNaviLayout.getVisibility() != View.VISIBLE) {
            return;
        }

        Log.d(TAG, "开始隐藏导航栏动画");
        isAnimating = true;

        ValueAnimator animator = ValueAnimator.ofFloat(1f, 0f);
        animator.setDuration(ANIMATION_DURATION_MS);
        animator.setInterpolator(new DecelerateInterpolator());

        animator.addUpdateListener(animation -> {
            float progress = (float) animation.getAnimatedValue();
            browserNaviLayout.setAlpha(progress);
            browserNaviLayout.setTranslationY(browserNaviLayout.getHeight() * (1f - progress));
        });

        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                browserNaviLayout.setVisibility(View.GONE);
                browserNaviLayout.setAlpha(1f);
                browserNaviLayout.setTranslationY(0f);
                isAnimating = false;
                showBrowserNavi = false;
                Log.d(TAG, "隐藏导航栏动画完成");
            }
        });

        animator.start();
    }

    /**
     * 收藏夹适配器
     * 用于显示收藏的网页列表
     */
    private class BookmarkAdapter extends BaseRecyclerViewAdapter<BrowserInfo> {

        public BookmarkAdapter(Context context, List<BrowserInfo> list) {
            super(context, R.layout.item_bookmark, list);
        }


        @Override
        public void bindViewHolder(BaseViewHolder holder, BrowserInfo browserInfo, int position) {
            TextView textView = holder.findViewById(R.id.tv_name);
            textView.setText(browserInfo.title);
            ImageView imageView = holder.findViewById(R.id.iv_icon);
            CheckBox checkBox = holder.findViewById(R.id.checkBox);
            if (browserInfo.favicon == null) {
                imageView.setImageResource(R.drawable.ic_item_def_favicon);
            } else {
                RequestManager requestManager = Glide.with(getContext());
                RequestBuilder<Drawable> requestBuilder = requestManager.load(browserInfo.favicon);
                requestBuilder = applyScaleTypeTransform(requestBuilder, imageView.getScaleType());
                requestBuilder.into(imageView);
            }
            Log.d(TAG, "bindViewHolder: " + browserInfo.selected);
            if (isInEditMode()) {
                checkBox.setVisibility(View.VISIBLE);
                checkBox.setChecked(browserInfo.selected);
            } else {
                checkBox.setVisibility(View.GONE);
            }
            holder.itemView.setOnClickListener(v -> handleItemClick(holder, false));
            holder.itemView.setOnLongClickListener(v -> {
                handleItemClick(holder, true);
                return true;
            });
        }

        private RequestBuilder<Drawable> applyScaleTypeTransform(RequestBuilder<Drawable> builder, ImageView.ScaleType scaleType) {
            switch (scaleType) {
                case CENTER_CROP:
                    return builder.centerCrop();
                case CENTER_INSIDE:
                    return builder.centerInside();
                case FIT_CENTER:
                case FIT_START:
                case FIT_END:
                case FIT_XY: // FitCenter is often a good default for these
                    return builder.fitCenter();
                default:
                    // No transform for MATRIX, CENTER
                    return builder;
            }

        }


        public void handleItemClick(BaseViewHolder viewHolder, boolean isLongClick) {
            boolean isInEditMode = isInEditMode();
            int position = viewHolder.getPosition();

            if (position < 0 || position >= getItemCount()) {
                return;
            }

            BrowserInfo browserInfo = getItem(position);
            OnItemClickListener listener = BrowserLayout.this.itemClickListener;

            if (listener != null) {
                View itemView = viewHolder.itemView;
                if (isLongClick) {
                    listener.onLongClick(isInEditMode);
                } else {
                    listener.onItemClick(itemView, isInEditMode, browserInfo);
                }
            }
        }
    }

    /**
     * 浏览器点击监听器接口
     */
    public interface OnBrowserClickListener {
        /**
         * 当检测到链接点击时调用
         */
        default void onBrowserEditTypeClick() {
        }

        /**
         * 当按钮被点击时调用
         *
         * @param view 被点击的视图
         */
        default void onBrowserButtonClick(View view) {
        }
    }


    /**
     * 项目点击监听器接口
     */
    public interface OnItemClickListener {
        /**
         * 当项目被点击时调用
         *
         * @param view        被点击的视图
         * @param isFavorite  是否处于编辑模式
         * @param browserInfo 浏览器信息
         */
        void onItemClick(View view, boolean isFavorite, BrowserInfo browserInfo);

        /**
         * 当项目被长按时调用
         *
         * @param isFavorite 是否处于编辑模式
         */
        default void onLongClick(boolean isFavorite) {
        }
    }

    public BrowserLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initializeComponents(context);
    }

    /**
     * 初始化组件
     */
    private void initializeComponents(Context context) {
        // 初始化屏幕密度相关参数
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        this.density = metrics.density;
        this.minScrollDistance = MIN_SCROLL_DISTANCE_DP * density;
        this.minFlingVelocity = MIN_FLING_VELOCITY_DP * density;
        this.minHorizontalDistance = MIN_HORIZONTAL_DISTANCE_DP * density;
        this.edgeZoneWidth = EDGE_ZONE_WIDTH_DP * density;
        this.maxVerticalDeviation = MAX_VERTICAL_DEVIATION_DP * density;

        // 初始化防抖处理
        this.debounceRunnable = () -> {
            if (pendingShow && !isAnimating) {
                showNavigationWithAnimation();
                pendingShow = false;
            } else if (pendingHide && !isAnimating) {
                hideNavigationWithAnimation();
                pendingHide = false;
            }
        };

        Log.d(TAG, String.format("初始化手势参数 - 密度: %.2f, 最小滑动距离: %.2f, 最小快速滑动速度: %.2f, 最小水平距离: %.2f, 边缘区域宽度: %.2f, 最大垂直偏移: %.2f",
                density, minScrollDistance, minFlingVelocity, minHorizontalDistance, edgeZoneWidth, maxVerticalDeviation));

        // 初始化自动隐藏任务和手势检测器
        this.autoHideTask = new AutoHideTask();
        this.gestureDetector = new GestureDetector(getContext(), new BrowserGestureListener());

        // 加载布局
        View.inflate(context, R.layout.layout_browser, this);

        // 初始化视图组件
        initializeViews();

        // 初始化WebView
        initializeWebView();

        // 设置点击监听器
        setupClickListeners();

        // 设置初始状态
        setupInitialState();

        // 重置自动隐藏计时器
        resetAutoHideTimer();
    }

    /**
     * 初始化视图组件
     */
    private void initializeViews() {
        phoneWebContainer = findViewById(R.id.rl_web_phone);
        webContainer = findViewById(R.id.rl_web);
        webViewContainer = findViewById(R.id.webview_container);
        loadProgressIndicator = findViewById(R.id.load_progress);
        browserNaviLayout = findViewById(R.id.browser_naiv_layout);
        divisionLineView = findViewById(R.id.view_division_line);

        // 导航按钮
        homeButton = findViewById(R.id.home);
        rewardButton = findViewById(R.id.reward);
        forwardButton = findViewById(R.id.forward);
        sendToRemoteButton = findViewById(R.id.send_to_remote);
        sendToPhoneButton = findViewById(R.id.send_to_phone);
        refreshButton = findViewById(R.id.refresh);
        starButton = findViewById(R.id.star);

        // 收藏夹相关视图
        favoriteLayout = findViewById(R.id.rl_favorite);
        bookmarkRecyclerView = findViewById(R.id.rv_bookmark);
        emptyFavoriteTextView = findViewById(R.id.tv_empty_fav_tips);
        bottomLayout = findViewById(R.id.ll_bottom);
        deleteTextView = findViewById(R.id.tv_delete);
        allSelectTextView = findViewById(R.id.tv_all_select);
    }

    /**
     * 初始化WebView
     */
    private void initializeWebView() {
        webView = new CustomWebView(getContext());
        webViewContainer.addView(webView);
        webView.setBackgroundColor(0);

        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) webView.getLayoutParams();
        layoutParams.width = FrameLayout.LayoutParams.MATCH_PARENT;
        layoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT;
        webView.setLayoutParams(layoutParams);
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        homeButton.setOnClickListener(this);
        rewardButton.setOnClickListener(this);
        forwardButton.setOnClickListener(this);
        sendToPhoneButton.setOnClickListener(this);
        sendToRemoteButton.setOnClickListener(this);
        refreshButton.setOnClickListener(this);
        starButton.setOnClickListener(this);
        deleteTextView.setOnClickListener(this);
        allSelectTextView.setOnClickListener(this);
    }

    /**
     * 设置初始状态
     */
    private void setupInitialState() {
        setEnableHome(false);
        setEnableReward(false);
        setEnableForward(false);
        setEnableStar(false);
        setEnableRefresh(false);
    }

    /**
     * 设置分割线位置
     *
     * @param position 位置：0=左侧，1=右侧，2=底部
     */
    private void setDivisionLinePosition(int position) {
        // 设置分割线可见性
        divisionLineView.setVisibility(showDivisionLine ? View.VISIBLE : View.GONE);

        float dimension = getResources().getDimension(R.dimen.division_line_size);
        RelativeLayout.LayoutParams layoutParams = null;
        int backgroundResource = R.drawable.ic_ctrl_line_vertical;

        switch (position) {
            case 0: // 左侧
                layoutParams = new RelativeLayout.LayoutParams((int) dimension, RelativeLayout.LayoutParams.MATCH_PARENT);
                layoutParams.addRule(RelativeLayout.END_OF, browserNaviLayout.getId());
                divisionLineView.setBackgroundResource(backgroundResource);
                break;

            case 1: // 右侧
                layoutParams = new RelativeLayout.LayoutParams((int) dimension, RelativeLayout.LayoutParams.MATCH_PARENT);
                layoutParams.addRule(RelativeLayout.START_OF, browserNaviLayout.getId());
                divisionLineView.setBackgroundResource(backgroundResource);
                break;

            case 2: // 底部
                layoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, (int) dimension);
                layoutParams.addRule(RelativeLayout.ABOVE, browserNaviLayout.getId());
                divisionLineView.setBackgroundResource(R.drawable.ic_ctrl_line_horizontal);
                break;
        }

        if (layoutParams != null) {
            divisionLineView.setLayoutParams(layoutParams);
        }
    }

    /**
     * 设置是否显示浏览器导航栏
     */
    public void setShowBrowserNavi(boolean show) {
        if (showBrowserNavi && isFavoriteVisible() && !show) {
            browserNaviLayout.setVisibility(View.VISIBLE);
        } else {
            // 使用动画显示/隐藏导航栏
            if (show) {
                showNavigationWithAnimation();
            } else {
                hideNavigationWithAnimation();
            }
        }
    }

    /**
     * 重置自动隐藏计时器
     */
    private void resetAutoHideTimer() {
        boolean isNaviVisible = browserNaviLayout.getVisibility() == View.VISIBLE;
        boolean isFavoriteVisible = isFavoriteVisible();

        if (autoHideBrowserNavi && isNaviVisible && !isFavoriteVisible) {
            browserNaviLayout.removeCallbacks(autoHideTask);
            browserNaviLayout.postDelayed(autoHideTask, 3000L);
        }
    }

    /**
     * 刷新收藏夹适配器
     */
    private void refreshBookmarkAdapter() {
        boolean hasData = (browserInfoList != null ? browserInfoList.size() : 0) > 0;
        Log.d("BrowserLayout", "refreshBookmarkAdapter hasData:" + hasData);

        bookmarkRecyclerView.setVisibility(hasData ? View.VISIBLE : View.GONE);
        emptyFavoriteTextView.setVisibility(hasData ? View.GONE : View.VISIBLE);

        if (bookmarkAdapter != null) {
            bookmarkAdapter.setDataList(browserInfoList);
            return;
        }

        bookmarkAdapter = new BookmarkAdapter(getContext(), browserInfoList);
        GridLayoutManager gridLayoutManager = new AutoFitGridLayoutManager(getContext(), 80, 128);
        gridLayoutManager.setOrientation(RecyclerView.VERTICAL);
        bookmarkRecyclerView.setLayoutManager(gridLayoutManager);
        bookmarkRecyclerView.setAdapter(bookmarkAdapter);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent motionEvent) {
        // 检测链接点击
        Log.d(TAG, "dispatchTouchEvent: " + webView.getHitTestResult().getType());
//        if (motionEvent.getAction() == MotionEvent.ACTION_UP &&
//                webView.getHitTestResult().getType() == WebView.HitTestResult.EDIT_TEXT_TYPE &&
//                browserClickListener != null) {
//            browserClickListener.onBrowserEditTypeClick();
//        }

        // 处理手势检测
        if (autoHideBrowserNavi) {
            gestureDetector.onTouchEvent(motionEvent);
        }

        return super.dispatchTouchEvent(motionEvent);
    }

    /**
     * 检查是否处于编辑模式
     */
    public boolean isInEditMode() {
        return bottomLayout.getVisibility() == View.VISIBLE;
    }

    /**
     * 检查收藏夹是否可见
     */
    public boolean isFavoriteVisible() {
        return favoriteLayout.getVisibility() == View.VISIBLE;
    }

    /**
     * 设置导航栏布局参数
     *
     * @param position 位置：0=左侧，1=右侧，2=底部
     * @param width    宽度
     * @param height   高度
     */
    public void setNaviLayoutParams(int position, int width, int height) {
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(width, height);
        webContainer.setPadding(0, 0, 0, 0);

        switch (position) {
            case 0: // 左侧
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT, RelativeLayout.TRUE);
                break;
            case 1: // 右侧
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT, RelativeLayout.TRUE);
                break;
            case 2: // 底部
                layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
                if (!autoHideBrowserNavi) {
                    webContainer.setPadding(0, 0, 0, height);
                }
                break;
        }

        setDivisionLinePosition(position);
        browserNaviLayout.setLayoutParams(layoutParams);

        // 设置导航栏内部布局
        if (browserNaviLayout.getBottomButtonLayout() != null) {
            switch (position) {
                case 0: // 左侧
                    browserNaviLayout.getBottomButtonLayout().setOrientation(LinearLayout.VERTICAL);
                    browserNaviLayout.setViewLayoutParams(browserNaviLayout.getBottomButtonLayout(),
                            Gravity.LEFT, width, RelativeLayout.LayoutParams.MATCH_PARENT);
                    break;
                case 1: // 右侧
                    browserNaviLayout.getBottomButtonLayout().setOrientation(LinearLayout.VERTICAL);
                    browserNaviLayout.setViewLayoutParams(browserNaviLayout.getBottomButtonLayout(),
                            Gravity.RIGHT, width, RelativeLayout.LayoutParams.MATCH_PARENT);
                    break;
                case 2: // 底部
                    browserNaviLayout.getBottomButtonLayout().setOrientation(LinearLayout.HORIZONTAL);
                    browserNaviLayout.setViewLayoutParams(browserNaviLayout.getBottomButtonLayout(),
                            Gravity.BOTTOM, RelativeLayout.LayoutParams.MATCH_PARENT, height);
                    break;
            }
        }
    }

    /**
     * 获取WebView实例
     */
    public WebView getWebView() {
        return webView;
    }

    /**
     * 设置加载进度
     *
     * @param visible  是否显示进度条
     * @param progress 进度值
     */
    public void setLoadProgress(boolean visible, int progress) {
        loadProgressIndicator.setVisibility(visible ? View.VISIBLE : View.GONE);
        loadProgressIndicator.setProgress(progress);
    }

    /**
     * 显示导航栏
     */
    public void showBrowserNavi() {
        showBrowserNavi = true;
        setShowBrowserNavi(true);
    }

    // 防重复调用的状态记录
    private boolean currentShowWebState = true;
    private long lastSwitchTime = 0;

    /**
     * 切换Web视图和收藏夹视图
     *
     * @param showWeb 是否显示Web视图
     */
    public void switchView(boolean showWeb) {
        Log.d(TAG, "switchView: " + showWeb + " (current: " + currentShowWebState + ")");

        // 防止短时间内重复调用相同状态
        long currentTime = System.currentTimeMillis();
        if (currentShowWebState == showWeb && (currentTime - lastSwitchTime) < 300) {
            Log.d(TAG, "switchView: 忽略重复调用 " + showWeb);
            return;
        }

        currentShowWebState = showWeb;
        lastSwitchTime = currentTime;

        phoneWebContainer.setVisibility(showWeb ? View.VISIBLE : View.GONE);
        favoriteLayout.setVisibility(!showWeb ? View.VISIBLE : View.GONE);

        if (autoHideBrowserNavi && isFavoriteVisible()) {
            browserNaviLayout.setVisibility(View.VISIBLE);
        } else {
            browserNaviLayout.setVisibility(showWeb ? View.VISIBLE : View.GONE);
        }

        if (showWeb) {
            webView.onResume();
        } else {
            webView.onPause();
        }

        resetAutoHideTimer();
    }

    @Override
    public void onClick(View view) {
        resetAutoHideTimer();
        if (browserClickListener != null) {
            browserClickListener.onBrowserButtonClick(view);
        }
    }

    // 公共设置方法

    /**
     * 设置是否自动隐藏浏览器导航栏
     */
    public void setAutoHideBrowserNavi(boolean autoHide) {
        this.autoHideBrowserNavi = autoHide;
    }


    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.d(TAG, "onConfigurationChanged");
        if (bookmarkRecyclerView != null) {
            bookmarkRecyclerView.requestLayout();
        }
    }

    /**
     * 设置前进按钮是否可用
     */
    public void setEnableForward(boolean enabled) {
        forwardButton.setEnabled(enabled);
    }

    /**
     * 设置首页按钮是否可用
     */
    public void setEnableHome(boolean enabled) {
        homeButton.setEnabled(enabled);
    }

    /**
     * 设置刷新按钮是否可用
     */
    public void setEnableRefresh(boolean enabled) {
        refreshButton.setEnabled(enabled);
    }

    /**
     * 设置后退按钮是否可用
     */
    public void setEnableReward(boolean enabled) {
        rewardButton.setEnabled(enabled);
    }

    /**
     * 设置发送按钮是否可用
     */
    public void setEnableSendTo(boolean enabled) {
        sendToRemoteButton.setEnabled(enabled);
    }

    /**
     * 设置收藏按钮是否可用
     */
    public void setEnableStar(boolean enabled) {
        starButton.setEnabled(enabled);
    }

    /**
     * 设置浏览器点击监听器
     */
    public void setOnClickBrowserListener(OnBrowserClickListener listener) {
        this.browserClickListener = listener;
    }

    /**
     * 设置项目点击监听器
     */
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.itemClickListener = listener;
    }

    /**
     * 设置收藏按钮选中状态
     */
    public void setSelectStar(boolean selected) {
        starButton.setSelected(selected);
    }

    /**
     * 设置全选按钮文本
     */
    public void setTextAllSelect(String text) {
        allSelectTextView.setText(text);
    }

    /**
     * 设置收藏夹数据
     */
    public void setBrowserInfoList(List<BrowserInfo> list) {
        Log.d(TAG, "setBrowserInfoList");
        this.browserInfoList = list;
        refreshBookmarkAdapter();
    }

    /**
     * 设置是否显示分割线
     */
    public void setShowDivisionLine(boolean show) {
        this.showDivisionLine = show;
    }

    // Getter 方法

    public BrowserNavLayout getBrowserNaviLayout() {
        return browserNaviLayout;
    }

    public LinearProgressIndicator getLoadProgressIndicator() {
        return loadProgressIndicator;
    }

    public RecyclerView getBookmarkRecyclerView() {
        return bookmarkRecyclerView;
    }

    public List<BrowserInfo> getBrowserInfoList() {
        return browserInfoList;
    }

    public LinearLayout getBottomLayout() {
        return bottomLayout;
    }

    public void notifyDataSetChanged() {
        bookmarkAdapter.notifyDataSetChanged();
    }

    public RelativeLayout getWebContainer() {
        return webContainer;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // 清理手势相关资源
        if (mainHandler != null && debounceRunnable != null) {
            mainHandler.removeCallbacks(debounceRunnable);
        }
        if (isAnimating && browserNaviLayout != null) {
            browserNaviLayout.animate().cancel();
        }
        Log.d(TAG, "BrowserLayout 资源清理完成");
    }

}
