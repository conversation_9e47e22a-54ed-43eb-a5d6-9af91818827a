package com.link.autolink.presentation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.link.autolink.R;

public class ToastBrowserLayout extends RelativeLayout {

    // Toast文本视图
    private TextView toastTextView;

    // 自动隐藏任务
    private AutoHideTask autoHideTask;

    private class AutoHideTask implements Runnable {

        @Override
        public void run() {
            setVisibility(View.GONE);
        }
    }

    public ToastBrowserLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initializeComponents(context);
    }

    private void initializeComponents(Context context) {
        this.autoHideTask = new AutoHideTask();

        // 初始状态为隐藏
        setVisibility(View.GONE);

        // 加载布局
        View.inflate(context, R.layout.layout_browser_toast, this);

        // 初始化视图
        initializeViews();
    }

    private void initializeViews() {
        toastTextView = findViewById(R.id.tv_toast);
    }

    public void setToast(String text) {
        toastTextView.setText(text);
    }

    public void setToast(int resId) {
        toastTextView.setText(resId);
    }

    public void showToast() {
        setVisibility(View.VISIBLE);
    }

    public void hideToast() {
        setVisibility(View.GONE);
        removeCallbacks(autoHideTask);
    }

    public void showToast(String text, long duration) {
        setToast(text);
        showToastWithDuration(duration);
    }

    public void showToast(int resId, long duration) {
        setToast(resId);
        showToastWithDuration(duration);
    }

    public void showToastWithDuration(long duration) {
        showToast();
        removeCallbacks(autoHideTask);
        postDelayed(autoHideTask, duration);
    }

    public void cancelAutoHide() {
        removeCallbacks(autoHideTask);
    }

    public boolean isShowing() {
        return getVisibility() == View.VISIBLE;
    }

    public TextView getToastTextView() {
        return toastTextView;
    }

}