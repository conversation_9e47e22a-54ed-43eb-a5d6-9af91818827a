package com.link.autolink.presentation.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.util.Log;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.ThemedSpinnerAdapter;

public class CustomWebView extends WebView {

    public CustomWebView(Context context) {
        super(context);
        initializeWebSettings();
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void initializeWebSettings() {
        WebSettings settings = getSettings();
        //设置 User Agent String (UA) 为桌面浏览器
//        String desktopUserAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
//        settings.setUserAgentString(desktopUserAgent);

        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        //设置默认的文本编码为 UTF-8
        settings.setDefaultTextEncodingName("UTF-8");
        //启用JavaScript
        settings.setJavaScriptEnabled(true);
        //允许JavaScript打开窗口
        settings.setJavaScriptCanOpenWindowsAutomatically(true);
        //不使用缓存
        settings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        //启用DOM存储
        settings.setDomStorageEnabled(true);
        //启用数据库
        settings.setDatabaseEnabled(true);
        //启用文件访问
        settings.setAllowFileAccess(true);
        //启用密码保存
        settings.setSavePassword(true);
        //启用缩放
        settings.setSupportZoom(false);
        //启用缩放控件
        settings.setBuiltInZoomControls(true);
        //将 WebView 的视口（Viewport）设置为一个较宽的值
        settings.setUseWideViewPort(true);
        //设置布局算法为 NORMAL
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
        //启用自动加载图片
        settings.setLoadsImagesAutomatically(true);
        //隐藏缩放控件
        settings.setDisplayZoomControls(false);
        //启用Overview Mode
        settings.setLoadWithOverviewMode(true);
        //启用混合内容
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        //启用自动播放视频
        settings.setMediaPlaybackRequiresUserGesture(false);
        //启用插件
        settings.setPluginState(WebSettings.PluginState.ON);
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d("CustomWebView", "onPause");
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d("CustomWebView", "onResume");
    }


    private static final String TAG = "CustomWebView";
}