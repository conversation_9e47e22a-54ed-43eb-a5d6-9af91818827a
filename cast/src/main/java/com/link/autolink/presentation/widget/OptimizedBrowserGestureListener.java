package com.link.autolink.presentation.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

/**
 * 优化的浏览器手势监听器
 * 提供更智能和丝滑的手势交互体验
 */
public class OptimizedBrowserGestureListener implements GestureDetector.OnGestureListener {
    
    private static final String TAG = "OptimizedBrowserGesture";
    
    // 手势配置常量
    private static final float MIN_FLING_VELOCITY_DP = 100f; // 最小快速滑动速度 (dp/s)
    private static final float MIN_SCROLL_DISTANCE_DP = 8f;  // 最小滑动距离 (dp)
    private static final float DIRECTION_THRESHOLD = 0.6f;   // 方向判断阈值 (cos 53°)
    private static final long DEBOUNCE_DELAY_MS = 150L;      // 防抖延迟时间
    private static final long ANIMATION_DURATION_MS = 250L;  // 动画持续时间
    
    // 依赖组件
    private final Context context;
    private final BrowserNavLayout browserNaviLayout;
    private final Runnable autoHideTask;
    private final Handler mainHandler;
    
    // 手势状态
    private boolean isAnimating = false;
    private boolean pendingShow = false;
    private boolean pendingHide = false;
    private final Runnable debounceRunnable;
    
    // 屏幕密度相关
    private final float density;
    private final float minScrollDistance;
    private final float minFlingVelocity;
    
    // 回调接口
    public interface NavigationVisibilityCallback {
        void onShowNavigation();
        void onHideNavigation();
        void resetAutoHideTimer();
    }
    
    private NavigationVisibilityCallback callback;
    
    public OptimizedBrowserGestureListener(Context context, 
                                         BrowserNavLayout browserNaviLayout,
                                         Runnable autoHideTask,
                                         NavigationVisibilityCallback callback) {
        this.context = context;
        this.browserNaviLayout = browserNaviLayout;
        this.autoHideTask = autoHideTask;
        this.callback = callback;
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        // 计算基于屏幕密度的阈值
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        this.density = metrics.density;
        this.minScrollDistance = MIN_SCROLL_DISTANCE_DP * density;
        this.minFlingVelocity = MIN_FLING_VELOCITY_DP * density;
        
        // 防抖处理
        this.debounceRunnable = new Runnable() {
            @Override
            public void run() {
                if (pendingShow && !isAnimating) {
                    showNavigationWithAnimation();
                    pendingShow = false;
                } else if (pendingHide && !isAnimating) {
                    hideNavigationWithAnimation();
                    pendingHide = false;
                }
            }
        };
        
        Log.d(TAG, String.format("初始化手势监听器 - 密度: %.2f, 最小滑动距离: %.2f, 最小快速滑动速度: %.2f", 
                density, minScrollDistance, minFlingVelocity));
    }
    
    @Override
    public boolean onDown(MotionEvent e) {
        // 取消待处理的防抖操作
        mainHandler.removeCallbacks(debounceRunnable);
        return false;
    }
    
    @Override
    public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
        if (e1 == null || e2 == null) return false;
        
        float deltaX = e2.getX() - e1.getX();
        float deltaY = e2.getY() - e1.getY();
        float absVelocityX = Math.abs(velocityX);
        float absVelocityY = Math.abs(velocityY);
        
        Log.d(TAG, String.format("快速滑动 - deltaX: %.2f, deltaY: %.2f, velocityX: %.2f, velocityY: %.2f", 
                deltaX, deltaY, velocityX, velocityY));
        
        // 检查是否为有效的水平快速滑动
        if (absVelocityX > minFlingVelocity && 
            absVelocityX > absVelocityY * 1.5f && 
            Math.abs(deltaX) > minScrollDistance) {
            
            if (deltaX > 0) {
                Log.d(TAG, "快速右滑 - 显示导航栏");
                requestShowNavigation();
            } else {
                Log.d(TAG, "快速左滑 - 隐藏导航栏");
                requestHideNavigation();
            }
            return true;
        }
        
        // 重置自动隐藏计时器
        if (callback != null) {
            callback.resetAutoHideTimer();
        }
        return false;
    }
    
    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        if (e1 == null || e2 == null) return false;
        
        float deltaX = e2.getX() - e1.getX();
        float deltaY = e2.getY() - e1.getY();
        float totalDistance = (float) Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        
        // 检查滑动距离是否足够
        if (totalDistance < minScrollDistance) {
            return false;
        }
        
        // 计算水平方向的比例 (使用余弦值判断方向)
        float horizontalRatio = Math.abs(deltaX) / totalDistance;
        
        Log.d(TAG, String.format("滑动检测 - deltaX: %.2f, deltaY: %.2f, 水平比例: %.2f", 
                deltaX, deltaY, horizontalRatio));
        
        // 判断是否为主要的水平滑动
        if (horizontalRatio >= DIRECTION_THRESHOLD) {
            if (deltaX > 0) {
                Log.d(TAG, "水平右滑 - 请求显示导航栏");
                requestShowNavigation();
            } else {
                Log.d(TAG, "水平左滑 - 请求隐藏导航栏");
                requestHideNavigation();
            }
            return true;
        }
        
        return false;
    }
    
    @Override
    public void onLongPress(MotionEvent e) {
        Log.d(TAG, "长按检测");
        if (callback != null) {
            callback.resetAutoHideTimer();
        }
    }
    
    @Override
    public void onShowPress(MotionEvent e) {
        if (callback != null) {
            callback.resetAutoHideTimer();
        }
    }
    
    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        Log.d(TAG, "单击检测");
        if (callback != null) {
            callback.resetAutoHideTimer();
        }
        return false;
    }
    
    /**
     * 请求显示导航栏（带防抖）
     */
    private void requestShowNavigation() {
        if (isAnimating) return;
        
        pendingShow = true;
        pendingHide = false;
        mainHandler.removeCallbacks(debounceRunnable);
        mainHandler.postDelayed(debounceRunnable, DEBOUNCE_DELAY_MS);
    }
    
    /**
     * 请求隐藏导航栏（带防抖）
     */
    private void requestHideNavigation() {
        if (isAnimating) return;
        
        pendingHide = true;
        pendingShow = false;
        mainHandler.removeCallbacks(debounceRunnable);
        mainHandler.postDelayed(debounceRunnable, DEBOUNCE_DELAY_MS);
    }
    
    /**
     * 带动画显示导航栏
     */
    private void showNavigationWithAnimation() {
        if (browserNaviLayout.getVisibility() == View.VISIBLE) {
            if (callback != null) {
                callback.resetAutoHideTimer();
            }
            return;
        }
        
        Log.d(TAG, "开始显示导航栏动画");
        isAnimating = true;
        
        browserNaviLayout.setVisibility(View.VISIBLE);
        browserNaviLayout.setAlpha(0f);
        browserNaviLayout.setTranslationY(browserNaviLayout.getHeight());
        
        ValueAnimator animator = ValueAnimator.ofFloat(0f, 1f);
        animator.setDuration(ANIMATION_DURATION_MS);
        animator.setInterpolator(new DecelerateInterpolator());
        
        animator.addUpdateListener(animation -> {
            float progress = (float) animation.getAnimatedValue();
            browserNaviLayout.setAlpha(progress);
            browserNaviLayout.setTranslationY(browserNaviLayout.getHeight() * (1f - progress));
        });
        
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                isAnimating = false;
                if (callback != null) {
                    callback.onShowNavigation();
                    callback.resetAutoHideTimer();
                }
                Log.d(TAG, "显示导航栏动画完成");
            }
        });
        
        animator.start();
    }
    
    /**
     * 带动画隐藏导航栏
     */
    private void hideNavigationWithAnimation() {
        if (browserNaviLayout.getVisibility() != View.VISIBLE) {
            return;
        }
        
        Log.d(TAG, "开始隐藏导航栏动画");
        isAnimating = true;
        
        ValueAnimator animator = ValueAnimator.ofFloat(1f, 0f);
        animator.setDuration(ANIMATION_DURATION_MS);
        animator.setInterpolator(new DecelerateInterpolator());
        
        animator.addUpdateListener(animation -> {
            float progress = (float) animation.getAnimatedValue();
            browserNaviLayout.setAlpha(progress);
            browserNaviLayout.setTranslationY(browserNaviLayout.getHeight() * (1f - progress));
        });
        
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                browserNaviLayout.setVisibility(View.GONE);
                browserNaviLayout.setAlpha(1f);
                browserNaviLayout.setTranslationY(0f);
                isAnimating = false;
                if (callback != null) {
                    callback.onHideNavigation();
                }
                Log.d(TAG, "隐藏导航栏动画完成");
            }
        });
        
        animator.start();
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        mainHandler.removeCallbacks(debounceRunnable);
        if (isAnimating) {
            browserNaviLayout.animate().cancel();
        }
    }
}
