package com.link.autolink.presentation;

import android.view.View;
import android.webkit.PermissionRequest;
import android.webkit.WebView;

import com.link.autolink.presentation.browser.model.BrowserInfo;

import java.util.List;

public interface WebViewCallback {

    /**
     * 获取回调类型标识
     * @return 回调类型 ("presentation" 或 "activity")
     */
    default String getCallbackType() {
        return "unknown";
    }

    default void onStarStatusChanged(boolean isStarred) {
    }

    default void onNavigationVisibilityChanged(boolean visible, String url) {
    }

    default void onBookmarksUpdated(List<BrowserInfo> bookmarks) {
    }

    default void onWebViewReady(WebView webView) {
    }

    default void onPermissionRequest(PermissionRequest request) {
    }

    default void onLocalBrowserStatus(boolean enabled) {
    }

    default void onPageLoadStatus(boolean isRemote, WebView webView, boolean showProgress, int progress) {
    }

    default void onPageStarted(boolean isRemote, String url) {
    }

    default void onPageFinished(WebView webView) {
    }

    default void onVideoSizeChanged(int which) {
    }

    /**
     * 视频进入全屏模式
     * @param customView 全屏视频视图
     */
    default void onVideoFullscreenEnter(View customView) {
    }

    /**
     * 视频退出全屏模式
     */
    default void onVideoFullscreenExit() {
    }
}