package com.link.autolink.presentation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import androidx.appcompat.widget.AppCompatImageView;

import com.link.autolink.R;

public class BrowserNavLayout extends LinearLayout {

    // 底部按钮布局容器
    private LinearLayout bottomButtonLayout;

    // 首页按钮
    private AppCompatImageView homeButton;

    // 发送到手机按钮
    private AppCompatImageView sendToPhoneButton;

    // 发送到远程设备按钮
    private AppCompatImageView sendToRemoteButton;

    public BrowserNavLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initializeViews(context);
    }

    private void initializeViews(Context context) {
        View.inflate(context, R.layout.layout_browser_navi, this);
        bottomButtonLayout = findViewById(R.id.bottom_btn_layout);
        homeButton = findViewById(R.id.home);
        sendToPhoneButton = findViewById(R.id.send_to_phone);
        sendToRemoteButton = findViewById(R.id.send_to_remote);
    }

    public final void setViewLayoutParams(View view, int gravity, int width, int height) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) view.getLayoutParams();
        layoutParams.width = width;
        layoutParams.height = height;
        layoutParams.gravity = gravity;
        view.setLayoutParams(layoutParams);
    }

    @Override
    public void setBackgroundColor(int color) {
        if (bottomButtonLayout != null) {
            bottomButtonLayout.setBackgroundColor(color);
        }
    }

    public LinearLayout getBottomButtonLayout() {
        return bottomButtonLayout;
    }

    public AppCompatImageView getHomeButton() {
        return homeButton;
    }

    public AppCompatImageView getSendToPhoneButton() {
        return sendToPhoneButton;
    }

    public AppCompatImageView getSendToRemoteButton() {
        return sendToRemoteButton;
    }
}