package com.link.autolink.presentation.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.link.autolink.R;

public class TipBrowserLayout extends LinearLayout {

    // 提示文本视图
    private TextView tipTextView;

    // 确认按钮点击监听器
    private View.OnClickListener confirmClickListener;

    // 自动隐藏任务
    private AutoHideTask autoHideTask;

    // 确认按钮
    private TextView confirmButton;

    private LinearLayout tipLayout;


    private class AutoHideTask implements Runnable {

        @Override
        public void run() {
            hideTip();
        }
    }

    public TipBrowserLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        initializeComponents(context);
    }

    private void initializeComponents(Context context) {
        this.autoHideTask = new AutoHideTask();
        // 加载布局
        View.inflate(context, R.layout.layout_browser_tip, this);

        // 初始化视图
        initializeViews();
    }

    private void initializeViews() {
        tipTextView = findViewById(R.id.tv_tip);
        confirmButton = findViewById(R.id.tv_tip_confirm);
        tipLayout = findViewById(R.id.tip_layout);
        hideTip();
        // 设置确认按钮点击监听器
        confirmButton.setOnClickListener(v -> {
            if (confirmClickListener != null) {
                confirmClickListener.onClick(v);
            }
            hideTip();
        });
    }

    private void hideTip() {
        tipLayout.setVisibility(View.GONE);
    }

    public void showTip() {
        tipLayout.setVisibility(View.VISIBLE);
    }

    public void showTipWithAutoHide(long delayMillis) {
        showTip();
        removeCallbacks(autoHideTask);
        postDelayed(autoHideTask, delayMillis);
    }

    public void cancelAutoHide() {
        removeCallbacks(autoHideTask);
    }

    public void setOnConfirmClickListener(View.OnClickListener onClickListener) {
        this.confirmClickListener = onClickListener;
    }

    public void setTip(String tip) {
        tipTextView.setText(tip);
    }

    public void setTip(int resId) {
        tipTextView.setText(resId);
    }

    public void setConfirmButtonText(String text) {
        confirmButton.setText(text);
    }

    public void setConfirmButtonText(int resId) {
        confirmButton.setText(resId);
    }

    public View.OnClickListener getConfirmClickListener() {
        return confirmClickListener;
    }

    public TextView getTipTextView() {
        return tipTextView;
    }

    public TextView getConfirmButton() {
        return confirmButton;
    }
}