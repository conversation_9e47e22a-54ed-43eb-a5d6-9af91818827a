package com.link.autolink.crash;

import androidx.annotation.NonNull;

import com.elvishew.xlog.XLog;

/**
 * <AUTHOR>
 * @date 2018/11/21
 */
public class CrashHandler implements Thread.UncaughtExceptionHandler {
    private static CrashHandler sInstance;
    private static final String TAG = "CrashHandler";
    private Thread.UncaughtExceptionHandler mDefaultCrashHandler;

    public static CrashHandler getInstance() {
        if (sInstance == null) {
            synchronized (CrashHandler.class) {
                if (sInstance == null) {
                    sInstance = new CrashHandler();
                }
            }
        }
        return sInstance;
    }

    public void init() {
        mDefaultCrashHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    @Override
    public void uncaughtException(@NonNull Thread t, @NonNull Throwable e) {
        String message = obtainExceptionInfo(e);
        XLog.tag(TAG).e(e);
        XLog.tag(TAG).e("uncaughtException:" + message);
        try {
            Thread.sleep(1000);
        } catch (InterruptedException ex) {
            ex.printStackTrace();
        }
        if (mDefaultCrashHandler != null) {
            mDefaultCrashHandler.uncaughtException(t, e);
        } else {
            android.os.Process.killProcess(android.os.Process.myPid());
        }
    }

    private static String obtainExceptionInfo(Throwable throwable) {
        StackTraceElement[] stackTraceElements = throwable.getStackTrace();
        StringBuilder sb = new StringBuilder();
        for (StackTraceElement stackTraceElement : stackTraceElements) {
            sb.append(stackTraceElement.toString()).append(";");
        }
        Throwable cause = throwable.getCause();
        if (cause != null) {
            StackTraceElement[] causeStackTrace = cause.getStackTrace();
            for (StackTraceElement stackTraceElement : causeStackTrace) {
                sb.append(stackTraceElement.toString()).append(":");
            }
        }
        return sb.toString();
    }
}
