package com.link.autolink.view;

import android.graphics.PixelFormat;
import android.os.Build;
import android.view.Gravity;
import android.view.WindowManager;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
public class ScreenOnLayout extends WindowManager.LayoutParams {
    /**
     * ScreenOnLayout.
     */
    public ScreenOnLayout() {
        super(0, 0, WindowManager.LayoutParams.TYPE_TOAST, FLAG_NOT_FOCUSABLE | FLAG_KEEP_SCREEN_ON,
                PixelFormat.RGBX_8888);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            this.type = TYPE_APPLICATION_OVERLAY;
        } else if (Build.VERSION.SDK_INT == Build.VERSION_CODES.N_MR1) {
            this.type = TYPE_PHONE;
        } else {
            this.type = TYPE_TOAST;
        }
        this.width = 1;
        this.height = 1;
        this.gravity = Gravity.TOP | Gravity.START;
    }
}
