package com.link.autolink.view;

import android.graphics.PixelFormat;
import android.os.Build;
import android.view.Gravity;
import android.view.WindowManager;

/**
 * <AUTHOR>
 * @date 2018/11/21
 */
public class ForceScreenLayout extends WindowManager.LayoutParams {

    public static final int LAYOUT_TYPE_LANDSCAPE = 1;

    public static final int LAYOUT_TYPE_PORTRAIT = 2;

    public static final int LAYOUT_TYPE_DIM = 3;

    public static final int LANDSCAPE =
            android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE;

    public static final int PORTRAIT = android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;

    public ForceScreenLayout(int screenOrientation) {
        super(0, 0, WindowManager.LayoutParams.TYPE_TOAST,
                FLAG_FULLSCREEN | FLAG_NOT_FOCUSABLE | FLAG_KEEP_SCREEN_ON, PixelFormat.RGBX_8888);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            this.type = TYPE_APPLICATION_OVERLAY;
        } else if (Build.VERSION.SDK_INT == Build.VERSION_CODES.N_MR1) {
            this.type = TYPE_PHONE;
        } else {
            this.type = TYPE_TOAST;
        }

        this.gravity = Gravity.TOP;
        if (screenOrientation == LAYOUT_TYPE_LANDSCAPE) {
            this.screenOrientation = LANDSCAPE;
        } else if (screenOrientation == LAYOUT_TYPE_PORTRAIT) {
            this.screenOrientation = PORTRAIT;
        } else if (screenOrientation == LAYOUT_TYPE_DIM) {
            this.screenBrightness = WindowManager.LayoutParams.BRIGHTNESS_OVERRIDE_OFF;
            this.buttonBrightness = WindowManager.LayoutParams.BRIGHTNESS_OVERRIDE_OFF;
        }
    }
}