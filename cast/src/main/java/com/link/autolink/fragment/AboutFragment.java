package com.link.autolink.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.car.autolink.update.AppInfo;
import com.link.autolink.R;
import com.link.autolink.activity.MainActivity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/8/14
 */
public class AboutFragment extends Fragment implements IBackPressed {

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_about, container, false);
    }

    @Override
    public boolean onBackPressed() {
        //((MainActivity) requireActivity()).backToMainFragment();
        return true;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        TextView help = view.findViewById(R.id.help_tv);
        TextView privacy = view.findViewById(R.id.privacy_tv);
        TextView version = view.findViewById(R.id.version_tv);
        version.setText(
                getResources().getString(R.string.app_version,
                        AppInfo.getVersionName(getContext())));
        help.setOnClickListener(v -> gotoHelpFragment());
        privacy.setOnClickListener(v -> gotoPolicyFragment());
    }

    private void gotoPolicyFragment() {
        Fragment policyFragment = new PolicyFragment();
//        requireActivity().getSupportFragmentManager().beginTransaction()
//                .replace(R.id.fragment, policyFragment)
//                .commit();
    }

    private void gotoHelpFragment() {
        Fragment helpFragment = new HelpFragment();
//        requireActivity().getSupportFragmentManager().beginTransaction()
//                .replace(R.id.fragment, helpFragment)
//                .commit();
    }
}