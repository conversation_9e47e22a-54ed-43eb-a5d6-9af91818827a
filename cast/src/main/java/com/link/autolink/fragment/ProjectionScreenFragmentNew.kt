package com.link.autolink.fragment

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.fragment.app.Fragment
import com.car.autolink.events.ProjectionStatusEvent
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.link.autolink.CastWifiP2pManager
import com.link.autolink.ProjectionControl
import com.link.autolink.R
import com.link.autolink.activity.BrowserActivity
import com.link.autolink.activity.WifiP2pActivity
import com.link.autolink.events.P2pConnectionEvent
import com.link.autolink.widget.ControlPanelView
import com.link.autolink.widget.OnControlPanelListener
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class ProjectionScreenFragmentNew : Fragment() {
    private lateinit var projectionScreenMenu: ControlPanelView

    override fun onAttach(context: Context) {
        super.onAttach(context)
        EventBus.getDefault().register(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d("MyFragment", "onCreate called") // <-- 添加日志
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_projection_screen_new, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        projectionScreenMenu = view.findViewById(R.id.psm)

        projectionScreenMenu.setOnControlPanelListener(object : OnControlPanelListener {
            override fun onConnectClick() {
                if (CastWifiP2pManager.isConnected) {
                    CastWifiP2pManager.disconnect()
                } else {
                    val intent = Intent(requireContext(), WifiP2pActivity::class.java)
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(intent)
                }
            }

            override fun onAntiDisconnectClick() {

            }

            override fun onOnlineVideoClick() {
                Log.d(TAG, "onOnlineVideoClick")
                ProjectionControl.requestPresentation()
                val intent = Intent(requireContext(), BrowserActivity::class.java)
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
            }

            override fun onScreenMirroringClick() {
                ProjectionControl.requestProjection()
            }

            override fun onSettingsClick() {

            }

        })
        requestPermissions()
    }

    override fun onDetach() {
        super.onDetach()
        EventBus.getDefault().unregister(this)
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onP2pConnectionEvent(event: P2pConnectionEvent) {
        Log.d(TAG, "onP2pConnectionEvent $event")
        projectionScreenMenu.updateConnectionState(event.state == 4, event.deviceName)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onProjectionStatusEvent(event: ProjectionStatusEvent) {
        Log.d(TAG, "onProjectionStatusEvent $event")
        projectionScreenMenu.updateStreamingStatus(event.currentState, event.currentMode)
    }


    private fun requestPermissions() {
        if (XXPermissions.isGranted(
                requireContext(),
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) PERMISSIONS else ADVANCE_PERMISSIONS
            )
        ) {
            CastWifiP2pManager.requestConnectionInfo()
        }
        XXPermissions.with(this)
            .permission(if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) PERMISSIONS else ADVANCE_PERMISSIONS)
            .request { permissions, allGranted ->
                {
                    if (allGranted) {
                        CastWifiP2pManager.requestConnectionInfo()
                    }
                }
            }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d("MyFragment", "onConfigurationChanged called")
    }

    companion object {
        private const val TAG = "ProjectionScreenFragmen"
        private val PERMISSIONS: Array<String?> = arrayOf(
            Permission.ACCESS_COARSE_LOCATION,
            Permission.ACCESS_FINE_LOCATION,
        )

        @RequiresApi(api = Build.VERSION_CODES.S)
        private val ADVANCE_PERMISSIONS: Array<String?> = arrayOf(
            Permission.ACCESS_COARSE_LOCATION,
            Permission.ACCESS_FINE_LOCATION,
            Permission.NEARBY_WIFI_DEVICES,
            Permission.BLUETOOTH_SCAN,
            Permission.BLUETOOTH_CONNECT,
            Permission.BLUETOOTH_ADVERTISE,
            Permission.POST_NOTIFICATIONS
        )
    }

}