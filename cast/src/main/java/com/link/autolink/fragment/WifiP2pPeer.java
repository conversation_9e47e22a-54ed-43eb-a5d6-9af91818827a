package com.link.autolink.fragment;

import android.net.wifi.p2p.WifiP2pDevice;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
public class WifiP2pPeer {
    static final int MIRRORING = 5;

    WifiP2pDevice mDevice;
    int state;

    public WifiP2pPeer(WifiP2pDevice device, int state) {
        mDevice = device;
        this.state = state;
    }

    public WifiP2pPeer(WifiP2pDevice device) {
        mDevice = device;
    }

    public void setState(int state) {
        this.state = state;
    }
}
