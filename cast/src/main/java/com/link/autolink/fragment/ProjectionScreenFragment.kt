package com.link.autolink.fragment

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import com.car.autolink.events.ProjectionRequestEvent
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.link.autolink.CastWifiP2pManager
import com.link.autolink.ProjectionControl
import com.link.autolink.R
import com.link.autolink.activity.BrowserActivity
import com.link.autolink.activity.WifiP2pActivity
import com.link.autolink.events.P2pConnectionEvent
import com.link.autolink.widget.ProjectionScreenMenu
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.jvm.java


class ProjectionScreenFragment : Fragment() {
    private lateinit var projectionScreenMenu: ProjectionScreenMenu

    override fun onAttach(context: Context) {
        super.onAttach(context)
        EventBus.getDefault().register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_projection_screen, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        projectionScreenMenu = view.findViewById(R.id.psm)
        projectionScreenMenu.setOnscreenMenuClickListener(object :
            ProjectionScreenMenu.OnScreenMenuClickListener {
            override fun onOnlineVideoClick(view: View?) {
                Log.d(TAG, "onOnlineVideoClick")
                val intent = Intent(requireContext(), BrowserActivity::class.java)
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)

            }

            override fun onProjectionScreenClick(view: View?) {
                ProjectionControl.requestProjection()
            }

            override fun onMuteClick(view: View?) {

            }

            override fun onConnectStatusClick(view: View?) {
                if (CastWifiP2pManager.isConnected){
                    CastWifiP2pManager.disconnect()
                }else {
                    val intent = Intent(requireContext(), WifiP2pActivity::class.java)
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(intent)
                }
            }

        })
        requestPermissions()
    }

    override fun onDetach() {
        super.onDetach()
        EventBus.getDefault().unregister(this)
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onP2pConnectionEvent(event: P2pConnectionEvent) {
        Log.d(TAG, "onP2pConnectionEvent $event")
        projectionScreenMenu.setConnectStatus(event.state, event.deviceName)
    }


    private fun requestPermissions() {
        if (XXPermissions.isGranted(
                requireContext(),
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) PERMISSIONS else ADVANCE_PERMISSIONS
            )
        ) {
            CastWifiP2pManager.requestConnectionInfo()
        }
        XXPermissions.with(this)
            .permission(if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) PERMISSIONS else ADVANCE_PERMISSIONS)
            .request { permissions, allGranted ->
                {
                    if (allGranted) {
                        CastWifiP2pManager.requestConnectionInfo()
                    }
                }
            }
    }

    companion object {
        private const val TAG = "ProjectionScreenFragmen"
        private val PERMISSIONS: Array<String?> = arrayOf(
            Permission.ACCESS_COARSE_LOCATION,
            Permission.ACCESS_FINE_LOCATION,
        )

        @RequiresApi(api = Build.VERSION_CODES.S)
        private val ADVANCE_PERMISSIONS: Array<String?> = arrayOf(
            Permission.ACCESS_COARSE_LOCATION,
            Permission.ACCESS_FINE_LOCATION,
            Permission.NEARBY_WIFI_DEVICES,
            Permission.BLUETOOTH_SCAN,
            Permission.BLUETOOTH_CONNECT,
            Permission.BLUETOOTH_ADVERTISE,
            Permission.POST_NOTIFICATIONS
        )
    }

}