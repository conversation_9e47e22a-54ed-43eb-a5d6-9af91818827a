package com.link.autolink.fragment;

import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
public class WifiP2pPeerDialogFragment extends DialogFragment {
    private static final String KEY_DIALOG_ID = "key_dialog_id";
    private static final String KEY_PARENT_FRAGMENT_ID = "key_parent_fragment_id";
    private int mDialogId;
    private Fragment mParentFragment;
    private DialogInterface.OnCancelListener mOnCancelListener;
    private DialogInterface.OnDismissListener mOnDismissListener;

    WifiP2pPeerDialogFragment(DialogCreatable dialogCreatable, int dialogId) {
        mDialogId = dialogId;
        if (!(dialogCreatable instanceof Fragment)) {
            throw new IllegalArgumentException("fragment argument must be an instance of "
                    + Fragment.class.getName());
        }
        mParentFragment = (Fragment) dialogCreatable;
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (mParentFragment != null) {
            outState.putInt(KEY_DIALOG_ID, mDialogId);
            outState.putInt(KEY_PARENT_FRAGMENT_ID, mParentFragment.getId());
        }
    }

    @Override
    public void onStart() {
        super.onStart();
//        if (mParentFragment != null && mParentFragment instanceof WifiFragment) {
//            ((WifiFragment) mParentFragment).onDialogShowing();
//        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            mDialogId = savedInstanceState.getInt(KEY_DIALOG_ID, 0);
            mParentFragment = getParentFragment();
            int parentFragmentId = savedInstanceState.getInt(KEY_PARENT_FRAGMENT_ID, -1);
            if (mParentFragment == null) {
                mParentFragment = getFragmentManager().findFragmentById(parentFragmentId);
            }
            if (!(mParentFragment instanceof DialogCreatable)) {
                throw new IllegalArgumentException(
                        (mParentFragment != null
                                ? mParentFragment.getClass().getName()
                                : parentFragmentId)
                                + " must implement "
                                + DialogCreatable.class.getName());
            }
//            if (mParentFragment instanceof WifiFragment) {
//                // restore mDialogFragment in mParentFragment
//                ((WifiFragment) mParentFragment).mDialogFragment = this;
//            }
        }
        return ((DialogCreatable) mParentFragment).onCreateDialog(mDialogId);
    }

    @Override
    public void onCancel(@NonNull DialogInterface dialog) {
        super.onCancel(dialog);
        if (mOnCancelListener != null) {
            mOnCancelListener.onCancel(dialog);
        }
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (mOnDismissListener != null) {
            mOnDismissListener.onDismiss(dialog);
        }
    }

    public int getDialogId() {
        return mDialogId;
    }

    @Override
    public void onDetach() {
        super.onDetach();
//        if (mParentFragment instanceof WifiFragment) {
//            // in case the dialog is not explicitly removed by removeDialog()
//            if (((WifiFragment) mParentFragment).mDialogFragment == this) {
//                ((WifiFragment) mParentFragment).mDialogFragment = null;
//            }
//        }
    }
}
