package com.link.autolink.fragment;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.link.autolink.R;
import com.link.autolink.activity.MainActivity;
import com.link.autolink.utils.Policy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
public abstract class BaseFragment extends Fragment implements
        Policy.RuleListener {
    private static final String TAG = "BaseFragment";
    public static final int WIFI_MODE = 0;
    public static final int USB_MODE = 1;

    Activity mActivity;
    private static final String[] PERMISSIONS = {
            Permission.ACCESS_COARSE_LOCATION,
            Permission.ACCESS_FINE_LOCATION,
    };

    @RequiresApi(api = Build.VERSION_CODES.S)
    private static final String[] ADVANCE_PERMISSIONS = {
            Permission.ACCESS_COARSE_LOCATION,
            Permission.ACCESS_FINE_LOCATION,
            Permission.NEARBY_WIFI_DEVICES,
            Permission.BLUETOOTH_SCAN,
            Permission.BLUETOOTH_CONNECT,
            Permission.BLUETOOTH_ADVERTISE,
            Permission.POST_NOTIFICATIONS
    };
    private Dialog mPolicyDialog;

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mActivity = getActivity();
        mPolicyDialog =
                Policy.getInstance()
                        .showRuleDialog(mActivity, getResources().getString(R.string.policy_title),
                                R.color.link, this);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        setHasOptionsMenu(true);
        return inflater.inflate(getFragmentLayout(), container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        injectViews(view);
    }

    /**
     * getFragmentLayout.
     *
     * @return layout id
     */
    protected abstract int getFragmentLayout();

    /**
     * inject views.
     *
     * @param view views
     */
    protected abstract void injectViews(final View view);

    /**
     * status changed.
     *
     * @param state autolink state
     */
    public abstract void onStatusChanged(int state);

    /**
     * start autolink.
     */
    public abstract void start();

    /**
     * shutdown autolink
     */
    public abstract void shutdown();


    public void permissionTask() {

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            XXPermissions.with(this)
                    .permission(PERMISSIONS)
                    .request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                            if (allGranted) {
                                start();
                            }
                        }

                        @Override
                        public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                            Toast.makeText(mActivity, R.string.permission_describe, Toast.LENGTH_SHORT).show();
                        }
                    });
        } else {
            XXPermissions.with(this)
                    .permission(ADVANCE_PERMISSIONS)
                    .request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                            if (allGranted) {
                                start();
                            }
                        }

                        @Override
                        public void onDenied(@NonNull List<String> permissions, boolean doNotAskAgain) {
                            Toast.makeText(mActivity, R.string.permission_describe, Toast.LENGTH_SHORT).show();
                        }
                    });
        }
    }

    public boolean hasPermissions() {
        Context context = getContext();
        if (context != null) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                return XXPermissions.isGranted(requireContext(), PERMISSIONS);
            }
            return XXPermissions.isGranted(requireContext(), ADVANCE_PERMISSIONS);
        } else {
            return false;
        }
    }

    @Override
    public void onDestroy() {
        if (mPolicyDialog != null) {
            mPolicyDialog.dismiss();
        }
        super.onDestroy();
    }

    @Override
    public void rule(boolean agree) {
        if (agree) {
            if (!hasPermissions()) {
                new AlertDialog.Builder(requireContext())
                        .setCancelable(false)
                        .setTitle(R.string.permission_title)
                        .setMessage(R.string.permission_msg)
                        .setPositiveButton(R.string.dlg_ok, (dialog, which) -> permissionTask())
                        .setNegativeButton(R.string.dlg_cancel, (dialog, which) -> {

                        })
                        .create()
                        .show();
            } else {
                permissionTask();
            }
        } else {
            if (!mActivity.isFinishing()) {
                mActivity.finish();
            }
        }
    }

    @Override
    public void policyClick() {
        if (mPolicyDialog != null) {
            mPolicyDialog.dismiss();
        }
        //((MainActivity) mActivity).onHelp();
    }
}
