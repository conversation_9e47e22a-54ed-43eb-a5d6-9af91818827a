package com.link.autolink.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.car.autolink.utils.Platform;
import com.link.autolink.R;
import com.link.autolink.activity.MainActivity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/8/11
 */
public class PolicyFragment extends Fragment implements IBackPressed {
    private static final String TAG = "PolicyFragment";
    private static final String URL = "http://support.autolink.top/privacy/privacy.html";
    private static final String PRIVACY_URL = "file:///android_asset/privacy.html";
    private static final String PRIVACY_CN_URL = "file:///android_asset/privacy_cn.html";
    private WebView mWebView;
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_policy, container, false);
    }

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mWebView = view.findViewById(R.id.policy);
        WebSettings settings = mWebView.getSettings();
        settings.setSupportMultipleWindows(true);
        settings.setJavaScriptEnabled(true);
        settings.setJavaScriptCanOpenWindowsAutomatically(true);
        settings.setMinimumFontSize(settings.getMinimumLogicalFontSize() + 8);
        settings.setAllowFileAccess(false);
        mWebView.setWebViewClient(new MyWebViewClient());
        if (Platform.isChina(this.getContext())) {
            mWebView.loadUrl(PRIVACY_CN_URL);
        } else {
            mWebView.loadUrl(PRIVACY_URL);
        }
    }

    @Override
    public boolean onBackPressed() {
        //((MainActivity) requireActivity()).backToAboutFragment();
        return true;
    }

    private class MyWebViewClient extends WebViewClient {
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            mWebView.loadUrl(request.getUrl().toString());
            return true;
        }
    }
}