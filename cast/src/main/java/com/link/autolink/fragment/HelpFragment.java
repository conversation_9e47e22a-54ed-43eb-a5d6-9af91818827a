package com.link.autolink.fragment;

import static android.content.Context.POWER_SERVICE;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.Fragment;

import com.car.autolink.utils.Platform;
import com.link.autolink.R;
import com.link.autolink.activity.MainActivity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
public class HelpFragment extends Fragment implements IBackPressed {
    private WebView mWebView;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_help, container, false);
    }

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mWebView = view.findViewById(R.id.wb_help);
        WebSettings webSettings = mWebView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setSupportZoom(true);
        mWebView.addJavascriptInterface(this, "help");
        if (Platform.isChina(this.getContext())) {
            mWebView.loadUrl("file:///android_asset/help_cn.html");
        } else {
            mWebView.loadUrl("file:///android_asset/help.html");
        }
    }

    /**
     * html back.
     *
     * @return true or false
     */
    public boolean goBack() {
        if (mWebView.canGoBack()) {
            mWebView.goBack();
            return true;
        }
        return false;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @JavascriptInterface
    public void ignoreBatteryOptimization() {
        try {
            PowerManager powerManager =
                    (PowerManager) getActivity().getSystemService(POWER_SERVICE);
            boolean hasIgnored =
                    powerManager.isIgnoringBatteryOptimizations(getActivity().getPackageName());
            if (!hasIgnored) {
                @SuppressLint("BatteryLife") Intent intent =
                        new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                intent.setData(Uri.parse("package:" + getActivity().getPackageName()));
                startActivity(intent);
            } else {
                Toast.makeText(requireContext().getApplicationContext(),
                        getString(R.string.battery_life_msg),
                        Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Log.e("ex", e.getMessage());
        }
    }

    @JavascriptInterface
    public void toSelfSetting() {
        goAppDetails();
    }

    private void goAppDetails() {
        Intent mIntent = new Intent();
        mIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mIntent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        mIntent.setData(Uri.fromParts("package", requireContext().getPackageName(), null));
        startActivity(mIntent);
    }


    @Override
    public boolean onBackPressed() {
        //((MainActivity) requireActivity()).backToAboutFragment();
        return true;
    }
}
