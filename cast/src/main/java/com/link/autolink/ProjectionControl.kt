package com.link.autolink

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.car.autolink.AutolinkControl
import com.car.autolink.events.ProjectionRequestEvent
import com.car.autolink.module.audio.BtAudioAdapter
import com.car.autolink.module.transport.DataConnection
import com.car.autolink.module.transport.ITransport
import com.car.autolink.module.transport.wifi.TcpConnection
import com.car.autolink.module.video.ProjectionRenderer
import com.car.autolink.module.video.ProjectionRendererNew
import com.link.autolink.presentation.ProjectionController
import org.greenrobot.eventbus.EventBus
import kotlin.concurrent.thread

@SuppressLint("StaticFieldLeak")
object ProjectionControl {
    private var context: Context? = null
    private var autolinkControl: AutolinkControl? = null
    private var connection: DataConnection? = null

    fun init(context: Context) {
        this.context = context
    }

    @Synchronized
    fun startConnect() {
        Log.d("ProjectionControl", "startConnect")

        // 如果已经有实例，直接返回
        if (autolinkControl != null) {
            Log.d("ProjectionControl", "AutolinkControl 已存在，跳过创建")
            return
        }

        // 创建 AutolinkControl 实例
        autolinkControl = AutolinkControl.Builder(context)
            .setAudio(BtAudioAdapter(context))
            .setVideo(ProjectionRendererNew(context))
            .build()

        // 启动 Socket 连接
        if (connection == null) {
            startConnectionEstablishing(TcpConnection())
        }
    }

    private fun startConnectionEstablishing(connection: DataConnection) {
        this.connection = connection
        thread {
            try {
                connection.start(object : DataConnection.Callback {
                    override fun onConnected(transport: ITransport) {
                        Log.d("ProjectionControl", "Socket 连接成功")
                        autolinkControl?.startConnect(transport)
                        ProjectionController.getInstance().isConnected = true
                    }

                    override fun onDisconnected() {
                        Log.d("ProjectionControl", "Socket 连接断开")
                        ProjectionController.getInstance().isConnected = false
                        release()
                    }
                })
            } catch (e: Exception) {
                Log.e("ProjectionControl", "Socket 连接异常", e)
                e.printStackTrace()
            }
        }
    }

    fun onActivityResult(resultCode: Int, intent: Intent, secondary: Boolean) {
        autolinkControl?.onActivityResult(resultCode, intent, secondary)
    }

    fun requestPresentation() {
        autolinkControl?.requestPresentation()
    }

    fun requestProjection() {
        autolinkControl?.requestProjection()
    }

    /**
     * 检查是否已经有 AutolinkControl 实例
     */
    fun isConnected(): Boolean {
        return autolinkControl != null
    }

    @Synchronized
    fun release() {
        Log.d("ProjectionControl", "release")

        connection?.shutdown()
        connection = null

        autolinkControl?.release()
        autolinkControl = null
    }
}