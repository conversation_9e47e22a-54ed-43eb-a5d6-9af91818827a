package com.link.autolink.widget

import android.animation.AnimatorInflater
import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Configuration
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.Log
import android.view.Gravity
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.core.content.ContextCompat
import com.car.autolink.events.ProjectionStatusEvent
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import com.google.android.material.imageview.ShapeableImageView
import com.google.android.material.shape.ShapeAppearanceModel
import com.link.autolink.R

interface OnControlPanelListener {
    fun onConnectClick()
    fun onAntiDisconnectClick()
    fun onOnlineVideoClick()
    fun onScreenMirroringClick()
    fun onSettingsClick()
}


class ControlPanelView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    private val TAG = "ControlPanelView"
    private var controlPanelListener: OnControlPanelListener? = null

    // --- UI 组件 ---
    private lateinit var statusPanel: ShapeableImageView
    private lateinit var connectButton: ExtendedFloatingActionButton
    private lateinit var antiDisconnectButton: ExtendedFloatingActionButton
    private lateinit var settingsButton: ExtendedFloatingActionButton
    private lateinit var onlineVideoButton: ExtendedFloatingActionButton
    private lateinit var screenMirroringButton: ExtendedFloatingActionButton

    // ✅ 新增的按钮容器
    private lateinit var topButtonGroup: LinearLayout
    private lateinit var bottomButtonGroup: LinearLayout

    init {
        val padding = (24 * resources.displayMetrics.density).toInt()
        setPadding(padding, padding, padding, padding)
        createChildViews()
        updateLayoutForOrientation(resources.configuration.orientation)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "onConfigurationChanged: New orientation is ${newConfig.orientation}")
        updateLayoutForOrientation(newConfig.orientation)
    }

    private fun createChildViews() {
        val cornerRadius = (8 * resources.displayMetrics.density)
        val applyFabStyle: (ExtendedFloatingActionButton) -> Unit = { fab ->
            fab.backgroundTintList = ColorStateList.valueOf(
                ContextCompat.getColor(
                    context,
                    R.color.your_app_primary_color
                )
            )
            fab.setTextColor(ContextCompat.getColor(context, android.R.color.white))
            fab.isAllCaps = false
            fab.typeface = Typeface.create("sans-serif-medium", Typeface.NORMAL)
            fab.textSize = 16f

            // ✅ 修正1：文字居中
            // 明确设置没有图标，并将内容重心设为居中
            fab.icon = null
            fab.gravity = Gravity.CENTER
            fab.shapeAppearanceModel = ShapeAppearanceModel.builder()
                .setAllCornerSizes(cornerRadius)
                .build()
        }


        connectButton = ExtendedFloatingActionButton(context).apply {
            id = R.id.cp_btn_connect
            text = "连接设备"
            setOnClickListener { controlPanelListener?.onConnectClick() }
            applyFabStyle(this)
        }

        statusPanel = ShapeableImageView(context).apply {
            id = R.id.cp_panel_status
            setImageResource(R.drawable.wlink)
            scaleType = ImageView.ScaleType.CENTER_CROP
            elevation = (8 * resources.displayMetrics.density)
//            val cornerRadius = (1 * resources.displayMetrics.density)
//            shapeAppearanceModel = ShapeAppearanceModel.builder()
//                .setAllCornerSizes(cornerRadius)
//                .build()
        }

        antiDisconnectButton = ExtendedFloatingActionButton(context).apply {
            text = "防断连设置"
            setOnClickListener { controlPanelListener?.onAntiDisconnectClick() }
            applyFabStyle(this)
        }
        settingsButton = ExtendedFloatingActionButton(context).apply {
            text = "设置"
            setOnClickListener { controlPanelListener?.onSettingsClick() }
            applyFabStyle(this)
        }
        onlineVideoButton = ExtendedFloatingActionButton(context).apply {
            text = "在线视频"
            setOnClickListener { controlPanelListener?.onOnlineVideoClick() }
            applyFabStyle(this)
        }
        screenMirroringButton = ExtendedFloatingActionButton(context).apply {
            text = "镜像投屏"
            setOnClickListener { controlPanelListener?.onScreenMirroringClick() }
            applyFabStyle(this)
        }

        // --- 将按钮放入新的容器中 ---
        val buttonMargin = (16 * resources.displayMetrics.density).toInt()
        val buttonHeight = (72 * resources.displayMetrics.density).toInt()
        val buttonParams = LinearLayout.LayoutParams(0, buttonHeight, 1f)

        topButtonGroup = LinearLayout(context).apply {
            id = R.id.cp_top_button_group
            orientation = LinearLayout.HORIZONTAL
            addView(antiDisconnectButton, buttonParams)
            addView(View(context), LinearLayout.LayoutParams(buttonMargin, 0))
            addView(settingsButton, buttonParams)
        }

        bottomButtonGroup = LinearLayout(context).apply {
            id = R.id.cp_bottom_button_group
            orientation = LinearLayout.HORIZONTAL
            addView(onlineVideoButton, buttonParams)
            addView(View(context), LinearLayout.LayoutParams(buttonMargin, 0))
            addView(screenMirroringButton, buttonParams)
        }

        // --- 将最终的组件添加到主视图 ---
        addView(connectButton)
        addView(statusPanel)
        addView(topButtonGroup)
        addView(bottomButtonGroup)
    }

    private fun updateLayoutForOrientation(orientation: Int) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            applyLandscapeLayout()
        } else {
            applyPortraitLayout()
        }
    }

    private fun applyPortraitLayout() {
        val verticalMargin = (24 * resources.displayMetrics.density).toInt()
        val buttonRowMargin = (30 * resources.displayMetrics.density).toInt()
        val connectButtonHeight = (72 * resources.displayMetrics.density).toInt()
        connectButton.layoutParams =
            LayoutParams(LayoutParams.WRAP_CONTENT, connectButtonHeight).apply {
                addRule(ALIGN_PARENT_TOP)
                addRule(ALIGN_PARENT_START)
                setMargins(0, 0, 0, verticalMargin)
            }

        statusPanel.layoutParams = LayoutParams(
            LayoutParams.MATCH_PARENT,
            (150 * resources.displayMetrics.density).toInt()
        ).apply {
            addRule(BELOW, connectButton.id)
            setMargins(0, 0, 0, verticalMargin)
        }

        topButtonGroup.layoutParams =
            LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT).apply {
                addRule(BELOW, statusPanel.id)
                setMargins(0, 0, 0, buttonRowMargin)
            }

        bottomButtonGroup.layoutParams =
            LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT).apply {
                addRule(BELOW, topButtonGroup.id)
                setMargins(0, 0, 0, 0)
            }
    }

    private fun applyLandscapeLayout() {
        val margin = (16 * resources.displayMetrics.density).toInt()
        val leftColumnWidth = (resources.displayMetrics.widthPixels / 2).toInt()
        val connectButtonHeight = (72 * resources.displayMetrics.density).toInt()
        // --- 左侧列 ---
        connectButton.layoutParams =
            LayoutParams(LayoutParams.WRAP_CONTENT, connectButtonHeight).apply {
                // ✅ 要求 1: 始终位于左上角
                addRule(ALIGN_PARENT_START)
                addRule(ALIGN_PARENT_TOP)
                // 移除了之前使其居中的所有逻辑
                setMargins(0, 0, 0, margin)
            }

        statusPanel.layoutParams = LayoutParams(leftColumnWidth, LayoutParams.MATCH_PARENT).apply {
            addRule(ALIGN_PARENT_START)
            addRule(BELOW, connectButton.id)
            addRule(ALIGN_PARENT_BOTTOM)
            setMargins(0, 0, 0, margin)
        }


        bottomButtonGroup.layoutParams =
            LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                addRule(RIGHT_OF, statusPanel.id)
                addRule(ALIGN_PARENT_END)
                addRule(ALIGN_PARENT_BOTTOM)
                setMargins(margin, 0, 0, margin)
            }

        topButtonGroup.layoutParams =
            LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
                addRule(RIGHT_OF, statusPanel.id)
                addRule(ALIGN_PARENT_END)
                addRule(ABOVE, bottomButtonGroup.id)
                setMargins(margin, 0, 0, 2 * margin)
            }
    }

    // --- 公共 API ---
    fun setOnControlPanelListener(listener: OnControlPanelListener) {
        this.controlPanelListener = listener
    }

    fun updateConnectionState(isConnected: Boolean, deviceName: String? = null) {
        if (isConnected && deviceName != null) {
            // --- 已连接状态 ---
            connectButton.text = deviceName

            // 设置为“已连接”的特殊外观
            connectButton.setTextColor(ContextCompat.getColor(context, android.R.color.white))
            connectButton.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.connected_success_green))

        } else {
            // --- 未连接状态 ---
            connectButton.text = "连接设备"

            // 恢复为 App 默认的主色调外观
            connectButton.setTextColor(ContextCompat.getColor(context, android.R.color.white))
            connectButton.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.your_app_primary_color))
        }
    }

    fun updateStreamingStatus(status: ProjectionStatusEvent.State, mode: Int) {
        // 默认先将两个按钮都恢复为非激活状态
        setButtonActive(onlineVideoButton, false)
        setButtonActive(screenMirroringButton, false)

        // 只有在 RUNNING 状态下才高亮其中一个
        if (status == ProjectionStatusEvent.State.RUNNING) {
            if (mode == 0) { // 模式0: 镜像投屏
                setButtonActive(screenMirroringButton, true)
            } else if (mode == 1) { // 模式1: 在线视频
                setButtonActive(onlineVideoButton, true)
            }
        }
    }

    private fun setButtonActive(button: ExtendedFloatingActionButton, isActive: Boolean) {
        if (isActive) {
            // 设置为“激活”样式
            button.backgroundTintList = ColorStateList.valueOf(
                ContextCompat.getColor(
                    context,
                    R.color.active_green
                )
            ) // 使用绿色
            button.setTextColor(ContextCompat.getColor(context, android.R.color.white))
        } else {
            // 恢复为默认样式
            button.backgroundTintList = ColorStateList.valueOf(
                ContextCompat.getColor(
                    context,
                    R.color.your_app_primary_color
                )
            ) // 恢复主色
            button.setTextColor(ContextCompat.getColor(context, android.R.color.white))
        }
    }
}