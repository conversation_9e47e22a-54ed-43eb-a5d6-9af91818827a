package com.link.autolink.widget

import androidx.fragment.app.FragmentManager

/**
 * 输入框Dialog帮助类
 * 用于统一管理输入框的显示和回调
 */
object InputDialogHelper {
    
    /**
     * 显示贴键盘输入框
     * @param fragmentManager FragmentManager
     * @param initialText 初始文本
     * @param listener 输入监听器
     */
    fun showKeyboardInputDialog(
        fragmentManager: FragmentManager,
        initialText: String = "",
        listener: KeyboardInputDialog.InputDialogListener
    ) {
        val dialog = KeyboardInputDialog.newInstance(initialText)
        dialog.listener = listener
        dialog.show(fragmentManager, "KeyboardInputDialog")
    }
    
    /**
     * 创建统一的输入监听器适配器
     * 将KeyboardInputDialog的监听器转换为InputBottomSheetFragment的监听器
     */
    fun createInputListener(
        onTextChanged: (String) -> Unit,
        onInputDone: () -> Unit
    ): KeyboardInputDialog.InputDialogListener {
        return object : KeyboardInputDialog.InputDialogListener {
            override fun onTextChanged(text: String) {
                onTextChanged(text)
            }
            
            override fun onInputDone() {
                onInputDone()
            }
        }
    }
}
