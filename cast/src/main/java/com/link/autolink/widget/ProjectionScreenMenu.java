package com.link.autolink.widget;

import android.content.Context;
import android.content.res.Configuration;
import android.util.AttributeSet;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.link.autolink.R;


public class ProjectionScreenMenu extends LinearLayout {
    private final RelativeLayout onlineVideoLayout;
    private final RelativeLayout projectionScreenLayout;
    private final TextView currentDevice;
    private final TextView currentDeviceName;
    private final LinearLayout connectionStatusLayout;
    private final Button connectStatusButton;
    private final ImageButton muteButton;
    private final View dividerView;

    public interface OnScreenMenuClickListener {
        void onOnlineVideoClick(View view);

        void onProjectionScreenClick(View view);

        void onMuteClick(View view);

        void onConnectStatusClick(View view);
    }

    private OnScreenMenuClickListener onScreenMenuClickListener;


    public ProjectionScreenMenu(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        View.inflate(context, R.layout.include_projection_screen_menu, this);

        // 初始化UI控件
        this.onlineVideoLayout = findViewById(R.id.rl_online_video);
        this.dividerView = findViewById(R.id.view_spot);
        this.projectionScreenLayout = findViewById(R.id.rl_mirror_projection_screen);
        this.muteButton = findViewById(R.id.imgBtn_mute);
        this.connectStatusButton = findViewById(R.id.btn_connect_state);
        this.connectionStatusLayout = findViewById(R.id.rl_connect_state);
        this.currentDevice = findViewById(R.id.tv_current_device);
        this.currentDeviceName = findViewById(R.id.tv_current_device_name);

        setOrientation(VERTICAL);
        updateLayoutOrientation();
        this.onlineVideoLayout.setOnClickListener(view -> {
            if (onScreenMenuClickListener != null) {
                onScreenMenuClickListener.onOnlineVideoClick(view);
            }
        });

        this.projectionScreenLayout.setOnClickListener(view -> {
            if (onScreenMenuClickListener != null) {
                onScreenMenuClickListener.onProjectionScreenClick(view);
            }
        });

        this.muteButton.setOnClickListener(view -> {
            if (onScreenMenuClickListener != null) {
                onScreenMenuClickListener.onMuteClick(view);
            }
        });

        this.connectStatusButton.setOnClickListener(view -> {
            if (onScreenMenuClickListener != null) {
                onScreenMenuClickListener.onConnectStatusClick(view);
            }

        });
    }


    public final void updateLayoutOrientation() {
        int orientation = getResources().getConfiguration().orientation;
        int marginSize = (int) getResources().getDimension(R.dimen.item_menu_margins);

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // 横屏布局
            setOrientation(HORIZONTAL);
            setViewLayoutParams(onlineVideoLayout, 0, LayoutParams.MATCH_PARENT, marginSize, 0, 0, marginSize);
            setViewLayoutParams(projectionScreenLayout, 0, LayoutParams.MATCH_PARENT, marginSize, 0, marginSize, marginSize);
            setViewLayoutParams(connectionStatusLayout, 0, LayoutParams.MATCH_PARENT, 0, 0, marginSize, marginSize);
        } else {
            // 竖屏布局
            setOrientation(VERTICAL);
            setViewLayoutParams(onlineVideoLayout, LayoutParams.MATCH_PARENT, 0, marginSize, 0, marginSize, 0);
            setViewLayoutParams(projectionScreenLayout, LayoutParams.MATCH_PARENT, 0, marginSize, marginSize, marginSize, marginSize);
            setViewLayoutParams(connectionStatusLayout, LayoutParams.MATCH_PARENT, 0, marginSize, 0, marginSize, marginSize);
        }
    }

    private void setViewLayoutParams(View view, int width, int height, int leftMargin, int topMargin, int rightMargin, int bottomMargin) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) view.getLayoutParams();
        layoutParams.width = width;
        layoutParams.height = height;
        layoutParams.setMargins(leftMargin, topMargin, rightMargin, bottomMargin);
        view.setLayoutParams(layoutParams);
    }


    @Override
    public final void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        updateLayoutOrientation();
    }

    @Override
    public final void onVisibilityChanged(@NonNull View view, int visibility) {
        super.onVisibilityChanged(view, visibility);
        updateLayoutOrientation();
    }

    /**
     * 设置静音按钮点击监听器
     */
    public void setOnscreenMenuClickListener(OnScreenMenuClickListener listener) {
        this.onScreenMenuClickListener = listener;
    }

    public void setConnectStatus(int state, String deviceName) {
        if (state == 1) {
            currentDeviceName.setVisibility(VISIBLE);
            currentDeviceName.setText(getContext().getString(R.string.projection_connecting));
        } else if (state == 0 || state == 3 || state == 5) {
            currentDeviceName.setVisibility(GONE);
            currentDevice.setVisibility(GONE);
            connectStatusButton.setText(getContext().getString(R.string.projection_add_device));
        }else {
            if (state != 4) {
                return;
            }
            currentDevice.setVisibility(VISIBLE);
            currentDeviceName.setVisibility(VISIBLE);
            currentDevice.setText(getContext().getString(R.string.projection_current_device));
            currentDeviceName.setText(deviceName);
            connectStatusButton.setText(getContext().getString(R.string.projection_disconnect));
        }
    }
}

