package com.link.autolink.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.*
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import androidx.fragment.app.DialogFragment
import com.google.android.material.textfield.TextInputEditText
import com.link.autolink.R

/**
 * 贴键盘输入框Dialog
 * 实现自动贴合键盘底部的输入框效果
 */
class KeyboardInputDialog : DialogFragment() {

    interface InputDialogListener {
        fun onTextChanged(text: String)
        fun onInputDone()
    }

    var listener: InputDialogListener? = null
    private var initialText: String? = null
    private lateinit var inputEditText: TextInputEditText
    private lateinit var sendButton: Button
    
    // 键盘高度监听相关
    private var rootView: View? = null
    private var keyboardHeight = 0
    private var isKeyboardShowing = false

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(requireContext(), R.style.KeyboardInputDialogTheme)

        // 设置Dialog属性
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.let { window ->
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            window.setGravity(Gravity.BOTTOM)

            // 设置Dialog占满屏幕宽度
            val layoutParams = window.attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            window.attributes = layoutParams
        }

        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_keyboard_input, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        rootView = view
        inputEditText = view.findViewById(R.id.input_edit_text)
        sendButton = view.findViewById(R.id.send_button)
        
        setupInputField()
        setupKeyboardListener()
    }

    private fun setupInputField() {
        // 设置初始文本
        inputEditText.setText(initialText ?: "")
        inputEditText.setSelection(inputEditText.length())
        
        // 提交操作
        val submitAction = {
            listener?.onInputDone()
            dismiss()
        }
        
        // 发送按钮点击事件
        sendButton.setOnClickListener { submitAction() }
        
        // 文本变化监听
        inputEditText.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                listener?.onTextChanged(s.toString())
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })
        
        // 键盘确认键监听
        inputEditText.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEND || 
                actionId == EditorInfo.IME_ACTION_DONE ||
                actionId == EditorInfo.IME_ACTION_SEARCH) {
                submitAction()
                return@setOnEditorActionListener true
            }
            return@setOnEditorActionListener false
        }
    }

    private fun setupKeyboardListener() {
        rootView?.let { root ->
            root.viewTreeObserver.addOnGlobalLayoutListener {
                val rect = android.graphics.Rect()
                root.getWindowVisibleDisplayFrame(rect)

                val screenHeight = root.rootView.height
                val keypadHeight = screenHeight - rect.bottom

                if (keypadHeight > screenHeight * 0.15) {
                    // 键盘显示
                    if (!isKeyboardShowing) {
                        isKeyboardShowing = true
                        keyboardHeight = keypadHeight
                        onKeyboardShow(keypadHeight)
                    } else if (keyboardHeight != keypadHeight) {
                        // 键盘高度变化（比如切换输入法）
                        keyboardHeight = keypadHeight
                        onKeyboardShow(keypadHeight)
                    }
                } else {
                    // 键盘隐藏
                    if (isKeyboardShowing) {
                        isKeyboardShowing = false
                        onKeyboardHide()
                    }
                }
            }
        }
    }

    private fun onKeyboardShow(keyboardHeight: Int) {
        // 键盘显示时，调整Dialog位置贴合键盘顶部
        dialog?.window?.let { window ->
            val layoutParams = window.attributes
            layoutParams.gravity = Gravity.BOTTOM
            layoutParams.y = keyboardHeight // 设置距离底部的距离为键盘高度
            window.attributes = layoutParams

            // 确保Dialog宽度占满屏幕
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT
            )
        }
    }

    private fun onKeyboardHide() {
        // 键盘隐藏时关闭Dialog
        dismiss()
    }

    fun showKeyboard() {
        inputEditText.requestFocus()
        inputEditText.postDelayed({
            val imm = context?.getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
            imm?.showSoftInput(inputEditText, InputMethodManager.SHOW_IMPLICIT)
        }, 500)
    }

    override fun onStart() {
        super.onStart()
        // 确保Dialog样式正确应用
        dialog?.window?.let { window ->
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT
            )
        }
    }

    override fun onResume() {
        super.onResume()
        showKeyboard()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initialText = arguments?.getString("initial_text", "")
    }

    fun updateText(text: String) {
        initialText = text
        if (::inputEditText.isInitialized) {
            inputEditText.setText(text)
            inputEditText.setSelection(text.length)
        }
    }

    companion object {
        fun newInstance(initialText: String): KeyboardInputDialog {
            val dialog = KeyboardInputDialog()
            val args = Bundle().apply { 
                putString("initial_text", initialText) 
            }
            dialog.arguments = args
            return dialog
        }
    }
}
