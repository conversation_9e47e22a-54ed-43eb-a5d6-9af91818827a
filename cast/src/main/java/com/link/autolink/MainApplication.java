package com.link.autolink;

import android.app.Application;
import android.graphics.BitmapFactory;
import android.util.Log;
import android.widget.ThemedSpinnerAdapter;

import com.elvishew.xlog.LogConfiguration;
import com.elvishew.xlog.LogLevel;
import com.elvishew.xlog.XLog;
import com.elvishew.xlog.flattener.ClassicFlattener;
import com.elvishew.xlog.printer.AndroidPrinter;
import com.elvishew.xlog.printer.Printer;
import com.elvishew.xlog.printer.file.FilePrinter;
import com.elvishew.xlog.printer.file.clean.FileLastModifiedCleanStrategy;
import com.elvishew.xlog.printer.file.naming.DateFileNameGenerator;
import com.link.autolink.activity.GlobalEventSubscriber;
import com.link.autolink.crash.CrashHandler;
import com.link.autolink.data.local.AppDatabase;
import com.link.autolink.data.local.BrowserInfoEntity;
import com.link.autolink.presentation.ProjectionController;

import java.io.File;
import java.util.List;
import java.util.concurrent.Executors;


public class MainApplication extends Application {

    @Override
    public void onCreate() {
        super.onCreate();
        CrashHandler.getInstance().init();
        initLog();
        initProjection();
        initBrowserHistory();
    }

    private static final String TAG = "MainApplication";

    private void initProjection() {
        GlobalEventSubscriber.INSTANCE.initialize();
        ProjectionControl.INSTANCE.init(this);
        CastWifiP2pManager.INSTANCE.init(this);
        initBrowserHistory();
        ProjectionController.getInstance().initialize(this);
    }

    private void initBrowserHistory() {
        Executors.newSingleThreadExecutor().execute(() -> {
            AppDatabase database = AppDatabase.getInstance(this);
            List<BrowserInfoEntity> list = database.browserInfoDao().getAll();
            Log.d(TAG, "initBrowserHistory:" + list);
            if (list != null && list.isEmpty()) {
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_hulu), "https://www.hulu.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_hulu), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_netflix), "https://www.netflix.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_netflix), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_youtube), "https://www.youtube.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_youtube), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_tiktok), "https://www.tiktok.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_tiktok), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_spotify), "https://accounts.spotify.com", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_spotify), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_douyin), "https://www.douyin.com/?is_from_mobile_home=1", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_tiktok), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_twitch), "https://www.twitch.tv/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_twitch), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_primevideo), "https://www.primevideo.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_primevideo), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_bilibili), "https://www.bilibili.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_bilibili), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_youku), "https://www.youku.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_youku), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_tengxun), "https://v.qq.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_tengxun), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_xigua), "https://www.ixigua.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_xigua), false));
                database.browserInfoDao().insert(new BrowserInfoEntity(getString(R.string.title_aiqiyi), "https://www.iqiyi.com/", BitmapFactory.decodeResource(getResources(), R.drawable.ic_item_aiqiyi), false));
            }
        });
    }

    private void initLog() {
        LogConfiguration config = new LogConfiguration.Builder()
                .logLevel(BuildConfig.DEBUG ? LogLevel.ALL : LogLevel.WARN)
                .tag(getString(R.string.app_name))
                .build();
        Printer androidPrinter = new AndroidPrinter();
        Printer filePrinter =
                new FilePrinter.Builder(new File(getExternalCacheDir(), "Log").getPath())
                        .fileNameGenerator(new DateFileNameGenerator())
                        .cleanStrategy(new FileLastModifiedCleanStrategy(2 * 60 * 60 * 1000))
                        .flattener(new ClassicFlattener())
                        .build();
        XLog.init(config, androidPrinter, filePrinter);
    }
}
