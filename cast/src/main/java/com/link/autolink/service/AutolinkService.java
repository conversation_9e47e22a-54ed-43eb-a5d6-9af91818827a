package com.link.autolink.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.content.res.Configuration;
import android.os.Build;
import android.os.IBinder;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationManagerCompat;

import com.car.autolink.events.ConfigurationChanged;
import com.car.autolink.events.MediaProjectionEvent;
import com.elvishew.xlog.XLog;
import com.link.autolink.R;

import org.greenrobot.eventbus.EventBus;

/**
 * <AUTHOR>
 * @date 2019/11/28
 */
public class AutolinkService extends Service {
    private static final String TAG = "AutolinkService";
    private static final int NOTIFICATION_ID = 0x1234;
    private NotificationManagerCompat mNotificationManager;
    private Notification notification;

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
    }


    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        startForegroundService();
        int resultCode = intent.getIntExtra("resultCode", -1);
        Intent data = intent.getParcelableExtra("data");
        boolean isSecondary = intent.getBooleanExtra("isSecondary", false);
        EventBus.getDefault().post(new MediaProjectionEvent(data, resultCode,isSecondary));
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        XLog.tag(TAG).d("onConfigurationChanged: " + newConfig);
        EventBus.getDefault().post(new ConfigurationChanged(newConfig));
    }


    private void createNotificationChannel() {
        mNotificationManager = NotificationManagerCompat.from(getApplicationContext());
        final String channelOneId = "com.link.autolink";
        final String channelName = "AutolinkService";
        NotificationChannel notificationChannel;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            notificationChannel = new NotificationChannel(channelOneId, channelName,
                    NotificationManager.IMPORTANCE_LOW);
            mNotificationManager.createNotificationChannel(notificationChannel);
        }
        Notification.Builder builder;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            builder = new Notification.Builder(this, channelOneId);
        } else {
            builder = new Notification.Builder(this);
        }
        notification = builder
                .setContentTitle(getString(R.string.app_name))
                .setContentText(getResources().getString(R.string.notification_mirror))
                .setSmallIcon(R.drawable.ic_notify).build();
    }

    @Override
    public void onDestroy() {
        XLog.tag(TAG).d("onDestroy");
        super.onDestroy();
        mNotificationManager.cancelAll();
        stopForeground(true);
    }


    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void startForegroundService() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION);
        } else {
            startForeground(NOTIFICATION_ID, notification);
        }
    }
}
