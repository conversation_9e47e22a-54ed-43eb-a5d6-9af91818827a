<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        html,
        body {
            width: 100%;
            height: 100%;
        }

        h3 {
            text-align: center;
            margin: 10px auto;
        }

        h4 {
            padding: 10px 0;
        }

        .content {
            width: 100%;
            height: 100%;
        }

        .container {
            width: 90%;
            height: auto;
            position: relative;
            margin: 0 auto;
            font-size: '微软雅黑';
        }

        .shb-indent {
            text-indent: 2em;
        }

        ul li {
            list-style: square inside;
        }

        .title4 li {
            text-indent: 2em;
            font-size: 14px;
            line-height: 24px;
        }

        div p {
            font-size: 14px;
            line-height: 24px;
        }


    </style>
</head>

<body>
<div class="content">
    <div class="container">
        <h3>About Autolink Pro</h3>
        <div class="article">
            <p>Autolink support android phone connect to headuint via usb cable or wifi. The phone's
                screen will mirror to headuint when connect successful</p>
        </div>
        <!-- **************Connect Methods***********-->
        <div class="article">
            <h4>How to connect?</h4>
            <p class="shb-indent">1.via USB cable</p>
            <ul class="title4">
                <li>Connect your android phone to headuint via usb cable,autolink will auto start.
                </li>
                <li>At this time,please pay attention to click the message box on your phone</li>
                <li>Plug out usb cable,autolink will disconnect.</li>
            </ul>
            <p class="shb-indent">2.via WIFI</p>
            <ul class="title4">
                <li>Ensure wifi is open,open autolink app,and make sure app is in wifi connection
                    mode
                </li>
                <li>Autolink app will auto search Available Device</li>
                <li>Tap target device to connect</li>
                <li>After connect successful,app will goto background automatically.</li>
                <li>Two methods to disconnect:Tap the connection device name or click disconnect
                    icon on
                    autolink's notification bar.
                </li>
            </ul>
            <p class="shb-indent">3.Note:During the connection process, do not leave connection
                page.</p>
        </div>
        <!-- **************Q&A***********-->
        <div class="article">
            <h4>Can't search wifi device?</h4>
            <p class="shb-indent">Maybe you should open GPS setting</p>
            <h4>Why can't connect successfully?</h4>
            <ul class="title4">
                <li>Please retry with another usb cable</li>
                <li>Please retry after reboot your phone</li>
            </ul>
            <h4>Can't audio output from headuint?</h4>
            <p class="shb-indent">Please check BT connection when atuolink is working.
                Some android phone can't connect BT automatically.Please connect it by yourself.</p>
            <h4>autolink auto disconnect?</h4>
            <p class="shb-indent">Maybe autolink app be killed by phone's system.Please check the
                following settings</p>
            <ul class="title4">
                <li><span style="color:blue;text-decoration: underline"
                          onclick="help.ignoreBatteryOptimization()">add autolink to battery optimization whitelist(click to set)</span>
                </li>
                <li><span style="color:blue;text-decoration: underline"
                          onclick="help.toSelfSetting()">allow autolink run in background(click to set)</span>
                </li>
            </ul>
            <p class="shb-indent">If you can’t find the auto-start permission in the app message information, please set it in the phone’s phone manager.</p>
            <h4>autolink pro back control?</h4>
            <p class="shb-indent">Ensure bluetooth connected as input device,if no,please connect
                bluetooth by yourself</p>
            <p class="shb-indent">FYI:please manual setting if click to set settings failed</p>
            <h4>netflix's video can't mirror?</h4>
            <p class="shb-indent">yes,netflix have video content protection.</p>
        </div>

    </div>
</div>
</body>
</html>