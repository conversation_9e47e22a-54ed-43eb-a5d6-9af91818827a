<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        html,
        body {
            width: 100%;
            height: 100%;
        }

        h3 {
            text-align: center;
            margin: 10px auto;
        }

        h4 {
            padding: 10px 0;
        }

        .content {
            width: 100%;
            height: 100%;
        }

        .container {
            width: 90%;
            height: auto;
            position: relative;
            margin: 0 auto;
            font-size: '微软雅黑';
        }

        .shb-indent {
            text-indent: 2em;
        }

        ul li {
            list-style: square inside;
        }

        .title4 li {
            text-indent: 2em;
            font-size: 14px;
            line-height: 24px;
        }

        div p {
            font-size: 14px;
            line-height: 24px;
        }



    </style>
</head>

<body>
<div class="content">
    <div class="container">
        <h3>关于 Autolink Pro</h3>
        <div class="article">
            <p>Autolink Pro支持使用USB和Wifi连接主机. 连接成功后将把手机的屏幕投屏到主机</p>
        </div>
        <!-- **************Connect Methods***********-->
        <div class="article">
            <h4>连接方式</h4>
            <p class="shb-indent">1.使用USB线</p>
            <ul class="title4">
                <li>使用usb连接手机和主机，Autolink Pro将会自动启动并且投屏.
                </li>
                <li>在连接过程中注意勾选确认投屏的弹框并且不要退出APP</li>
                <li>拔掉USB，投屏将会立即结束</li>
            </ul>
            <p class="shb-indent">2.通过Wifi</p>
            <ul class="title4">
                <li>打开系统Wifi，进入APP，选择Wifi模式</li>
                <li>Autolink Pro将自动搜索可用的Wifi设备</li>
                <li>选择Wifi设备连接</li>
                <li>连接成功后会自动投屏</li>
                <li>两种方式退出投屏:
                    进入APP选择正在投屏的设备，点击触发选择框，选择退出
                    点击通知栏的退出按钮
                </li>
            </ul>
            <p class="shb-indent">3.注意事项:在连接过程中，不要退出APP，不要切换模式</p>
        </div>
        <!-- **************Q&A***********-->
        <div class="article">
            <h4>无法搜索Wifi设备?</h4>
            <p class="shb-indent">可能需要打开位置设置</p>
            <h4>无法连接成功?</h4>
            <ul class="title4">
                <li>请使用其他的USB线尝试</li>
                <li>请重启你的手机尝试</li>
            </ul>
            <h4>音频无法从主机端输出?</h4>
            <p class="shb-indent">请检查蓝牙的连接. 有的手机无法自动连接蓝牙，请手动连接蓝牙</p>
            <h4>Autolink pro 自动断开连接?</h4>
            <p class="shb-indent">有可能APP被系统关闭，请按照如下设定进行尝试</p>
            <ul class="title4">
                <li><span style="color:blue;text-decoration: underline"
                          onclick="help.ignoreBatteryOptimization()">忽略Autolink pro的电池优化</span>
                </li>
                <li><span style="color:blue;text-decoration: underline"
                          onclick="help.toSelfSetting()">设置Autolink pro后台以及自动运行权限</span>
                </li>
            </ul>
            <p class="shb-indent">如果在应用消息信息里面没有找到自启动权限，请到手机的手机管家中设置</p>
            <h4>autolink pro 回控?</h4>
            <p class="shb-indent">确保蓝牙已经连接，并且已连接蓝牙的设置中，输入设备选项也成功连接，如果没有连接，请手动连接</p>
            <p class="shb-indent">如果设置失败，请手动进行设置</p>
            <h4>netflix无法投屏?</h4>
            <p class="shb-indent">因为netflix有内容保护，因此无法投屏</p>
        </div>

    </div>
</div>
</body>
</html>