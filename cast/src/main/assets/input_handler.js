// input_handler.js (Version 2 - Advanced)

(function() {
    'use strict';

    // 防止重复注入
    if (window.inputHandlerInjectedV2) {
        return;
    }
    window.inputHandlerInjectedV2 = true;

    console.log("Input handler script v2 injected.");

    // 核心的事件处理逻辑，与之前相同
    function onFocus(event) {
        const target = event.target;
        if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable)) {
            console.log("Input focused in:", event.currentTarget.host || document.domain, target);
            const type = target.type || 'text';
            const value = target.value || target.textContent;

            if (window.AndroidBridge && window.AndroidBridge.onInputFocused) {
                window.AndroidBridge.onInputFocused(type, value);
            }
        }
    }

    function onBlur(event) {
        const target = event.target;
        if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable)) {
             console.log("Input blurred in:", event.currentTarget.host || document.domain, target);
             if (window.AndroidBridge && window.AndroidBridge.onInputBlurred) {
                window.AndroidBridge.onInputBlurred();
            }
        }
    }

    // 关键函数：递归地为节点及其子孙、Shadow DOM 安装监听器
    function attachListenersToNode(node) {
        if (!node) return;

        // 1. 为当前节点（或 Shadow Root）安装监听器
        node.addEventListener('focusin', onFocus, true);
        node.addEventListener('focusout', onBlur, true);

        // 2. 遍历所有子元素
        const children = node.querySelectorAll('*');
        children.forEach(child => {
            // 3. 如果子元素有 Shadow DOM，递归进入
            if (child.shadowRoot) {
                console.log("Found Shadow DOM, attaching listeners...", child);
                attachListenersToNode(child.shadowRoot);
            }
            // 4. 如果子元素是同域的 iframe，递归进入其文档
            if (child.tagName === 'IFRAME') {
                try {
                    // contentDocument 访问可能会因为跨域而失败
                    if (child.contentDocument) {
                        console.log("Found same-origin iframe, attaching listeners...", child);
                        attachListenersToNode(child.contentDocument);
                    }
                } catch (e) {
                    console.warn("Could not access cross-origin iframe:", child.src, e);
                }
            }
        });
    }

    // 初始加载：为整个文档和已存在的元素安装监听器
    attachListenersToNode(document);

    // 使用 MutationObserver 监听未来动态添加到页面的元素
    const observer = new MutationObserver((mutationsList) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(newNode => {
                    // 只关心元素节点
                    if (newNode.nodeType === Node.ELEMENT_NODE) {
                         console.log("New node added, attaching listeners...", newNode);
                         // 为新添加的节点及其所有子孙安装监听器
                         attachListenersToNode(newNode);
                    }
                });
            }
        }
    });

    // 配置 observer
    const config = { childList: true, subtree: true };

    // 启动 observer，监视整个文档的变化
    observer.observe(document.body, config);

})();