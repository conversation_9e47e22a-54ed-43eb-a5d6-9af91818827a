<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_presentation"
    android:background="@color/presentation_bg_color"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.link.autolink.presentation.widget.BrowserLayout
        android:id="@+id/bl_remote"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="Remote"/>
    <com.link.autolink.presentation.widget.TipBrowserLayout
        android:id="@+id/tip_input_browser_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"/>
    <com.link.autolink.presentation.widget.ToastBrowserLayout
        android:id="@+id/toast_browser_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"/>
    <com.link.autolink.presentation.widget.TipLocalBrowserLayout
        android:id="@+id/tip_local_browser_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <com.link.autolink.presentation.widget.DisconnectLayout
        android:id="@+id/tip_disconnect_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <View
        android:id="@+id/view_point"
        android:background="#00000000"
        android:layout_width="1dp"
        android:layout_height="1dp"/>
</RelativeLayout>