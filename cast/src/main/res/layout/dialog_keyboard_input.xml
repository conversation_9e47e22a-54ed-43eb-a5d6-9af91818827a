<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/keyboard_input_dialog_bg"
    android:padding="16dp"
    android:elevation="8dp">

    <!-- 隐藏的背景overlay用于点击关闭 -->
    <View
        android:id="@+id/background_overlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/text_input_layout"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入内容"
        app:endIconMode="clear_text"
        app:boxBackgroundColor="@android:color/white">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/input_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:imeOptions="actionSend"
            android:inputType="text"
            android:maxLines="1"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:textColor="@android:color/black"
            android:textColorHint="@android:color/darker_gray" />
    </com.google.android.material.textfield.TextInputLayout>

    <Button
        android:id="@+id/send_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginTop="8dp"
        android:text="发送"
        android:textColor="@android:color/white"
        android:backgroundTint="?attr/colorPrimary" />

</LinearLayout>
