<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_horizontal"
    android:orientation="horizontal"
    android:id="@+id/tip_layout"
    android:paddingBottom="20dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:weightSum="5">
    <RelativeLayout
        android:id="@+id/rl_tip"
        android:background="@drawable/icon_shape_tip_bg"
        android:layout_width="0dp"
        android:layout_height="@dimen/browser_naiv_def_siez"
        android:layout_weight="4"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">
        <TextView
            android:textSize="18sp"
            android:textColor="@color/explain_color"
            android:id="@+id/tv_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/virtual_tip_cannot_input_open_on_phone"
            android:maxLines="1"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/tv_tip_confirm"/>
        <TextView
            android:textSize="18sp"
            android:textColor="@color/blue"
            android:gravity="center"
            android:id="@+id/tv_tip_confirm"
            android:background="@drawable/icon_shape_btn_selector"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:text="@string/devices_dialog_confirm"
            android:singleLine="true"
            android:layout_centerVertical="true"
            android:textAllCaps="false"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:layout_alignParentEnd="true"/>
    </RelativeLayout>
</LinearLayout>
