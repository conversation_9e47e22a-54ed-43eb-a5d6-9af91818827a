<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

  <ImageView
      android:id="@+id/icon_iv"
      android:layout_width="80dp"
      android:layout_height="80dp"
      android:layout_marginBottom="128dp"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintHorizontal_bias="0.5"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      app:srcCompat="@mipmap/ic_launcher"
      />
  <TextView
      android:id="@+id/privacy_tv"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:text="@string/privacy_policy"
      android:textColor="@android:color/holo_orange_dark"
      android:textSize="16sp"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@+id/help_tv"
      />
  <TextView
      android:id="@+id/name_tv"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginTop="8dp"
      android:text="@string/app_name"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@+id/icon_iv"
      />
  <TextView
      android:id="@+id/version_tv"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:text="@string/app_version"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@+id/name_tv"
      />
  <TextView
      android:id="@+id/help_tv"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginBottom="72dp"
      android:text="@string/help"
      android:textColor="@android:color/holo_orange_dark"
      android:textSize="16sp"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      />
  <TextView
      android:id="@+id/copyright_tv"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:text="@string/copyright"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@+id/privacy_tv"
      />
  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:text="@string/rights"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@+id/copyright_tv"
      />
</androidx.constraintlayout.widget.ConstraintLayout>