<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_horizontal"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="20dp"
    android:weightSum="5">
    <RelativeLayout
        android:gravity="center"
        android:id="@+id/rl_toast"
        android:background="@drawable/icon_shape_toast_bg"
        android:layout_width="0dp"
        android:layout_height="@dimen/browser_naiv_def_siez"
        android:layout_weight="4"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">
        <TextView
            android:textSize="18sp"
            android:textColor="@color/title_color"
            android:gravity="center"
            android:id="@+id/tv_toast"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"/>
    </RelativeLayout>
</LinearLayout>
