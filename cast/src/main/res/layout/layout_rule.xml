<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:orientation="vertical"
      >

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:padding="10dp"
        android:text="@string/policy_title"
        android:textColor="#212121"
        android:textSize="16sp"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/line"
        />


    <TextView
        android:id="@+id/tv_text"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:lineSpacingMultiplier="1.2"
        android:paddingBottom="20dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="20dp"
        android:text=""
        android:textColor="#56595e"
        android:textSize="14sp"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/line"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        >

      <TextView
          android:id="@+id/tv_cancel"
          android:layout_width="match_parent"
          android:layout_height="match_parent"
          android:layout_gravity="center"
          android:layout_weight="1"
          android:background="@drawable/btn_rule_selector"
          android:gravity="center"
          android:padding="10dp"
          android:text="@string/rule_disagree"
          android:textColor="#212121"
          android:textSize="15sp"
          />

      <TextView
          android:id="@+id/tv_ok"
          android:layout_width="match_parent"
          android:layout_height="match_parent"
          android:layout_gravity="center"
          android:layout_weight="1"
          android:background="@drawable/btn_rule_right_selector"
          android:gravity="center"
          android:padding="10dp"
          android:text="@string/rule_agree"
          android:textColor="#3b81f1"
          android:textSize="15sp"
          />

      <View
          android:layout_width="1px"
          android:layout_height="match_parent"
          android:background="@color/line"
          />
    </LinearLayout>
  </LinearLayout>
</FrameLayout>