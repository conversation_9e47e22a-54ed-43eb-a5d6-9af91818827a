<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:gravity="center"
        android:id="@+id/rl_online_video"
        android:background="@drawable/icon_online_video_selector"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2">
        <TextView
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/title_color"
            android:gravity="center"
            android:id="@+id/tv_online_video"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/projection_online_video"
            android:drawableTop="@drawable/ic_online_video"/>
    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/rl_mirror_projection_screen"
        android:background="@drawable/icon_mirror_projection_screen_selector"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2">
        <ImageButton
            android:id="@+id/imgBtn_mute"
            android:background="@null"
            android:padding="12dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_mute_selector"/>
        <View
            android:id="@+id/view_spot"
            android:layout_width="1dp"
            android:layout_height="1dp"/>
        <TextView
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/title_color"
            android:gravity="center"
            android:id="@+id/tv_mirror_projection_screen"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/projection_mirror_screen"
            android:drawableTop="@drawable/ic_mirror_casting"
            android:layout_centerInParent="true"/>
    </RelativeLayout>
    <LinearLayout
        android:gravity="center"
        android:orientation="vertical"
        android:id="@+id/rl_connect_state"
        android:background="@drawable/icon_connect_state_selector"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1.5">
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true">
            <TextView
                android:textSize="18sp"
                android:textColor="@color/title_color"
                android:gravity="center"
                android:id="@+id/tv_current_device"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/projection_current_no_device"/>
            <TextView
                android:textSize="18sp"
                android:textColor="@color/title_color"
                android:gravity="center"
                android:id="@+id/tv_current_device_name"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:text="@string/projection_current_no_device"/>
        </LinearLayout>
        <Button
            android:textSize="18sp"
            android:textColor="@color/title_color"
            android:gravity="center"
            android:id="@+id/btn_connect_state"
            android:background="@drawable/icon_shape_btn_selector"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:text="@string/projection_add_device"
            android:singleLine="true"
            android:textAllCaps="false"/>
    </LinearLayout>
</merge>
