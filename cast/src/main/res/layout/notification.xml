<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    tools:ignore="ContentDescription"
    >

  <LinearLayout
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:layout_gravity="center_vertical"
      android:layout_weight="1"
      android:orientation="vertical">

    <TextView
        android:id="@+id/notification_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@android:style/TextAppearance.Material.Notification.Title" />

    <TextView
        android:id="@+id/notification_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@android:style/TextAppearance.Material.Notification.Line2" />

  </LinearLayout>

  <ImageButton
      android:id="@+id/notification_exit"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      style="@style/NotificationAction"/>

</LinearLayout>
