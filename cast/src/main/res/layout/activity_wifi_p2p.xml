<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar_wifi_p2p"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:titleTextColor="@color/title_color"/>
    <LinearLayout
        android:id="@+id/ll_permission"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/toolbar_wifi_p2p">
        <TextView
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:id="@+id/tv_permission"
            android:background="@color/blue"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/devices_access_fine_location"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"/>
    </LinearLayout>
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/ll_pair_refresh"
        android:layout_below="@+id/ll_permission">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:textSize="16sp"
                android:textColor="@color/title_color"
                android:gravity="center"
                android:id="@+id/tv_paired"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="@string/devices_paired"
                android:singleLine="true"
                android:layout_marginStart="30dp"/>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_paired"
                android:background="@drawable/icon_recycler_bg"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"/>
            <LinearLayout
                android:gravity="center"
                android:orientation="horizontal"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="16dp"
                android:layout_marginStart="30dp">
                <TextView
                    android:textSize="16sp"
                    android:textColor="@color/title_color"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/devices_available_device"
                    android:singleLine="true"/>
                <ProgressBar
                    android:id="@+id/pb_loading"
                    android:visibility="gone"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:indeterminateTint="@color/title_color"
                    style="?android:attr/progressBarStyle"/>
            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_available"
                android:background="@drawable/icon_recycler_bg"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"/>
            <TextView
                android:textSize="18sp"
                android:textColor="@color/title_color"
                android:gravity="center"
                android:id="@+id/tv_no_available"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:text="@string/devices_click_searching"
                android:singleLine="true"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <LinearLayout
        android:gravity="center"
        android:id="@+id/ll_pair_refresh"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_alignParentBottom="true">
        <Button
            android:textSize="18sp"
            android:textColor="@color/blue"
            android:gravity="center"
            android:id="@+id/btn_pair_refresh"
            android:background="@drawable/icon_shape_btn_selector"
            android:layout_width="172dp"
            android:layout_height="44dp"
            android:text="@string/devices_discover_start"
            android:singleLine="true"/>
    </LinearLayout>
</RelativeLayout>
