<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar_local_browser"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:elevation="8dp"
        app:titleTextColor="@color/title_color"
        style="@style/BaseToolbarStyle">
        <com.link.autolink.presentation.widget.ClearEditText
            android:id="@+id/search_edittext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.appcompat.widget.Toolbar>
    <com.link.autolink.presentation.widget.BrowserLayout
        android:id="@+id/browserLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar_local_browser"
        android:contentDescription="Phone"/>
</RelativeLayout>
