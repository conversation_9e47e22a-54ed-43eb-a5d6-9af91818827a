<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/ic_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:src="@mipmap/ic_launcher"
            android:layout_centerHorizontal="true"/>
        <TextView
            android:textSize="16dp"
            android:textColor="@color/title_color"
            android:id="@+id/tv_app_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_below="@+id/ic_logo"
            android:layout_centerHorizontal="true"/>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_set_info"
            android:background="@drawable/icon_recycler_bg"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:layout_marginBottom="10dp"
            android:layout_below="@+id/tv_app_version"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"/>
    </RelativeLayout>
</ScrollView>
