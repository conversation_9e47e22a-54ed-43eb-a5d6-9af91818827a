<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_margin="4dp"
    >

    <ImageView
        android:id="@+id/wifi_signal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="20dp"
        android:contentDescription="@null"
        tools:src="@drawable/ic_list_unknown"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >

        <TextView
            android:id="@+id/device_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textSize="14sp"
            tools:text="test"
            />

        <TextView
            android:id="@+id/device_details"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textSize="14sp"
            tools:text="details"
            />
    </LinearLayout>

</LinearLayout>