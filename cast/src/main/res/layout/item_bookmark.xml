<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:background="@drawable/icon_shape_bg_selector"
    android:gravity="center"
    android:padding="8dp">

    <RelativeLayout
        android:id="@+id/fl_icon"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:gravity="center">

        <ImageView
            android:src="@drawable/ic_item_def_favicon"
            android:id="@+id/iv_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:scaleType="centerCrop" />
        <CheckBox
            android:id="@+id/checkBox"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:layout_marginStart="-6dp"
            android:layout_marginTop="-6dp"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:visibility="gone" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/fl_icon"
        android:layout_centerHorizontal="true"
        android:lines="1"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/title_color"
        android:textSize="16sp" />
</RelativeLayout>