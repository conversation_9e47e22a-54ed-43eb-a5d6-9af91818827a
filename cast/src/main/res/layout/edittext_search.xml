<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:id="@+id/rl_search"
        android:background="@drawable/search_box_bg"
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_centerVertical="true">
        <EditText
            android:id="@+id/et_search"
            android:background="@android:color/transparent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:hint="@string/online_search_edit_hint"
            android:layout_centerVertical="true"
            android:inputType="textUri"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:layout_toStartOf="@+id/ivBtn_delete"/>
        <ImageButton
            android:id="@+id/ivBtn_delete"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_edit_delete"
            android:layout_centerVertical="true"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_alignParentEnd="true"/>
    </RelativeLayout>
</RelativeLayout>
