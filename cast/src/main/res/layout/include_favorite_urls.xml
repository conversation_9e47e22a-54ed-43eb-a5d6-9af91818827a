<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rl_favorite"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/ll_bottom"
        android:layout_marginStart="@dimen/rv_bookmark_margin"
        android:layout_marginEnd="@dimen/rv_bookmark_margin">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_bookmark"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </RelativeLayout>
    <TextView
        android:gravity="center"
        android:id="@+id/tv_empty_fav_tips"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:text="@string/ctrl_favorite_urls_empty"/>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/ll_bottom"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_alignParentBottom="true">
        <TextView
            android:gravity="center"
            android:id="@+id/tv_delete"
            android:background="?attr/selectableItemBackground"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="@string/online_delete"
            android:layout_weight="1"
            app:drawableTopCompat="@drawable/ic_delete"/>
        <Space
            android:layout_width="8dp"
            android:layout_height="0dp"/>
        <TextView
            android:gravity="center"
            android:id="@+id/tv_all_select"
            android:background="?attr/selectableItemBackground"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:text="@string/ctrl_select_all"
            android:layout_weight="1"
            app:drawableTopCompat="@drawable/icon_all_select_selector"/>
    </LinearLayout>
</RelativeLayout>
