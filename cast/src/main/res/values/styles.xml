<resources>

    <!--
        Base application theme, dependent on API level. This theme is replaced
        by AppBaseTheme from res/values-vXX/styles.xml on newer devices.
    -->

    <!-- Application theme. -->

    <!-- Notification bar actions -->
    <style name="Theme.LyProjectionScreen" parent="@style/Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="colorControlActivated">@color/blue</item>
        <item name="colorControlNormal">@color/black</item>
        <item name="colorPrimary">@color/blue</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="ContainerStyleNoPaddingBottom">
        <item name="android:gravity">top|center_vertical</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">40dp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
    </style>

    <style name="TitleStyle">
        <item name="android:textSize">15sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">clip_horizontal</item>
        <item name="android:fadingEdge">none</item>
      <item name="android:textAllCaps">false</item>
    </style>

  <style name="NotificationAction">
    <item name="android:gravity">center|right</item>
    <item name="android:scaleType">fitCenter</item>
    <item name="android:background">?android:selectableItemBackground</item>
  </style>


  <style name="POLICY_DIALOG" parent="android:style/Theme.Dialog">
    <item name="android:windowBackground">@drawable/btn_rule</item>
    <item name="android:windowFrame">@null</item>
    <!-- 无边框 -->
    <item name="android:windowNoTitle">true</item>
    <!-- 没有标题 -->
    <item name="android:windowIsTranslucent">true</item>
    <!-- 背景是否半透明 -->
  </style>

    <style name="CustomTabLayout" parent="@android:style/TextAppearance.Widget.TabWidget">
        <item name="colorAccent">@color/blue</item>
        <item name="tabIndicatorHeight">0dp</item>
    </style>

    <style name="PresentationDialog" parent="@style/Theme.AppCompat.DayNight.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="colorOnPrimary">@color/blue</item>
        <item name="colorPrimary">@color/blue</item>
        <item name="colorPrimaryVariant">@color/blue</item>
    </style>

    <style name="BaseToolbarStyle" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:paddingLeft">0dp</item>
        <item name="android:paddingRight">0dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">?attr/actionBarSize</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="actionMenuTextColor">#000</item>
        <item name="contentInsetStart">0dp</item>
        <item name="contentInsetStartWithNavigation">0dp</item>
    </style>

    <style name="App.Theme.CustomBottomSheetDialog" parent="Theme.Material3.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/App.Widget.CustomBottomSheet</item>
    </style>

    <style name="App.Widget.CustomBottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="backgroundTint">@android:color/transparent</item>
    </style>

    <!-- 贴键盘输入框Dialog主题 -->
    <style name="KeyboardInputDialogTheme" parent="@style/Theme.MaterialComponents.DayNight.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowSoftInputMode">stateAlwaysVisible</item>
        <item name="android:windowAnimationStyle">@style/KeyboardInputDialogAnimation</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <!-- Dialog动画样式 -->
    <style name="KeyboardInputDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_in_bottom</item>
        <item name="android:windowExitAnimation">@anim/slide_out_bottom</item>
    </style>

</resources>
