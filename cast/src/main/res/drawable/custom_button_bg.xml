<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <gradient
                android:angle="270"
                android:startColor="#FF555555"
                android:endColor="#FF666666"
                android:type="linear" />
            <stroke android:width="1dp" android:color="#FF333333" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <gradient
                android:angle="270"
                android:startColor="#FF888888"
                android:endColor="#FF777777"
                android:type="linear" />
            <stroke android:width="1dp" android:color="#FF555555" />
        </shape>
    </item>

</selector>