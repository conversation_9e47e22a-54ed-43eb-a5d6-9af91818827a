<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <ripple android:color="@color/title_color">
            <item android:drawable="@drawable/ic_ctrl_star_border"/>
        </ripple>
    </item>
    <item android:state_enabled="false">
        <inset android:drawable="@drawable/ic_ctrl_star_border_disable"/>
    </item>
    <item android:state_selected="true">
        <inset android:drawable="@drawable/ic_ctrl_star_border_checked"/>
    </item>
    <item>
        <inset android:drawable="@drawable/ic_ctrl_star_border"/>
    </item>
</selector>
