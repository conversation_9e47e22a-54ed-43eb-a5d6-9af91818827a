import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id("com.google.devtools.ksp")
}

android {
    namespace = "com.link.autolink"

    signingConfigs {
        create("release") {
            storeFile = file("/autolink.jks")
            storePassword = "sunmedia"
            keyAlias = "autolinkpro"
            keyPassword = "sunmedia"
            enableV1Signing = true
            enableV2Signing = true
        }

        getByName("debug") {
            storeFile = file("/autolink.jks")
            storePassword = "sunmedia"
            keyAlias = "autolinkpro"
            keyPassword = "sunmedia"
            enableV1Signing = true
            enableV2Signing = true
        }

        create("beta") {
            storeFile = file("/autolink.jks")
            storePassword = "sunmedia"
            keyAlias = "autolinkpro"
            keyPassword = "sunmedia"
            enableV1Signing = true
            enableV2Signing = true
        }
    }
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "com.link.autolink.pro"
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        versionCode=  libs.versions.autolinkProVersionCode.get().toInt()
        versionName = libs.versions.autolinkProVersionName.get()

        resourceConfigurations += listOf("zh-rCN", "en")
    }

    buildTypes {
        release {
            isDebuggable = false
            isJniDebuggable = false
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
        debug {
            isMinifyEnabled = false
            proguardFiles(
                    getDefaultProguardFile("proguard-android-optimize.txt"),
            "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("debug")
        }
        create("beta") {
            isDebuggable = true
            isMinifyEnabled = false
            signingConfig = signingConfigs.getByName("beta")
            versionNameSuffix = "-beta"
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    applicationVariants.all {
        outputs.all {
            val output = this as com.android.build.gradle.internal.api.BaseVariantOutputImpl
            val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss")
            val buildTime = dateFormat.format(Date())
            output.outputFileName = "Cast_${defaultConfig.versionName}_${buildType.name}_$buildTime.apk"
        }
    }
    buildFeatures {
        viewBinding = true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
}

tasks.register("copyReleaseApk", Copy::class) {
    val releaseApkDir = File("${buildDir}/outputs/apk/release")
    val destinationDir = File(rootProject.projectDir, "apps_store/release/autolink_pro")

    from(releaseApkDir) {
        include("*.apk")
    }
    into(destinationDir)

    doFirst {
        if (!destinationDir.exists()) {
            destinationDir.mkdirs()
        }

        val calendar = Calendar.getInstance()
        calendar.add(Calendar.YEAR, -2)
        val twoYearsAgo = calendar.time

        // 删除超过两年的 APK 文件
        destinationDir.listFiles { file ->
            file.extension == "apk" && file.lastModified() < twoYearsAgo.time
        }?.forEach { file ->
            println("Deleting old APK: ${file.name}")
            file.delete()
        }
    }
}

tasks.whenTaskAdded {
    if (name == "assembleRelease") {
        finalizedBy("copyReleaseApk")
    }
}

dependencies {
    implementation(project(":carlink"))
    implementation(libs.androidx.constraintlayout)
    implementation(libs.xxpermissions)
    implementation(libs.androidx.localbroadcastmanager)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.recyclerview)
    implementation(libs.androidx.viewpager2)
    implementation(libs.material)
    implementation(libs.androidx.activity)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.room.runtime)
    ksp(libs.androidx.room.compiler)
    implementation(libs.androidx.room.ktx)
    implementation(libs.glide)
}
